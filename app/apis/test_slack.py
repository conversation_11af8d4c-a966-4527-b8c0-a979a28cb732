from fastapi import Request
from app import router, app
from app.common import DefaultResponseSchema
from app.common.slack import send_slack_message


__all__ = ['test_slack', 'check_slack_config']


@router.get("/test-slack/config")
async def check_slack_config(request: Request):
    """Check Slack configuration without sending a message"""
    token = app.config.SLACK_BOT_TOKEN
    channel = app.config.SLACK_CHANNEL_ID

    return {
        "ok": True,
        "slack_bot_token_set": bool(token),
        "slack_bot_token_format": token[:10] + "..." if token else None,
        "slack_channel_id_set": bool(channel),
        "slack_channel_id": channel,
        "message": "Slack configuration check complete"
    }


@router.post("/test-slack", response_model=DefaultResponseSchema)
async def test_slack(request: Request):
    """Test endpoint to verify Slack integration"""
    try:
        await send_slack_message("Test message from CloudAudit API")
        return DefaultResponseSchema(
            ok=True,
            status_code=200,
            message="Slack message sent successfully"
        )
    except Exception as e:
        return DefaultResponseSchema(
            ok=False,
            status_code=500,
            message=f"Slack notification failed: {str(e)}"
        )
