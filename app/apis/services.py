from fastapi import Request
from app import router
from app.common import GET_SERVICES_API, GetServicesResponseSchema, requires_permission, UserPermissionEnum
from ..core import CloudProviderServiceProcessor


__all__ = ['fetch_services']


@router.get(GET_SERVICES_API, response_model=GetServicesResponseSchema)
async def fetch_services(request: Request,
                         user: dict = requires_permission(UserPermissionEnum.READ_ONLY.value)):
    cloud_provider_id = request.query_params.get('cloud_provider_id')
    service = CloudProviderServiceProcessor(cloud_provider_id)
    response = await service.process()

    return GetServicesResponseSchema(
        ok=True,
        status_code=200,
        data=response
    )
