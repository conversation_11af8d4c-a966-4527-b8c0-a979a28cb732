from fastapi import Request
from app import router
from app.common import (ACCOUNTS_API, DefaultResponseSchema, AddAccountSchema,
                        GetAccountsResponseSchema, DELETE_ACCOUNT_API, requires_permission,
                        UserPermissionEnum, GET_ACCOUNT_DETAIL_API, AccountDetailResponseSchema)
from ..core import AddAccountService, GetAccountsService, DeleteAccountService, GetAccountDetailService


__all__ = ['add_account', 'get_accounts', 'delete_account', 'get_account_detail']


@router.post(ACCOUNTS_API, response_model=DefaultResponseSchema)
async def add_account(request: Request, payload: AddAccountSchema,
                      user: dict = requires_permission(UserPermissionEnum.CREATE_ACCOUNT.value)):
    service = AddAccountService(payload, user)
    await service.process()

    return DefaultResponseSchema(
        ok=True,
        status_code=200,
        message="Account added successfully"
    )


@router.get(ACCOUNTS_API, response_model=GetAccountsResponseSchema)
async def get_accounts(request: Request,
                       user: dict = requires_permission(UserPermissionEnum.READ_ONLY.value)):
    cloud_provider_id = request.query_params.get('cloud_provider_id')
    workspace_id = user.get('workspace_id')
    service = GetAccountsService(cloud_provider_id, workspace_id)
    response = await service.process()

    return GetAccountsResponseSchema(
        ok=True,
        status_code=200,
        data=response
    )


@router.get(GET_ACCOUNT_DETAIL_API, response_model=AccountDetailResponseSchema)
async def get_account_detail(request: Request, account_id: int,
                            user: dict = requires_permission(UserPermissionEnum.READ_ONLY.value)):
    service = GetAccountDetailService(account_id, user)
    response = await service.process()

    return AccountDetailResponseSchema(
        ok=True,
        status_code=200,
        data=response
    )


@router.delete(DELETE_ACCOUNT_API, response_model=DefaultResponseSchema)
async def delete_account(request: Request,
                         user: dict = requires_permission(UserPermissionEnum.DELETE_ACCOUNT.value)):
    account_id = request.path_params.get('account_id')
    service = DeleteAccountService(account_id, user)
    await service.process()

    return DefaultResponseSchema(
        ok=True,
        status_code=200,
        message="Account deleted successfully"
    )
