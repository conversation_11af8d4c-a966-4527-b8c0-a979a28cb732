from fastapi import Request
from app import router
from app.common import REFRESH_TOKEN_API, RefreshTokenSchema, LoginResponseSchema, AUTH_TOKEN_TYPE
from app.core.services.auth import RefreshTokenService


__all__ = ['refresh_token']


@router.post(REFRESH_TOKEN_API, response_model=LoginResponseSchema)
async def refresh_token(request: Request, payload: RefreshTokenSchema):
    service = RefreshTokenService(payload)
    response = await service.process()
    return LoginResponseSchema(
        ok=True,
        status_code=200,
        token_type=AUTH_TOKEN_TYPE,
        access_token=response.get("access_token"),
        refresh_token=response.get("refresh_token"),
        message="New access token generated successfully"
    )
