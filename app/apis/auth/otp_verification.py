from fastapi import Request, Response
from app import router
from app.common import (VERIFY_OTP_API, RESEND_OTP_API, OTPVerificationSchema, ResendOTPSchema, OTPResponseSchema,
                        OTPExpiredException, OTPNotFoundException, OTPAlreadyVerifiedException,
                        OTPResendCooldownException)
from app.core.services.auth import OTPVerificationService, ResendOTPService

__all__ = ['verify_otp', 'resend_otp']


@router.post(VERIFY_OTP_API, response_model=OTPResponseSchema)
async def verify_otp(request: Request, response: Response, payload: OTPVerificationSchema):
    """
    Verify OTP code and complete user registration

    This endpoint validates the OTP code sent to the user's email and creates
    the user account if the OTP is valid and not expired.
    """
    try:
        service = OTPVerificationService(payload)
        result = await service.process()
        
        return OTPResponseSchema(
            ok=True,
            status_code=200,
            message=result["message"],
            data={"email": result["email"]}
        )
    
    except OTPExpiredException:
        response.status_code = 400
        return OTPResponseSchema(
            ok=False,
            status_code=400,
            message="OTP code has expired. Please request a new one.",
            data={"error_type": "otp_expired"}
        )

    except OTPNotFoundException:
        response.status_code = 400
        return OTPResponseSchema(
            ok=False,
            status_code=400,
            message="Invalid or expired OTP code.",
            data={"error_type": "otp_not_found"}
        )

    except OTPAlreadyVerifiedException:
        response.status_code = 400
        return OTPResponseSchema(
            ok=False,
            status_code=400,
            message="OTP code has already been used.",
            data={"error_type": "otp_already_verified"}
        )

    except Exception as e:
        response.status_code = 500
        return OTPResponseSchema(
            ok=False,
            status_code=500,
            message="Account creation failed. Please try again.",
            data={"error_type": "internal_error"}
        )


@router.post(RESEND_OTP_API, response_model=OTPResponseSchema)
async def resend_otp(request: Request, response: Response, payload: ResendOTPSchema):
    """
    Resend OTP code to user's email
    
    This endpoint allows users to request a new OTP code if the previous one
    has expired or was not received. Rate limiting is applied to prevent abuse.
    """
    try:
        service = ResendOTPService(payload)
        result = await service.process()
        
        return OTPResponseSchema(
            ok=True,
            status_code=200,
            message=result["message"],
            data={
                "email": result["email"],
                "expires_in_minutes": result["expires_in_minutes"]
            }
        )
    
    except OTPResendCooldownException as e:
        response.status_code = 429
        return OTPResponseSchema(
            ok=False,
            status_code=429,
            message=str(e),
            data={"error_type": "resend_cooldown"}
        )

    except OTPNotFoundException:
        response.status_code = 404
        return OTPResponseSchema(
            ok=False,
            status_code=404,
            message="No pending verification found for this email.",
            data={"error_type": "no_pending_otp"}
        )

    except Exception as e:
        response.status_code = 500
        return OTPResponseSchema(
            ok=False,
            status_code=500,
            message="Failed to resend verification code. Please try again.",
            data={"error_type": "internal_error"}
        )
