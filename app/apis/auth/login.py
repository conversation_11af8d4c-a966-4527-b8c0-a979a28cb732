from fastapi import Request
from app import router
from app.common import LOGIN_API, SignUpLoginSchema, LoginResponseSchema, AUTH_TOKEN_TYPE
from app.core.services.auth import LoginService


__all__ = ['login']


@router.post(LOGIN_API, response_model=LoginResponseSchema)
async def login(request: Request, payload: SignUpLoginSchema):
    service = LoginService(payload)
    response = await service.process()
    return LoginResponseSchema(
        ok=True,
        status_code=200,
        token_type=AUTH_TOKEN_TYPE,
        access_token=response.get("access_token"),
        refresh_token=response.get("refresh_token"),
        message="User logged in successfully"
    )
