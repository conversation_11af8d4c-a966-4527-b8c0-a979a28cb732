from fastapi import Request, Response
from app import router
from app.common import (SIGNUP_API, SignUpLoginSchema, DefaultResponseSchema,
                        UserExistsException, WorkspaceNameRequiredException, InternalServerException)
from app.core.services.auth import SignUpService


__all__ = ['signup']


@router.post(SIGNUP_API, response_model=DefaultResponseSchema)
async def signup(request: Request, response: Response, payload: SignUpLoginSchema):
    """
    Initiate user signup by sending OTP to email

    This endpoint validates the signup data and sends an OTP to the user's email
    for verification. The user account is not created until the OTP is verified.
    """
    try:
        service = SignUpService(payload)
        result = await service.process()

        return DefaultResponseSchema(
            ok=True,
            status_code=200,
            message=result["message"],
            data={
                "email": result["email"],
                "expires_in_minutes": result["expires_in_minutes"]
            }
        )

    except UserExistsException:
        response.status_code = 400
        return DefaultResponseSchema(
            ok=False,
            status_code=400,
            message="User with this email already exists"
        )

    except WorkspaceNameRequiredException:
        response.status_code = 400
        return DefaultResponseSchema(
            ok=False,
            status_code=400,
            message="Workspace name is required"
        )

    except InternalServerException as e:
        response.status_code = 500
        return DefaultResponseSchema(
            ok=False,
            status_code=500,
            message=str(e) if str(e) else "Internal server error occurred"
        )

    except Exception as e:
        response.status_code = 500
        return DefaultResponseSchema(
            ok=False,
            status_code=500,
            message="An unexpected error occurred. Please try again."
        )
