from fastapi import Request
from app import router
from app.common import (GET_FINDINGS_API, GetFindingsResponseSchema, requires_permission,
                       UserPermissionEnum, GET_FINDING_DETAIL_API, FindingDetailResponseSchema,
                       REMEDIATE_FINDING_API, RemediateFindingSchema,
                       RemediateFindingResponseSchema)
from ..core import GetFindingsService, GetFindingDetailService, RemediateFindingService


__all__ = ["get_findings", "get_finding_detail", "remediate_finding"]


@router.get(GET_FINDINGS_API, response_model=GetFindingsResponseSchema)
async def get_findings(
    request: Request,
    scan_id: int,
    service_id: int = None,
    status: str = None,
    severity: str = None,
    page: int = 1,
    page_size: int = 20,
    user: dict = requires_permission(UserPermissionEnum.READ_ONLY.value),
):
    service = GetFindingsService(user, scan_id, service_id, status, severity, page, page_size)
    response = await service.process()

    return GetFindingsResponseSchema(ok=True, status_code=200, data=response["data"], pagination=response["pagination"])


@router.get(GET_FINDING_DETAIL_API, response_model=FindingDetailResponseSchema)
async def get_finding_detail(
    request: Request,
    finding_id: int,
    user: dict = requires_permission(UserPermissionEnum.READ_ONLY.value),
):
    service = GetFindingDetailService(user, finding_id)
    response = await service.process()

    # Ensure the response conforms to the schema
    # This handles cases where the details field might be a list instead of a dict
    if isinstance(response.get("details"), list):
        # If details is a list, we keep it as is
        pass
    elif not isinstance(response.get("details"), dict):
        # If details is neither a list nor a dict, set it to an empty dict
        response["details"] = {}

    return FindingDetailResponseSchema(ok=True, status_code=200, data=response)


@router.post(REMEDIATE_FINDING_API, response_model=RemediateFindingResponseSchema)
async def remediate_finding(
    request: Request,
    finding_id: int,
    payload: RemediateFindingSchema,
    user: dict = requires_permission(UserPermissionEnum.REMEDIATE_FINDINGS.value),
):
    service = RemediateFindingService(user, finding_id, payload.details)
    await service.process()

    return RemediateFindingResponseSchema(
        ok=True,
        status_code=200,
        message="Finding remediation initiated successfully"
    )
