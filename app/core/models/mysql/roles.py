from .helper import insert, insert_many, fetch_row, fetch_rows, update_row, delete_rows, delete
from ...models import sql_scripts
from app.common import get_utc_timestamp

__all__ = ['add_custom_role', 'add_custom_role_permissions', 'add_user_custom_role', 'get_custom_role',
           'get_custom_role_permissions', 'update_custom_role_name', 'delete_custom_role_permissions',
           'delete_custom_role', 'get_workspace_custom_roles', 'get_roles', 'add_user_role',
           'remove_user_role', 'get_roles_of_user', 'get_custom_roles_of_user']


async def add_custom_role(conn_pool, name, workspace_id):
    """Add custom role to custom_roles table"""
    result = await insert(conn_pool, sql_stmt=sql_scripts['add_custom_role'],
                          params={"name": name, "workspace_id": workspace_id,
                                  "created_at": get_utc_timestamp()})
    return result['id']


async def add_custom_role_permissions(conn_pool, custom_role_id, permissions):
    """Add custom role permissions to custom_role_permissions table"""
    return await insert_many(conn_pool, sql_stmt=sql_scripts['add_custom_role_permissions'],
                             params=[{"custom_role_id": custom_role_id,
                                      "permission_id": permission['id']} for permission in permissions])


async def add_user_custom_role(conn_pool, user_id, custom_role_id):
    """Add user custom role to user_custom_roles table"""
    return await insert(conn_pool, sql_stmt=sql_scripts['add_user_custom_role'],
                        params={"user_id": user_id, "custom_role_id": custom_role_id})


async def get_custom_role(conn_pool, custom_role_id):
    """Get custom role by id"""
    return await fetch_row(conn_pool, sql_stmt=sql_scripts['get_custom_role'], params={"id": custom_role_id})


async def get_custom_role_permissions(conn_pool, custom_role_id):
    """Get custom role permissions by id"""
    result = await fetch_rows(conn_pool, sql_stmt=sql_scripts['get_custom_role_permissions'],
                              params={"custom_role_id": custom_role_id})
    return {item["permission_id"] for item in result} if result else set()


async def update_custom_role_name(conn_pool, custom_role_id, name):
    """Update custom role by id"""
    return await update_row(conn_pool, sql_stmt=sql_scripts['update_custom_role_name'],
                            params={"id": custom_role_id, "name": name})


async def delete_custom_role_permissions(conn_pool, custom_role_id, permissions):
    """Delete custom role permissions by id"""
    return await delete_rows(conn_pool, sql_stmt=sql_scripts['delete_custom_role_permissions'],
                             params=[{"custom_role_id": custom_role_id, "permission_id": permission['id']} for permission in permissions])


async def delete_custom_role(conn_pool, custom_role_id):
    """Delete custom role by id"""
    return await delete(conn_pool, sql_stmt=sql_scripts['delete_custom_role'], params={"id": custom_role_id})


async def get_workspace_custom_roles(conn_pool, workspace_id):
    """Get all custom roles by workspace id"""
    return await fetch_rows(conn_pool, sql_stmt=sql_scripts['get_workspace_custom_roles'],
                            params={"workspace_id": workspace_id})


async def get_roles(conn_pool):
    """Get all roles by workspace id"""
    return await fetch_rows(conn_pool, sql_stmt=sql_scripts['get_roles'])


async def add_user_role(conn_pool, user_id, role_id, is_custom_role=False):
    """Insert user role into user_roles/user_custom_roles table"""
    params = {"user_id": user_id}
    params.update({"custom_role_id": role_id} if is_custom_role else {"role_id": role_id})
    sql_stmt = "add_user_custom_role" if is_custom_role else "add_user_role"
    return await insert(conn_pool, sql_stmt=sql_scripts[sql_stmt], params=params)


async def remove_user_role(conn_pool, user_id, role_id, is_custom_role):
    """Delete user role from user_roles/user_custom_roles table"""
    params = {"user_id": user_id}
    params.update({"custom_role_id": role_id} if is_custom_role else {"role_id": role_id})
    sql_stmt = "remove_user_custom_role" if is_custom_role else "remove_user_role"
    return await delete(conn_pool, sql_stmt=sql_scripts[sql_stmt], params=params)


async def get_roles_of_user(conn_pool, user_id):
    """Get all roles of a user"""
    return await fetch_rows(conn_pool, sql_stmt=sql_scripts['get_roles_of_user'], params={"user_id": user_id})


async def get_custom_roles_of_user(conn_pool, user_id):
    """Get all custom roles of a user"""
    return await fetch_rows(conn_pool, sql_stmt=sql_scripts['get_custom_roles_of_user'], params={"user_id": user_id})
