from .helper import fetch_rows, fetch_row
from ...models import sql_scripts

__all__ = ['get_cloud_providers', 'get_cloud_provider_by_id']


async def get_cloud_providers(conn_pool):
    """Fetch data from cloud providers table"""
    return await fetch_rows(conn_pool, sql_stmt=sql_scripts['get_cloud_providers'])


async def get_cloud_provider_by_id(conn_pool, cloud_provider_id):
    """Fetch cloud provider data by cloud provider id"""
    return await fetch_row(conn_pool, sql_stmt=sql_scripts['get_cloud_provider_by_id'],
                           params={"cloud_provider_id": cloud_provider_id})
