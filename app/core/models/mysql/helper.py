import aiomysql


__all__ = ['fetch_row', 'fetch_rows', 'insert', 'insert_many', 'insert_row', 'update_row', 'delete', 'delete_rows']


async def fetch_row(conn_pool, sql_stmt, params=None):
    try:
        async with conn_pool.acquire() as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                if params:
                    await cursor.execute(sql_stmt, params)
                else:
                    await cursor.execute(sql_stmt)
                result = await cursor.fetchone()
                return result
    except RuntimeError as e:
        if "attached to a different loop" in str(e):
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Event loop error in fetch_row: {e}")
            raise RuntimeError(f"Event loop error in fetch_row: {e}")
        else:
            raise


async def fetch_rows(conn_pool, sql_stmt, params=None):
    try:
        async with conn_pool.acquire() as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                if params:
                    await cursor.execute(sql_stmt, params)
                else:
                    await cursor.execute(sql_stmt)
                result = await cursor.fetchall()
                return result
    except RuntimeError as e:
        if "attached to a different loop" in str(e):
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Event loop error in fetch_rows: {e}")
            raise RuntimeError(f"Event loop error in fetch_rows: {e}")
        else:
            raise


async def insert(conn_pool, sql_stmt, params):
    import asyncio
    import random

    max_retries = 3
    base_delay = 0.1  # 100ms base delay

    for attempt in range(max_retries + 1):
        try:
            async with conn_pool.acquire() as connection:
                async with connection.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(sql_stmt, params)
                    result = {
                        "id": cursor.lastrowid,
                        "row_count": cursor.rowcount
                    }
                    await connection.commit()
                return result
        except Exception as e:
            # Check if it's a RuntimeError related to event loop issues
            if isinstance(e, RuntimeError) and "attached to a different loop" in str(e):
                # This is the specific error we're trying to fix
                # Log it and re-raise with more context
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Event loop error in insert (attempt {attempt + 1}): {e}")
                if attempt < max_retries:
                    # Short delay before retry
                    await asyncio.sleep(0.1)
                    continue
                else:
                    raise RuntimeError(f"Event loop error persisted after {max_retries + 1} attempts: {e}")
            # Check if it's a deadlock error (MySQL error code 1213) or too many connections (1040)
            elif hasattr(e, 'args') and len(e.args) >= 2 and e.args[0] in (1213, 1040):
                if attempt < max_retries:
                    # Exponential backoff with jitter to avoid thundering herd
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 0.1)
                    await asyncio.sleep(delay)
                    continue
                else:
                    # Final attempt failed, re-raise the error
                    raise
            else:
                # Not a retryable error, re-raise immediately
                raise


async def insert_many(conn_pool, sql_stmt, params):
    async with conn_pool.acquire() as connection:
        async with connection.cursor(aiomysql.DictCursor) as cursor:
            await cursor.executemany(sql_stmt, params)
            result = {
                "id": cursor.lastrowid,
                "row_count": cursor.rowcount
            }
            await connection.commit()
        return result


async def insert_row(conn, sql_stmt, params):
    result = {
        "insert_id": None,
        "rowcount": None
    }
    async with conn.cursor() as cursor:
        await cursor.execute(sql_stmt, params)
        result['insert_id'] = cursor.lastrowid
        result['rowcount'] = cursor.rowcount
    return result


async def update_row(conn_pool, sql_stmt, params):
    import asyncio
    import random

    max_retries = 3
    base_delay = 0.1  # 100ms base delay

    for attempt in range(max_retries + 1):
        try:
            async with conn_pool.acquire() as connection:
                async with connection.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(sql_stmt, params)
                    result = {
                        "id": cursor.lastrowid,
                        "row_count": cursor.rowcount
                    }
                    await connection.commit()
                return result
        except Exception as e:
            # Check if it's a RuntimeError related to event loop issues
            if isinstance(e, RuntimeError) and "attached to a different loop" in str(e):
                # This is the specific error we're trying to fix
                # Log it and re-raise with more context
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Event loop error in update_row (attempt {attempt + 1}): {e}")
                if attempt < max_retries:
                    # Short delay before retry
                    await asyncio.sleep(0.1)
                    continue
                else:
                    raise RuntimeError(f"Event loop error persisted after {max_retries + 1} attempts: {e}")
            # Check if it's a deadlock error (MySQL error code 1213) or too many connections (1040)
            elif hasattr(e, 'args') and len(e.args) >= 2 and e.args[0] in (1213, 1040):
                if attempt < max_retries:
                    # Exponential backoff with jitter to avoid thundering herd
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 0.1)
                    await asyncio.sleep(delay)
                    continue
                else:
                    # Final attempt failed, re-raise the error
                    raise
            else:
                # Not a retryable error, re-raise immediately
                raise


async def delete(conn_pool, sql_stmt, params):
    import asyncio
    import random

    max_retries = 3
    base_delay = 0.1  # 100ms base delay

    for attempt in range(max_retries + 1):
        try:
            async with conn_pool.acquire() as connection:
                async with connection.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(sql_stmt, params)
                    result = {
                        "id": cursor.lastrowid,
                        "row_count": cursor.rowcount
                    }
                    await connection.commit()
                return result
        except Exception as e:
            # Check if it's a RuntimeError related to event loop issues
            if isinstance(e, RuntimeError) and "attached to a different loop" in str(e):
                # This is the specific error we're trying to fix
                # Log it and re-raise with more context
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Event loop error in delete (attempt {attempt + 1}): {e}")
                if attempt < max_retries:
                    # Short delay before retry
                    await asyncio.sleep(0.1)
                    continue
                else:
                    raise RuntimeError(f"Event loop error persisted after {max_retries + 1} attempts: {e}")
            # Check if it's a deadlock error (MySQL error code 1213) or too many connections (1040)
            elif hasattr(e, 'args') and len(e.args) >= 2 and e.args[0] in (1213, 1040):
                if attempt < max_retries:
                    # Exponential backoff with jitter to avoid thundering herd
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 0.1)
                    await asyncio.sleep(delay)
                    continue
                else:
                    # Final attempt failed, re-raise the error
                    raise
            else:
                # Not a retryable error, re-raise immediately
                raise


async def delete_rows(conn_pool, sql_stmt, params):
    async with conn_pool.acquire() as connection:
        async with connection.cursor(aiomysql.DictCursor) as cursor:
            await cursor.executemany(sql_stmt, params)
            result = {
                "id": cursor.lastrowid,
                "row_count": cursor.rowcount
            }
            await connection.commit()
        return result
