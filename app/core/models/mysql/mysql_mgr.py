import aiomysql
import asyncio
import threading

from app import app

__all__ = ["mysql_connection_pool_factory", "get_shared_mysql_pool", "cleanup_mysql_pool_for_current_loop"]

# Global shared connection pool for Celery tasks - keyed by event loop
_shared_mysql_pools = {}
_pool_lock = threading.Lock()


async def mysql_connection_pool_factory(**kwargs):
    """ This method is used to create MySQL pool creation """

    minsize = kwargs.get('minsize', app.config.MYSQL_CONNECTION_POOL_MINIMUM_SIZE)
    maxsize = kwargs.get('maxsize', app.config.MYSQL_CONNECTION_POOL_MAXIMUM_SIZE)
    host = kwargs.get('host', app.config.MYSQL_DATABASE_HOST)
    user = kwargs.get('user', app.config.MYSQL_DATABASE_USER)
    password = kwargs.get('password', app.config.MYSQL_DATABASE_PASSWORD)
    db = kwargs.get('db', app.config.MYSQL_DATABASE_NAME)
    pool_recycle = kwargs.get('pool_recycle', app.config.MYSQL_CONNECTION_MAX_POOL_RECYCLE_TIME)

    return await aiomysql.create_pool(
        minsize=minsize,
        maxsize=maxsize,
        host=host,
        user=user,
        password=password,
        db=db,
        pool_recycle=pool_recycle,
        cursorclass=aiomysql.DictCursor,
        autocommit=True,
    )


async def get_shared_mysql_pool():
    """
    Get or create a shared MySQL connection pool for Celery tasks.
    This prevents each task from creating its own pool and exhausting connections.
    Each event loop gets its own pool to avoid "attached to a different loop" errors.
    """
    global _shared_mysql_pools

    # Get the current event loop
    try:
        current_loop = asyncio.get_running_loop()
        loop_id = id(current_loop)
    except RuntimeError:
        # No running loop, create a new one
        current_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(current_loop)
        loop_id = id(current_loop)

    with _pool_lock:
        # Check if we have a pool for this event loop
        if loop_id not in _shared_mysql_pools or _shared_mysql_pools[loop_id].closed:
            # Create a larger shared pool for concurrent Celery tasks
            # Increase pool size to handle more concurrent tasks
            try:
                _shared_mysql_pools[loop_id] = await mysql_connection_pool_factory(
                    minsize=5,  # Reasonable minimum
                    maxsize=20,  # Reasonable maximum per event loop
                )
            except Exception as e:
                # If pool creation fails, remove the entry and re-raise
                if loop_id in _shared_mysql_pools:
                    del _shared_mysql_pools[loop_id]
                raise e

    return _shared_mysql_pools[loop_id]


async def cleanup_mysql_pool_for_current_loop():
    """
    Clean up the MySQL connection pool for the current event loop.
    Should be called when the event loop is about to be closed.
    """
    global _shared_mysql_pools

    try:
        current_loop = asyncio.get_running_loop()
        loop_id = id(current_loop)

        with _pool_lock:
            if loop_id in _shared_mysql_pools:
                pool = _shared_mysql_pools[loop_id]
                if not pool.closed:
                    pool.close()
                    await pool.wait_closed()
                del _shared_mysql_pools[loop_id]
    except RuntimeError:
        # No running loop, nothing to clean up
        pass
