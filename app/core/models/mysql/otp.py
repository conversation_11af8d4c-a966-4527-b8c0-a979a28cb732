from .helper import fetch_row, insert, update_row, delete
from ...models import sql_scripts

__all__ = ['store_otp', 'get_otp', 'verify_otp', 'get_otp_by_email', 'cleanup_expired_otps', 'get_verified_otp']


async def store_otp(conn_pool, email, otp_code, expires_at, signup_data):
    """
    Store OTP in the database
    Uses INSERT ... ON DUPLICATE KEY UPDATE to replace existing OTP for the same email
    """
    return await insert(
        conn_pool,
        sql_stmt=sql_scripts['store_otp'],
        params={
            "email": email,
            "otp_code": otp_code,
            "expires_at": expires_at,
            "signup_data": signup_data
        }
    )


async def get_otp(conn_pool, email, otp_code):
    """
    Get OTP record by email and OTP code
    Only returns non-expired, non-verified OTPs
    """
    return await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts['get_otp'],
        params={
            "email": email,
            "otp_code": otp_code
        }
    )


async def verify_otp(conn_pool, email, otp_code):
    """
    Mark OTP as verified
    """
    return await update_row(
        conn_pool,
        sql_stmt=sql_scripts['verify_otp'],
        params={
            "email": email,
            "otp_code": otp_code
        }
    )


async def get_otp_by_email(conn_pool, email):
    """
    Get the most recent OTP record by email
    Only returns non-expired, non-verified OTPs
    """
    return await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts['get_otp_by_email'],
        params={"email": email}
    )


async def cleanup_expired_otps(conn_pool):
    """
    Clean up expired OTP records and verified OTPs older than 1 day
    """
    return await delete(
        conn_pool,
        sql_stmt=sql_scripts['cleanup_expired_otps'],
        params={}
    )


async def get_verified_otp(conn_pool, email):
    """
    Get the most recent verified OTP record by email
    Used for retrieving signup data after successful verification
    """
    return await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts['get_verified_otp'],
        params={"email": email}
    )
