from .helper import fetch_row, fetch_rows
from ...models import sql_scripts
import json

__all__ = ["get_scan_access", "get_findings_for_scan", "get_finding_detail"]


async def get_scan_access(conn_pool, scan_id, user_id):
    """Check if user has access to the scan"""
    result = await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts["check_scan_access"],
        params={"scan_id": scan_id, "user_id": user_id},
    )
    return result is not None and result.get("has_access", 0) == 1


async def get_findings_for_scan(conn_pool, scan_id, service_id=None, status=None, severity=None, page=1, page_size=20):
    """Get findings for a scan with pagination and filters - without details field for better performance"""
    # Build the WHERE clause based on filters
    where_clauses = ["f.scan_id = %(scan_id)s"]
    params = {"scan_id": scan_id, "offset": (page - 1) * page_size, "limit": page_size}

    if service_id:
        where_clauses.append("f.service_id = %(service_id)s")
        params["service_id"] = service_id

    if status:
        where_clauses.append("f.status = %(status)s")
        params["status"] = status

    if severity:
        where_clauses.append("f.severity = %(severity)s")
        params["severity"] = severity

    where_clause = " AND ".join(where_clauses)

    count_result = await fetch_row(conn_pool, sql_stmt=sql_scripts["get_findings_count"].replace("#where_clause#", where_clause), params=params)
    total = count_result.get("total", 0) if count_result else 0

    findings = await fetch_rows(conn_pool, sql_stmt=sql_scripts["get_findings"].replace("#where_clause#", where_clause), params=params)

    return {"findings": findings, "total": total}


async def get_finding_detail(conn_pool, finding_id, user_id):
    """Get detailed information about a specific finding with joined data from related tables"""
    result = await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts["get_finding_detail"],
        params={"finding_id": finding_id, "user_id": user_id}
    )

    if result and 'details' in result and result['details']:
        # Parse the JSON details field
        try:
            result['details'] = json.loads(result['details'])
        except:
            # If JSON parsing fails, set to empty dict
            result['details'] = []

    return result
