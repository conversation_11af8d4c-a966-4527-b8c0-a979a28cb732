from .helper import insert, fetch_row, delete
from ...models import sql_scripts

__all__ = ['replace_refresh_token', 'get_user_refresh_token', 'update_refresh_token', 'delete_refresh_token']


async def replace_refresh_token(conn_pool, user_id, refresh_token, expires_at):
    """Replace refresh token in refresh_tokens table"""
    return await insert(conn_pool, sql_stmt=sql_scripts['replace_refresh_token'],
                        params={"user_id": user_id, "refresh_token": refresh_token, "expires_at": expires_at})


async def get_user_refresh_token(conn_pool, refresh_token):
    """Get user detail and refresh token from refresh_tokens and users table"""
    return await fetch_row(conn_pool, sql_stmt=sql_scripts['get_user_refresh_token'],
                           params={"refresh_token": refresh_token})


async def update_refresh_token(conn_pool, user_id, refresh_token, expires_at):
    """Update refresh token in refresh_tokens table"""
    return await insert(conn_pool, sql_stmt=sql_scripts['update_refresh_token'],
                        params={"user_id": user_id, "refresh_token": refresh_token, "expires_at": expires_at})


async def delete_refresh_token(conn_pool, refresh_token):
    """Delete refresh token from refresh_tokens table"""
    return await delete(conn_pool, sql_stmt=sql_scripts['delete_refresh_token'],
                        params={"refresh_token": refresh_token})
