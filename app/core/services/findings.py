import importlib
from app import app
from app.common.exception import ScanNotAuthorizedException, ResourceNotFoundException, RemediateDetailNotValidException, RemediationInProgressException
from app.common.constants import FINDING_DETAIL_KEYS
from app.common.utils import get_utc_timestamp
from ..models.mysql import get_scan_access, get_findings_for_scan, get_finding_detail, get_account_credentials, update_findings

__all__ = ["GetFindingsService", "GetFindingDetailService", "RemediateFindingService"]


class GetFindingsService:
    def __init__(self, user, scan_id, service_id=None, status=None, severity=None, page=1, page_size=20):
        self.user_id = user["user_id"]
        self.scan_id = scan_id
        self.service_id = service_id
        self.status = status
        self.severity = severity
        self.page = page
        self.page_size = page_size
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Check if user has access to this scan
        has_access = await get_scan_access(self.conn_pool, self.scan_id, self.user_id)
        if not has_access:
            raise ScanNotAuthorizedException

        # Get findings with pagination and filters
        findings_result = await get_findings_for_scan(
            self.conn_pool, self.scan_id, self.service_id, self.status, self.severity, self.page, self.page_size
        )

        # Calculate pagination info
        total_findings = findings_result["total"]
        total_pages = (total_findings + self.page_size - 1) // self.page_size  # Ceiling division

        return {
            "data": findings_result["findings"],
            "pagination": {"total": total_findings, "page": self.page, "page_size": self.page_size, "total_pages": total_pages},
        }


class GetFindingDetailService:
    def __init__(self, user, finding_id):
        self.user_id = user["user_id"]
        self.finding_id = finding_id
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Get finding details with joined data from related tables
        finding = await get_finding_detail(self.conn_pool, self.finding_id, self.user_id)
        if not finding:
            raise ResourceNotFoundException

        # The details field is already parsed in get_finding_detail
        # Ensure it's a dict if it's None
        if finding.get("details") is None:
            finding["details"] = []

        # Add field labels based on service type
        service_name = finding.get("service_name", "").lower()
        finding["field_labels"] = {}
        # Add common field labels
        if "common" in FINDING_DETAIL_KEYS:
            finding["field_labels"].update(FINDING_DETAIL_KEYS["common"])

        # Add service-specific field labels if available
        if service_name in FINDING_DETAIL_KEYS:
            finding["field_labels"].update(FINDING_DETAIL_KEYS[service_name])

        return finding


class RemediateFindingService:
    def __init__(self, user, finding_id, details):
        self.user_id = user["user_id"]
        self.finding_id = finding_id
        self.details = details
        self.conn_pool = app.state.connection_pool
        self.finding = None
        self.details_list = None
        self.detail_index = None
        self.credentials = None
        self.finding_update = None

    async def _get_finding_details(self):
        """Get finding details and verify access."""
        self.finding = await get_finding_detail(self.conn_pool, self.finding_id, self.user_id)
        if not self.finding:
            raise ResourceNotFoundException
        
        # Get the current details list
        self.details_list = self.finding["details"]
        
        # Prepare the finding update with current status as default
        self.finding_update = {
            "status": self.finding["status"],
            "details": self.details_list
        }

    async def _validate_detail_exists(self):
        """
        Validate that the provided detail exists in the finding details.
        Compares details excluding the 'remediate' key since it might have been added
        in previous remediation attempts.
        """
        self.detail_index = None

        # Create a copy of self.details without the remediate key for comparison
        details_for_comparison = {k: v for k, v in self.details.items() if k != 'remediate'}

        for i, detail in enumerate(self.details_list):
            # Create a copy of the current detail without the remediate key
            detail_for_comparison = {k: v for k, v in detail.items() if k != 'remediate'}

            # Compare the details excluding the remediate key
            if detail_for_comparison == details_for_comparison:
                self.detail_index = i
                break
                
        if self.detail_index is None:
            raise RemediateDetailNotValidException

        # Check if remediation is already in progress
        await self._check_remediation_in_progress()

    async def _check_remediation_in_progress(self):
        """
        Check if remediation is already in progress for this detail.
        Raises an exception if remediation is pending.
        """
        detail = self.details_list[self.detail_index]

        if "remediate" in detail and detail["remediate"].get("status") == "pending":
            # Get the user who initiated the remediation
            attempted_by = detail["remediate"].get("attempted_by", {})
            user_email = attempted_by.get("email", "another user")

            # Get the timestamp when remediation was initiated
            attempted_at = detail["remediate"].get("attempted_at", "recently")

            error_message = f"Remediation already in progress, initiated by {user_email} at {attempted_at}"
            app.logger.info(error_message)

            # Create a custom exception for this case
            raise RemediationInProgressException

    async def _get_user_details(self, user_id):
        """Get user details from the database."""
        from app.core.models.mysql import get_user_by_id

        user = await get_user_by_id(self.conn_pool, user_id)
        if not user:
            # If user details can't be retrieved, fall back to just the ID
            return {"id": user_id}

        # Return a subset of user information that's relevant
        return {
            "id": user_id,
            "email": user.get("email", ""),
            "is_admin": user.get("created_by") == user_id  # User is admin if they created themselves
        }

    async def _get_account_credentials(self):
        """Get account credentials for the finding."""
        account_id = self.finding["account_id"]
        self.credentials = await get_account_credentials(self.conn_pool, account_id)

    async def _get_remediation_processor(self):
        """Dynamically import and instantiate the appropriate remediation processor."""
        cloud_provider = self.finding["cloud_provider_name"].lower()
        service_name = self.finding["service_name"].lower()
        
        try:
            module_path = f"app.core.services.api_cloud_providers.{cloud_provider}.{service_name}.remediate"
            remediation_module = importlib.import_module(module_path)
            processor_class = getattr(remediation_module, "RemediationProcessor")
            return processor_class(self.credentials), None
        except (ImportError, AttributeError) as e:
            error_message = f"Remediation not implemented for {cloud_provider}/{service_name}: {str(e)}"
            app.logger.warning(error_message)
            return None, error_message

    async def _perform_remediation(self, processor):
        """Perform the remediation and update the details."""
        # First, set status to pending to prevent race conditions
        if "remediate" not in self.details_list[self.detail_index]:
            self.details_list[self.detail_index]["remediate"] = {}
        
        # Get user details
        user_details = await self._get_user_details(self.user_id)
        
        # Update with pending status
        self.details_list[self.detail_index]["remediate"]["status"] = "pending"
        self.details_list[self.detail_index]["remediate"]["attempted_at"] = get_utc_timestamp()
        self.details_list[self.detail_index]["remediate"]["attempted_by"] = user_details
        
        # Update finding to save the pending status before starting remediation
        await self._update_finding()
        
        # Now proceed with the actual remediation
        policy_check = self.finding["policy_check"]
        success, message, updated_details = await processor.remediate(policy_check, self.details)
        
        # Add timestamp and user information to the remediate object
        if "remediate" not in updated_details:
            updated_details["remediate"] = {}
            
        updated_details["remediate"]["attempted_at"] = get_utc_timestamp()
        updated_details["remediate"]["attempted_by"] = user_details
        
        # Replace the old details with the updated one
        self.details_list[self.detail_index] = updated_details
        
        return success

    def _check_all_details_remediated(self):
        """Check if all details are either compliant or successfully remediated."""
        all_details_compliant_or_remediated = True
        
        for detail in self.details_list:
            # If a detail is marked as non-compliant (compliance=False) and hasn't been successfully remediated
            # (either no remediate key or remediate.status is not "pass")
            if detail.get("compliance") is False and (
                "remediate" not in detail or 
                detail["remediate"].get("status") != "pass"
            ):
                all_details_compliant_or_remediated = False
                break
        
        # Only update status to remediated if ALL non-compliant details have been remediated
        if all_details_compliant_or_remediated:
            self.finding_update["status"] = "remediated"

    async def _handle_remediation_failure(self, error_message):
        """Handle the case when remediation fails."""
        # Update the specific detail object with failure information
        if "remediate" not in self.details_list[self.detail_index]:
            self.details_list[self.detail_index]["remediate"] = {}
            
        self.details_list[self.detail_index]["remediate"]["status"] = "fail"
        self.details_list[self.detail_index]["remediate"]["message"] = f"Error: {error_message}"
        self.details_list[self.detail_index]["remediate"]["attempted_at"] = get_utc_timestamp()

        # Get user details instead of just storing the ID
        user_details = await self._get_user_details(self.user_id)
        self.details_list[self.detail_index]["remediate"]["attempted_by"] = user_details

    async def _update_finding(self):
        """Update the finding in the database."""
        await update_findings(self.conn_pool, self.finding_id, self.finding_update)

    async def process(self):
        """
        Process the remediation request.
        """
        # Get finding details and validate
        await self._get_finding_details()
        await self._validate_detail_exists()
        await self._get_account_credentials()
        
        # Get remediation processor
        processor, error_message = await self._get_remediation_processor()
        
        if not processor:
            # Handle remediation failure
            await self._handle_remediation_failure(error_message)
            await self._update_finding()
            return False
        
        try:
            # Perform remediation
            success = await self._perform_remediation(processor)
            
            # Check if all details are remediated
            self._check_all_details_remediated()
            
            # Update finding in database
            await self._update_finding()
            
            return success
            
        except Exception as e:
            error_message = f"Error during remediation: {str(e)}"
            app.logger.exception(error_message)
            
            # Handle remediation failure
            await self._handle_remediation_failure(error_message)
            await self._update_finding()
            
            return False
