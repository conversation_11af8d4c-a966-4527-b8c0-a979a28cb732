import copy
import json

from celery.utils.log import get_task_logger
from ..config import BaseRemediationProcessor
from app.common import AWSServiceNameEnum, ResourceComplianceStatusEnum

__all__ = ['RemediationProcessor']

logger = get_task_logger(__name__)


class RemediationProcessor(BaseRemediationProcessor):
    """
    Processor for remediating ECS compliance issues.
    Inherits from BaseRemediationProcessor to reuse AWS session management.
    """

    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.remediation_results = {
            "secure_networking_modes": {
                "status": False,
                "message": "",
                "field_updates": {"secure_network_mode": True, "has_task_role": True}
            },
            "latest_farget_platform_version": {
                "status": False,
                "message": "",
                "field_updates": {"is_latest": True}
            },
            "container_insights_enabled": {
                "status": False,
                "message": "",
                "field_updates": {"container_insights_enabled": True}
            },
            "no_public_ip_assignment_task_sets": {
                "status": False,
                "message": "",
                "field_updates": {"no_public_ip": True}
            },
            "no_public_ip_assignment_services": {
                "status": False,
                "message": "",
                "field_updates": {"no_public_ip": True}
            },
            "no_host_process_namespace": {
                "status": False,
                "message": "",
                "field_updates": {"no_host_process_namespace": True}
            },
            "non_privileged_containers": {
                "status": False,
                "message": "",
                "field_updates": {"non_privileged": True}
            },
            "read_only_root_filesystems": {
                "status": False,
                "message": "",
                "field_updates": {"readonly_root_filesystem": True}
            },
            "no_env_variables_for_secrets": {
                "status": False,
                "message": "",
                "field_updates": {"has_secrets_in_env": False}
            },
            "logging_configuration_required": {
                "status": False,
                "message": "",
                "field_updates": {"has_logging_config": True}
            }
        }

    async def remediate_secure_networking_modes(self, details):
        """
        Remediate ECS by updating task definitions to use secure networking modes.
        """
        try:
            task_definition_arn = details.get("task_definition_arn", "")
            region = details.get("region", "")

            if not task_definition_arn or not region:
                return False, "Missing task definition ARN or region"

            # Pre-check: Get the current task definition and check if already using secure networking
            task_def_response = await self.client.describe_task_definition(
                taskDefinition=task_definition_arn
            )

            if not task_def_response.get("taskDefinition"):
                return False, f"Task definition {task_definition_arn} not found"

            task_def = task_def_response["taskDefinition"]

            # Check if already using awsvpc network mode and has a task role
            if task_def.get("networkMode") == "awsvpc" and task_def.get("taskRoleArn"):
                self.remediation_results["secure_networking_modes"]["field_updates"]["network_mode"] = task_def.get(
                    "networkMode")
                self.remediation_results["secure_networking_modes"]["field_updates"]["task_role_arn"] = task_def.get(
                    "taskRoleArn")
                return True, f"Task definition {task_definition_arn} is already using secure networking mode"

            # Create a new revision with awsvpc network mode
            container_definitions = task_def.get("containerDefinitions", [])

            # Prepare task role ARN - use existing or create a new one if needed
            task_role_arn = task_def.get("taskRoleArn", "")
            if not task_role_arn:
                # Create a basic task role if one doesn't exist
                session = self.get_session()
                async with session.client('iam') as iam_client:
                    role_name = f"ECSTaskRole-{task_def['family']}"
                    try:
                        # Check if role already exists
                        role_response = await iam_client.get_role(RoleName=role_name)
                        task_role_arn = role_response["Role"]["Arn"]
                    except Exception:
                        # Create a new role with basic permissions
                        role_response = await iam_client.create_role(
                            RoleName=role_name,
                            AssumeRolePolicyDocument=json.dumps({
                                "Version": "2012-10-17",
                                "Statement": [
                                    {
                                        "Effect": "Allow",
                                        "Principal": {"Service": "ecs-tasks.amazonaws.com"},
                                        "Action": "sts:AssumeRole"
                                    }
                                ]
                            })
                        )
                        task_role_arn = role_response["Role"]["Arn"]

            # Register a new task definition with secure networking
            networkMode = "awsvpc"
            new_task_def = await self.client.register_task_definition(
                family=task_def["family"],
                taskRoleArn=task_role_arn,
                executionRoleArn=task_def.get("executionRoleArn", ""),
                networkMode=networkMode,  # Use awsvpc network mode for better security
                containerDefinitions=container_definitions,
                volumes=task_def.get("volumes", []),
                placementConstraints=task_def.get("placementConstraints", []),
                requiresCompatibilities=task_def.get("requiresCompatibilities", []),
                cpu=task_def.get("cpu", ""),
                memory=task_def.get("memory", "")
            )

            self.remediation_results["secure_networking_modes"]["field_updates"]["network_mode"] = networkMode
            self.remediation_results["secure_networking_modes"]["field_updates"]["task_role_arn"] = task_role_arn

            logger.info(
                f"Successfully updated task definition to use secure networking mode: {new_task_def['taskDefinition']['taskDefinitionArn']}")
            return True, f"Successfully updated task definition to use secure networking mode: {new_task_def['taskDefinition']['taskDefinitionArn']}"

        except Exception as e:
            error_msg = f"Failed to update task definition to use secure networking mode: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_latest_fargate_platform_version(self, details):
        """
        Remediate ECS by updating to the latest Fargate platform version.
        """
        try:
            cluster_arn = details.get("cluster_arn", "")
            service_arn = details.get("service_arn", "")
            region = details.get("region", "")

            if not cluster_arn or not service_arn or not region:
                return False, "Missing cluster ARN, service ARN, or region"

            # Pre-check: Get the service details and check if already using LATEST
            service_response = await self.client.describe_services(
                cluster=cluster_arn,
                services=[service_arn]
            )

            if not service_response.get("services"):
                return False, f"Service {service_arn} not found in cluster {cluster_arn}"

            service = service_response["services"][0]

            # Check if already using LATEST platform version
            if service.get("platformVersion") == "LATEST":
                self.remediation_results["latest_farget_platform_version"]["field_updates"][
                    "platform_version"] = "LATEST"
                return True, f"Service {service_arn} is already using the latest Fargate platform version"

            # Update the service to use the LATEST platform version
            await self.client.update_service(
                cluster=cluster_arn,
                service=service_arn,
                platformVersion="LATEST",
                forceNewDeployment=True
            )

            self.remediation_results["latest_farget_platform_version"]["field_updates"]["platform_version"] = "LATEST"

            logger.info(f"Successfully updated to latest Fargate platform version for ECS service {service_arn}")
            return True, f"Successfully updated to latest Fargate platform version for ECS service {service_arn}"

        except Exception as e:
            error_msg = f"Failed to update to latest Fargate platform version: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_container_insights_enabled(self, details):
        """
        Remediate ECS by enabling Container Insights.
        """
        try:
            cluster_arn = details.get("cluster_arn", "")
            region = details.get("region", "")

            if not cluster_arn or not region:
                return False, "Missing cluster ARN or region"

            # Pre-check: Check if Container Insights is already enabled
            cluster_response = await self.client.describe_clusters(
                clusters=[cluster_arn]
            )

            if not cluster_response.get("clusters"):
                return False, f"Cluster {cluster_arn} not found"

            cluster = cluster_response["clusters"][0]
            settings = cluster.get("settings", [])

            container_insights_enabled = any(
                setting.get("name") == "containerInsights" and setting.get("value") == "enabled"
                for setting in settings
            )

            if container_insights_enabled:
                return True, f"Container Insights is already enabled for ECS cluster {cluster_arn}"

            # Enable Container Insights
            await self.client.update_cluster_settings(
                cluster=cluster_arn,
                settings=[
                    {
                        'name': 'containerInsights',
                        'value': 'enabled'
                    }
                ]
            )

            logger.info(f"Successfully enabled Container Insights for ECS cluster {cluster_arn}")
            return True, f"Successfully enabled Container Insights for ECS cluster {cluster_arn}"

        except Exception as e:
            error_msg = f"Failed to enable Container Insights: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_no_public_ip_assignment_task_sets(self, details):
        """
        Remediate ECS by disabling public IP assignment for task sets.
        """
        try:
            cluster_arn = details.get("cluster_arn", "")
            service_arn = details.get("service_arn", "")
            task_set_arn = details.get("task_set_arn", "")
            region = details.get("region", "")

            if not cluster_arn or not service_arn or not task_set_arn or not region:
                return False, "Missing cluster ARN, service ARN, task set ARN, or region"

            # Pre-check: Get the task set details and check if public IP is already disabled
            task_set_response = await self.client.describe_task_sets(
                cluster=cluster_arn,
                service=service_arn,
                taskSets=[task_set_arn]
            )

            if not task_set_response.get("taskSets"):
                return False, f"Task set {task_set_arn} not found in service {service_arn}"

            task_set = task_set_response["taskSets"][0]

            # Get the current network configuration
            network_config = task_set.get("networkConfiguration", {}).get("awsvpcConfiguration", {})

            # Check if public IP assignment is already disabled
            if network_config.get("assignPublicIp") == "DISABLED":
                self.remediation_results["no_public_ip_assignment_task_sets"]["field_updates"][
                    "assign_public_ip"] = "DISABLED"
                return True, f"Public IP assignment is already disabled for ECS task set {task_set_arn}"

            # Create a new task set with public IP assignment disabled
            # Note: We can't directly update a task set, so we need to create a new one
            new_task_set = await self.client.create_task_set(
                cluster=cluster_arn,
                service=service_arn,
                taskDefinition=task_set["taskDefinition"],
                networkConfiguration={
                    'awsvpcConfiguration': {
                        'subnets': network_config.get("subnets", []),
                        'securityGroups': network_config.get("securityGroups", []),
                        'assignPublicIp': 'DISABLED'
                    }
                },
                scale={
                    'value': 100.0,
                    'unit': 'PERCENT'
                }
            )

            # Delete the old task set
            await self.client.delete_task_set(
                cluster=cluster_arn,
                service=service_arn,
                taskSet=task_set_arn
            )

            self.remediation_results["no_public_ip_assignment_task_sets"]["field_updates"][
                "assign_public_ip"] = "DISABLED"

            logger.info(f"Successfully disabled public IP assignment for ECS task set {task_set_arn}")
            return True, f"Successfully disabled public IP assignment for ECS task set {task_set_arn}"

        except Exception as e:
            error_msg = f"Failed to disable public IP assignment for task set: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_no_public_ip_assignment_services(self, details):
        """
        Remediate ECS by disabling public IP assignment for services.
        """
        try:
            cluster_arn = details.get("cluster_arn", "")
            service_arn = details.get("service_arn", "")
            region = details.get("region", "")

            if not cluster_arn or not service_arn or not region:
                return False, "Missing cluster ARN, service ARN, or region"

            # Pre-check: Get the service details and check if public IP is already disabled
            service_response = await self.client.describe_services(
                cluster=cluster_arn,
                services=[service_arn]
            )

            if not service_response.get("services"):
                return False, f"Service {service_arn} not found in cluster {cluster_arn}"

            service = service_response["services"][0]

            # Check if the service uses Fargate launch type
            if service.get("launchType") != "FARGATE" and not any(
                    provider.get("name") == "FARGATE" for provider in service.get("capacityProviderStrategy", [])
            ):
                return False, "This remediation only applies to Fargate services"

            # Get the current network configuration
            network_config = service.get("networkConfiguration", {}).get("awsvpcConfiguration", {})

            # Check if public IP assignment is already disabled
            if network_config.get("assignPublicIp") == "DISABLED":
                self.remediation_results["no_public_ip_assignment_services"]["field_updates"][
                    "assign_public_ip"] = "DISABLED"
                return True, f"Public IP assignment is already disabled for ECS service {service_arn}"

            # Update the service to disable public IP assignment
            await self.client.update_service(
                cluster=cluster_arn,
                service=service_arn,
                networkConfiguration={
                    'awsvpcConfiguration': {
                        'subnets': network_config.get("subnets", []),
                        'securityGroups': network_config.get("securityGroups", []),
                        'assignPublicIp': 'DISABLED'
                    }
                },
                forceNewDeployment=True
            )

            self.remediation_results["no_public_ip_assignment_services"]["field_updates"][
                "assign_public_ip"] = "DISABLED"

            logger.info(f"Successfully disabled public IP assignment for ECS service {service_arn}")
            return True, f"Successfully disabled public IP assignment for ECS service {service_arn}"

        except Exception as e:
            error_msg = f"Failed to disable public IP assignment: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_no_host_process_namespace(self, details):
        """
        Remediate ECS by updating task definitions to not share host process namespace.
        """
        try:
            task_definition_arn = details.get("task_definition_arn", "")
            region = details.get("region", "")

            if not task_definition_arn or not region:
                return False, "Missing task definition ARN or region"

            # Pre-check: Get the current task definition and check if already not sharing host process namespace
            task_def_response = await self.client.describe_task_definition(
                taskDefinition=task_definition_arn
            )

            if not task_def_response.get("taskDefinition"):
                return False, f"Task definition {task_definition_arn} not found"

            task_def = task_def_response["taskDefinition"]

            # Check if already not using host process namespace
            if task_def.get("pidMode") != "host":
                self.remediation_results["no_host_process_namespace"]["field_updates"]["pid_mode"] = task_def.get(
                    "pidMode")
                return True, f"Task definition {task_definition_arn} is already not sharing host process namespace"

            # Create a new revision without host process namespace sharing
            container_definitions = task_def.get("containerDefinitions", [])

            # Register a new task definition with pidMode set to task
            new_task_def = await self.client.register_task_definition(
                family=task_def["family"],
                taskRoleArn=task_def.get("taskRoleArn", ""),
                executionRoleArn=task_def.get("executionRoleArn", ""),
                networkMode=task_def.get("networkMode", "bridge"),
                containerDefinitions=container_definitions,
                volumes=task_def.get("volumes", []),
                placementConstraints=task_def.get("placementConstraints", []),
                requiresCompatibilities=task_def.get("requiresCompatibilities", []),
                cpu=task_def.get("cpu", ""),
                memory=task_def.get("memory", ""),
                pidMode="task"  # Use task-level process namespace instead of host
            )

            self.remediation_results["no_host_process_namespace"]["field_updates"]["pid_mode"] = "task"

            logger.info(
                f"Successfully updated task definition to not share host process namespace: {new_task_def['taskDefinition']['taskDefinitionArn']}")
            return True, f"Successfully updated task definition to not share host process namespace: {new_task_def['taskDefinition']['taskDefinitionArn']}"

        except Exception as e:
            error_msg = f"Failed to update task definition to not share host process namespace: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_non_privileged_containers(self, details):
        """
        Remediate ECS by updating task definitions to use non-privileged containers.
        """
        try:
            task_definition_arn = details.get("task_definition_arn", "")
            region = details.get("region", "")

            if not task_definition_arn or not region:
                return False, "Missing task definition ARN or region"

            # Pre-check: Get the current task definition and check if already using non-privileged containers
            task_def_response = await self.client.describe_task_definition(
                taskDefinition=task_definition_arn
            )

            if not task_def_response.get("taskDefinition"):
                return False, f"Task definition {task_definition_arn} not found"

            task_def = task_def_response["taskDefinition"]

            # Check if any containers are privileged
            container_definitions = task_def.get("containerDefinitions", [])
            has_privileged_containers = any(container.get("privileged", False) for container in container_definitions)

            if not has_privileged_containers:
                self.remediation_results["non_privileged_containers"]["field_updates"]["is_privileged"] = False
                return True, f"Task definition {task_definition_arn} is already using non-privileged containers"

            # Create a new revision with non-privileged containers
            updated_container_definitions = copy.deepcopy(container_definitions)

            # Update container definitions to remove privileged mode
            for container in updated_container_definitions:
                if container.get("privileged"):
                    container["privileged"] = False

            # Register a new task definition with non-privileged containers
            new_task_def = await self.client.register_task_definition(
                family=task_def["family"],
                taskRoleArn=task_def.get("taskRoleArn", ""),
                executionRoleArn=task_def.get("executionRoleArn", ""),
                networkMode=task_def.get("networkMode", "bridge"),
                containerDefinitions=updated_container_definitions,
                volumes=task_def.get("volumes", []),
                placementConstraints=task_def.get("placementConstraints", []),
                requiresCompatibilities=task_def.get("requiresCompatibilities", []),
                cpu=task_def.get("cpu", ""),
                memory=task_def.get("memory", "")
            )

            self.remediation_results["non_privileged_containers"]["field_updates"]["is_privileged"] = False

            logger.info(
                f"Successfully updated task definition to use non-privileged containers: {new_task_def['taskDefinition']['taskDefinitionArn']}")
            return True, f"Successfully updated task definition to use non-privileged containers: {new_task_def['taskDefinition']['taskDefinitionArn']}"

        except Exception as e:
            error_msg = f"Failed to update task definition to use non-privileged containers: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_read_only_root_filesystems(self, details):
        """
        Remediate ECS by updating task definitions to use read-only root filesystems.
        """
        try:
            task_definition_arn = details.get("task_definition_arn", "")
            region = details.get("region", "")

            if not task_definition_arn or not region:
                return False, "Missing task definition ARN or region"

            # Pre-check: Get the current task definition and check if already using read-only root filesystems
            task_def_response = await self.client.describe_task_definition(
                taskDefinition=task_definition_arn
            )

            if not task_def_response.get("taskDefinition"):
                return False, f"Task definition {task_definition_arn} not found"

            task_def = task_def_response["taskDefinition"]

            # Check if all containers already have read-only root filesystems
            container_definitions = task_def.get("containerDefinitions", [])
            all_readonly = all(container.get("readonlyRootFilesystem", False) for container in container_definitions)

            if all_readonly:
                return True, f"Task definition {task_definition_arn} is already using read-only root filesystems"

            # Create a new revision with read-only root filesystems
            updated_container_definitions = copy.deepcopy(container_definitions)

            # Update container definitions to use read-only root filesystems
            for container in updated_container_definitions:
                container["readonlyRootFilesystem"] = True

            # Register a new task definition with read-only root filesystems
            new_task_def = await self.client.register_task_definition(
                family=task_def["family"],
                taskRoleArn=task_def.get("taskRoleArn", ""),
                executionRoleArn=task_def.get("executionRoleArn", ""),
                networkMode=task_def.get("networkMode", "bridge"),
                containerDefinitions=updated_container_definitions,
                volumes=task_def.get("volumes", []),
                placementConstraints=task_def.get("placementConstraints", []),
                requiresCompatibilities=task_def.get("requiresCompatibilities", []),
                cpu=task_def.get("cpu", ""),
                memory=task_def.get("memory", "")
            )

            logger.info(
                f"Successfully updated task definition to use read-only root filesystems: {new_task_def['taskDefinition']['taskDefinitionArn']}")
            return True, f"Successfully updated task definition to use read-only root filesystems: {new_task_def['taskDefinition']['taskDefinitionArn']}"

        except Exception as e:
            error_msg = f"Failed to update task definition to use read-only root filesystems: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_no_env_variables_for_secrets(self, details):
        """
        Remediate ECS by updating task definitions to use AWS Secrets Manager instead of environment variables for secrets.
        """
        try:
            task_definition_arn = details.get("task_definition_arn", "")
            region = details.get("region", "")

            if not task_definition_arn or not region:
                return False, "Missing task definition ARN or region"

            # Pre-check: Get the current task definition and check if already not using env vars for secrets
            task_def_response = await self.client.describe_task_definition(
                taskDefinition=task_definition_arn
            )

            if not task_def_response.get("taskDefinition"):
                return False, f"Task definition {task_definition_arn} not found"

            task_def = task_def_response["taskDefinition"]

            # Check if any containers have secrets in environment variables
            container_definitions = task_def.get("containerDefinitions", [])
            has_secrets_in_env = False

            for container in container_definitions:
                env_vars = container.get("environment", [])
                if any("secret" in env.get("name", "").lower() or "key" in env.get("name", "").lower()
                       for env in env_vars):
                    has_secrets_in_env = True
                    break

            if not has_secrets_in_env:
                return True, f"Task definition {task_definition_arn} is already not using environment variables for secrets"

            # Create a new revision with secrets properly handled
            updated_container_definitions = copy.deepcopy(container_definitions)

            # Update container definitions to use Secrets Manager
            for container in updated_container_definitions:
                # Check for sensitive environment variables
                env_vars = container.get("environment", [])
                secrets = container.get("secrets", []) or []

                # Look for potential secrets in environment variables
                new_env_vars = []
                for env in env_vars:
                    name = env.get("name", "")
                    value = env.get("value", "")

                    # Check if this looks like a secret (password, key, token, etc.)
                    if any(secret_word in name.lower() for secret_word in
                           ["password", "secret", "key", "token", "credential"]):
                        # Move to secrets
                        secret_name = f"/ecs/{task_def['family']}/{name}"

                        # Create a secret in Secrets Manager
                        session = self.get_session()
                        async with session.client('secretsmanager', region_name=region) as sm_client:
                            try:
                                await sm_client.create_secret(
                                    Name=secret_name,
                                    SecretString=value,
                                    Description=f"Secret for ECS task {task_def['family']}"
                                )
                            except Exception as e:
                                if "ResourceExistsException" not in str(e):
                                    raise
                                # Update the secret if it already exists
                                await sm_client.update_secret(
                                    SecretId=secret_name,
                                    SecretString=value
                                )

                        # Add to secrets list
                        secrets.append({
                            "name": name,
                            "valueFrom": f"arn:aws:secretsmanager:{region}:{self.credentials['aws_account_id']}:secret:{secret_name}"
                        })
                    else:
                        new_env_vars.append(env)

                # Update the container definition
                container["environment"] = new_env_vars
                container["secrets"] = secrets

            # Register a new task definition with secrets properly handled
            new_task_def = await self.client.register_task_definition(
                family=task_def["family"],
                taskRoleArn=task_def.get("taskRoleArn", ""),
                executionRoleArn=task_def.get("executionRoleArn", ""),
                networkMode=task_def.get("networkMode", "bridge"),
                containerDefinitions=updated_container_definitions,
                volumes=task_def.get("volumes", []),
                placementConstraints=task_def.get("placementConstraints", []),
                requiresCompatibilities=task_def.get("requiresCompatibilities", []),
                cpu=task_def.get("cpu", ""),
                memory=task_def.get("memory", "")
            )

            logger.info(
                f"Successfully updated task definition to use AWS Secrets Manager: {new_task_def['taskDefinition']['taskDefinitionArn']}")
            return True, f"Successfully updated task definition to use AWS Secrets Manager: {new_task_def['taskDefinition']['taskDefinitionArn']}"

        except Exception as e:
            error_msg = f"Failed to update task definition to use AWS Secrets Manager: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_logging_configuration_required(self, details):
        """
        Remediate ECS by updating task definitions to include logging configuration.
        """
        try:
            task_definition_arn = details.get("task_definition_arn", "")
            region = details.get("region", "")

            if not task_definition_arn or not region:
                return False, "Missing task definition ARN or region"

            # Pre-check: Get the current task definition and check if already has logging configuration
            task_def_response = await self.client.describe_task_definition(
                taskDefinition=task_definition_arn
            )

            if not task_def_response.get("taskDefinition"):
                return False, f"Task definition {task_definition_arn} not found"

            task_def = task_def_response["taskDefinition"]

            # Check if all containers already have logging configuration
            container_definitions = task_def.get("containerDefinitions", [])
            all_have_logging = all("logConfiguration" in container for container in container_definitions)

            if all_have_logging:
                return True, f"Task definition {task_definition_arn} already has logging configuration for all containers"

            # Create a new revision with logging configuration
            updated_container_definitions = copy.deepcopy(container_definitions)

            # Update container definitions to include logging
            for container in updated_container_definitions:
                if not container.get("logConfiguration"):
                    # Add CloudWatch Logs configuration
                    container["logConfiguration"] = {
                        "logDriver": "awslogs",
                        "options": {
                            "awslogs-group": f"/ecs/{task_def['family']}",
                            "awslogs-region": region,
                            "awslogs-stream-prefix": "ecs"
                        }
                    }

            # Register a new task definition with logging configuration
            new_task_def = await self.client.register_task_definition(
                family=task_def["family"],
                taskRoleArn=task_def.get("taskRoleArn", ""),
                executionRoleArn=task_def.get("executionRoleArn", ""),
                networkMode=task_def.get("networkMode", "bridge"),
                containerDefinitions=updated_container_definitions,
                volumes=task_def.get("volumes", []),
                placementConstraints=task_def.get("placementConstraints", []),
                requiresCompatibilities=task_def.get("requiresCompatibilities", []),
                cpu=task_def.get("cpu", ""),
                memory=task_def.get("memory", "")
            )

            # Create the log group if it doesn't exist
            session = self.get_session()
            async with session.client('logs', region_name=region) as logs_client:
                try:
                    await logs_client.create_log_group(
                        logGroupName=f"/ecs/{task_def['family']}"
                    )
                except Exception as e:
                    if "ResourceAlreadyExistsException" not in str(e):
                        logger.warning(f"Failed to create log group: {str(e)}")

            logger.info(
                f"Successfully updated task definition to include logging configuration: {new_task_def['taskDefinition']['taskDefinitionArn']}")
            return True, f"Successfully updated task definition to include logging configuration: {new_task_def['taskDefinition']['taskDefinitionArn']}"

        except Exception as e:
            error_msg = f"Failed to update task definition to include logging configuration: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def get_remediation_functions(self):
        """
        Return a mapping of policy checks to remediation functions.
        """
        return {
            "secure_networking_modes": self.remediate_secure_networking_modes,
            "latest_farget_platform_version": self.remediate_latest_fargate_platform_version,
            "container_insights_enabled": self.remediate_container_insights_enabled,
            "no_public_ip_assignment_task_sets": self.remediate_no_public_ip_assignment_task_sets,
            "no_public_ip_assignment_services": self.remediate_no_public_ip_assignment_services,
            "no_host_process_namespace": self.remediate_no_host_process_namespace,
            "non_privileged_containers": self.remediate_non_privileged_containers,
            "read_only_root_filesystems": self.remediate_read_only_root_filesystems,
            "no_env_variables_for_secrets": self.remediate_no_env_variables_for_secrets,
            "logging_configuration_required": self.remediate_logging_configuration_required
        }

    async def remediate(self, policy_check, details):
        """
        Main remediation method that delegates to specific remediation functions.
        """
        logger.info(f"Starting remediation for ECS policy check: {policy_check}")

        # Initialize updated details with the original details
        updated_details = details.copy()

        # Initialize AWS client
        session = self.get_session()
        async with session.client(AWSServiceNameEnum.ECS.value,
                                  region_name=details.get("region", "us-east-1")) as client:
            self.client = client

            # Get the appropriate remediation function
            remediation_functions = self.get_remediation_functions()
            if policy_check not in remediation_functions:
                error_msg = f"No remediation function found for policy check: {policy_check}"
                logger.warning(error_msg)

                # Update remediation results
                if policy_check in self.remediation_results:
                    self.remediation_results[policy_check]["status"] = False
                    self.remediation_results[policy_check]["message"] = error_msg

                # Add remediation failure information
                updated_details["remediate"] = {
                    "status": "fail",
                    "message": f"Error: {error_msg}"
                }

                return False, error_msg, updated_details

            # Call the appropriate remediation function
            success, message = await remediation_functions[policy_check](details)

            # Update remediation results
            if policy_check in self.remediation_results:
                self.remediation_results[policy_check]["status"] = success
                self.remediation_results[policy_check]["message"] = message

            # Update the details with remediation information
            updated_details["remediate"] = {
                "status": ResourceComplianceStatusEnum.PASS.value if success else ResourceComplianceStatusEnum.FAIL.value,
                "message": message
            }

            # If successful, update the field values
            if success and policy_check in self.remediation_results:
                for field, value in self.remediation_results[policy_check]["field_updates"].items():
                    updated_details[field] = value

            # Update compliance status
            updated_details["compliance"] = True

        return success, message, updated_details
