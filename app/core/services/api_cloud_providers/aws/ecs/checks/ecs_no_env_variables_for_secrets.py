from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, ECSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ecs.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "no_env_variables_for_secrets": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.NO_ENV_VARIABLES_FOR_SECRETS.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}

            task_definition_arns = (cached.get("task_definitions") or {}).get("taskDefinitionArns", [])
            task_definition_details = cached.get("task_definition_details", {})

            for task_definition_arn in task_definition_arns:
                task_definition_data = task_definition_details.get(task_definition_arn, {})
                task_definition = task_definition_data.get("taskDefinition", {})
                container_definitions = task_definition.get("containerDefinitions", [])

                for container in container_definitions:
                    environment_variables = container.get("environment", [])
                    has_secrets_in_env = any(
                        "secret" in env.get("name", "").lower() or "key" in env.get("name", "").lower()
                        for env in environment_variables
                    )

                    compliance = not has_secrets_in_env

                    if (
                        findings["no_env_variables_for_secrets"]["status"]
                        == ResourceComplianceStatusEnum.PASS.value
                        and not compliance
                    ):
                        findings["no_env_variables_for_secrets"]["status"] = (
                            ResourceComplianceStatusEnum.FAIL.value
                        )

                    findings["no_env_variables_for_secrets"]["details"].append({
                        "task_definition_arn": task_definition_arn,
                        "container_name": container.get("name"),
                        "environment_variables": environment_variables,
                        "has_secrets_in_env": has_secrets_in_env,
                        "compliance": compliance,
                        "region": region,
                    })

        return findings

    def remediate(self):
        pass