from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, ECSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ecs.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "container_insights_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.CONTAINER_INSIGHTS_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            
            # Get cluster ARNs from cached data
            cluster_arns = (cached.get("clusters") or {}).get("clusterArns", [])
            
            # Get detailed cluster information from cached data
            cluster_details = cached.get("cluster_details", {})

            for cluster_arn in cluster_arns:
                # Get cluster details from cache
                cluster_data = cluster_details.get(cluster_arn, {})
                settings = cluster_data.get("settings", [])
                
                # Check if Container Insights is enabled
                container_insights_enabled = any(
                    setting.get("name") == "containerInsights" and setting.get("value") == "enabled"
                    for setting in settings
                )
                
                compliance = container_insights_enabled
                
                # Update overall status if we found a non-compliant cluster
                if (findings["container_insights_enabled"]["status"] == ResourceComplianceStatusEnum.PASS.value 
                    and not compliance):
                    findings["container_insights_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value
                
                # Add cluster details
                findings["container_insights_enabled"]["details"].append({
                    "cluster_arn": cluster_arn,
                    "container_insights_enabled": container_insights_enabled,
                    "compliance": compliance,
                    "region": region,
                })

        return findings

    def remediate(self):
        pass