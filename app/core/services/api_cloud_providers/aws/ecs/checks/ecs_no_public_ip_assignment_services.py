from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, ECSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ecs.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "no_public_ip_assignment_services": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.NO_PUBLIC_IP_ASSIGNMENT_SERVICES.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}

            # Cluster → service ARNs mapping
            cluster_services = cached.get("services", {})
            service_details = cached.get("service_details", {})

            for cluster_arn, service_data in cluster_services.items():
                for service_arn in service_data.get("serviceArns", []):
                    service_key = f"{cluster_arn}#{service_arn}"
                    svc = service_details.get(service_key, {})

                    network_configuration = svc.get("networkConfiguration", {})
                    assign_public_ip = (
                        network_configuration
                        .get("awsvpcConfiguration", {})
                        .get("assignPublicIp", "DISABLED")
                    )

                    no_public_ip = assign_public_ip == "DISABLED"
                    compliance = no_public_ip

                    if (
                        findings["no_public_ip_assignment_services"]["status"]
                        == ResourceComplianceStatusEnum.PASS.value
                        and not compliance
                    ):
                        findings["no_public_ip_assignment_services"]["status"] = (
                            ResourceComplianceStatusEnum.FAIL.value
                        )

                    findings["no_public_ip_assignment_services"]["details"].append({
                        "cluster_arn": cluster_arn,
                        "service_arn": service_arn,
                        "assign_public_ip": assign_public_ip,
                        "no_public_ip": no_public_ip,
                        "compliance": compliance,
                        "region": region,
                    })

        return findings

    def remediate(self):
        # Optional: implement update-service with assignPublicIp=DISABLED
        pass