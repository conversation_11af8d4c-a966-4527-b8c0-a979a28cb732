from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, ECSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ecs.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "task_definition_logging_nonblocking": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.TASK_DEFINITION_LOGGING_NONBLOCKING.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}

            task_definition_arns = (cached.get("task_definitions") or {}).get("taskDefinitionArns", [])
            task_definition_details = cached.get("task_definition_details", {})

            for task_definition_arn in task_definition_arns:
                task_definition_data = task_definition_details.get(task_definition_arn, {})
                task_definition = task_definition_data.get("taskDefinition", {})

                task_def_family = task_definition.get("family", "Unknown")
                task_def_revision = task_definition.get("revision", "Unknown")
                container_definitions = task_definition.get("containerDefinitions", [])

                if not container_definitions:
                    # No containers in task definition
                    findings["task_definition_logging_nonblocking"]["details"].append({
                        "task_definition_arn": task_definition_arn,
                        "task_definition_family": task_def_family,
                        "task_definition_revision": task_def_revision,
                        "region": region,
                        "container_name": "N/A",
                        "logging_configured": False,
                        "logging_mode": "N/A",
                        "log_driver": "N/A",
                        "log_options": {},
                        "compliance": False,
                        "note": "No container definitions found"
                    })
                    findings["task_definition_logging_nonblocking"]["status"] = ResourceComplianceStatusEnum.FAIL.value
                    continue

                for container in container_definitions:
                    container_name = container.get("name", "Unknown")
                    log_configuration = container.get("logConfiguration", {})

                    logging_configured = bool(log_configuration)
                    log_driver = log_configuration.get("logDriver", "None") if logging_configured else "None"
                    log_options = log_configuration.get("options", {}) if logging_configured else {}
                    logging_mode = log_options.get("mode", "blocking")  # default to blocking if not set

                    is_nonblocking = logging_mode == "non-blocking"
                    compliance = logging_configured and is_nonblocking

                    if not compliance:
                        findings["task_definition_logging_nonblocking"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                    findings["task_definition_logging_nonblocking"]["details"].append({
                        "task_definition_arn": task_definition_arn,
                        "task_definition_family": task_def_family,
                        "task_definition_revision": task_def_revision,
                        "region": region,
                        "container_name": container_name,
                        "logging_configured": logging_configured,
                        "logging_mode": logging_mode,
                        "log_driver": log_driver,
                        "log_options": log_options,
                        "compliance": compliance
                    })

        return findings

    def remediate(self):
        pass
