from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, ECSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ecs.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "no_public_ip_assignment_task_sets": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.NO_PUBLIC_IP_ASSIGNMENT_TASK_SETS.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            
            # Get cluster ARNs from cached data
            cluster_arns = (cached.get("clusters") or {}).get("clusterArns", [])
            
            # Get services and task sets from cached data
            services = cached.get("services", {})
            task_sets = cached.get("task_sets", {})
            task_set_details = cached.get("task_set_details", {})

            for cluster_arn in cluster_arns:
                # Get services for this cluster
                cluster_services = services.get(cluster_arn, {}).get("serviceArns", [])
                
                for service_arn in cluster_services:
                    # Get task sets for this service using composite key
                    service_key = f"{cluster_arn}#{service_arn}"
                    service_task_sets = task_sets.get(service_key, {}).get("taskSets", [])
                    
                    for task_set_arn in service_task_sets:
                        # Get task set details from cache using composite key
                        task_set_key = f"{cluster_arn}#{service_arn}#{task_set_arn}"
                        task_set_data = task_set_details.get(task_set_key, {})
                        
                        network_configuration = task_set_data.get("networkConfiguration", {})
                        assign_public_ip = network_configuration.get("awsvpcConfiguration", {}).get("assignPublicIp", "DISABLED")
                        
                        # Check if public IP assignment is disabled (compliant)
                        no_public_ip = assign_public_ip == "DISABLED"
                        compliance = no_public_ip
                        
                        # Update overall status if we found a non-compliant task set
                        if (findings["no_public_ip_assignment_task_sets"]["status"] == ResourceComplianceStatusEnum.PASS.value 
                            and not compliance):
                            findings["no_public_ip_assignment_task_sets"]["status"] = ResourceComplianceStatusEnum.FAIL.value
                        
                        # Add task set details
                        findings["no_public_ip_assignment_task_sets"]["details"].append({
                            "cluster_arn": cluster_arn,
                            "service_arn": service_arn,
                            "task_set_arn": task_set_arn,
                            "assign_public_ip": assign_public_ip,
                            "no_public_ip": no_public_ip,
                            "compliance": compliance,
                            "region": region,
                        })

        return findings

    def remediate(self):
        pass