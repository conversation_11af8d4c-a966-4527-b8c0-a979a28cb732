from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, ECSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ecs.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "read_only_root_filesystems": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.READ_ONLY_ROOT_FILESYSTEMS.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}

            task_definition_arns = (cached.get("task_definitions") or {}).get("taskDefinitionArns", [])
            task_definition_details = cached.get("task_definition_details", {})

            for task_definition_arn in task_definition_arns:
                task_definition_data = task_definition_details.get(task_definition_arn, {})
                task_definition = task_definition_data.get("taskDefinition", {})
                container_definitions = task_definition.get("containerDefinitions", [])

                for container in container_definitions:
                    readonly_root_filesystem = container.get("readonlyRootFilesystem", False)
                    compliance = readonly_root_filesystem

                    if (
                        findings["read_only_root_filesystems"]["status"]
                        == ResourceComplianceStatusEnum.PASS.value
                        and not compliance
                    ):
                        findings["read_only_root_filesystems"]["status"] = (
                            ResourceComplianceStatusEnum.FAIL.value
                        )

                    findings["read_only_root_filesystems"]["details"].append({
                        "task_definition_arn": task_definition_arn,
                        "container_name": container.get("name"),
                        "readonly_root_filesystem": readonly_root_filesystem,
                        "compliance": compliance,
                        "region": region,
                    })

        return findings

    def remediate(self):
        pass
