from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, ECSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ecs.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "secure_networking_modes": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.SECURE_NETWORKING_MODES.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            
            # Get task definition ARNs from cached data
            task_definition_arns = (cached.get("task_definitions") or {}).get("taskDefinitionArns", [])
            
            # Get detailed task definitions from cached data
            task_definition_details = cached.get("task_definition_details", {})

            for task_definition_arn in task_definition_arns:
                # Get task definition details from cache
                task_definition_data = task_definition_details.get(task_definition_arn, {})
                task_definition = task_definition_data.get("taskDefinition", {})
                
                network_mode = task_definition.get("networkMode", "")
                task_role_arn = task_definition.get("taskRoleArn", "")
                
                # Check for secure networking mode (awsvpc)
                secure_network_mode = network_mode == "awsvpc"
                has_task_role = bool(task_role_arn)
                
                # Task definition is compliant if it has secure network mode and task role
                compliance = secure_network_mode and has_task_role
                
                # Update overall status if we found a non-compliant task definition
                if (findings["secure_networking_modes"]["status"] == ResourceComplianceStatusEnum.PASS.value 
                    and not compliance):
                    findings["secure_networking_modes"]["status"] = ResourceComplianceStatusEnum.FAIL.value
                
                # Add task definition details
                findings["secure_networking_modes"]["details"].append({
                    "task_definition_arn": task_definition_arn,
                    "network_mode": network_mode,
                    "task_role_arn": task_role_arn,
                    "secure_network_mode": secure_network_mode,
                    "has_task_role": has_task_role,
                    "compliance": compliance,
                    "region": region,
                })

        return findings

    def remediate(self):
        pass