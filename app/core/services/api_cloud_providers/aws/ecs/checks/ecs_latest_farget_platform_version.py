from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, ECSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ecs.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "latest_fargate_platform_version": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.LATEST_FARGATE_PLATFORM_VERSION.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            
            # Get cluster ARNs from cached data
            cluster_arns = (cached.get("clusters") or {}).get("clusterArns", [])
            
            # Get services and service details from cached data
            services = cached.get("services", {})
            service_details = cached.get("service_details", {})

            for cluster_arn in cluster_arns:
                # Get services for this cluster
                cluster_services = services.get(cluster_arn, {}).get("serviceArns", [])
                
                for service_arn in cluster_services:
                    # Get service details from cache using composite key
                    service_key = f"{cluster_arn}#{service_arn}"
                    service_data = service_details.get(service_key, {})
                    
                    platform_version = service_data.get("platformVersion", "LATEST")
                    launch_type = service_data.get("launchType", "")
                    
                    # Only check Fargate services
                    if launch_type == "FARGATE":
                        is_latest = platform_version == "LATEST"
                        compliance = is_latest
                        
                        # Update overall status if we found a non-compliant service
                        if (findings["latest_fargate_platform_version"]["status"] == ResourceComplianceStatusEnum.PASS.value 
                            and not compliance):
                            findings["latest_fargate_platform_version"]["status"] = ResourceComplianceStatusEnum.FAIL.value
                        
                        # Add service details
                        findings["latest_fargate_platform_version"]["details"].append({
                            "cluster_arn": cluster_arn,
                            "service_arn": service_arn,
                            "platform_version": platform_version,
                            "launch_type": launch_type,
                            "is_latest": is_latest,
                            "compliance": compliance,
                            "region": region,
                        })

        return findings

    def remediate(self):
        pass