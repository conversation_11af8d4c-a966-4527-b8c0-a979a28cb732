import asyncio
from celery.utils.log import get_task_logger
from ..config import BaseChecksProcessor
from app.common import (AWSServiceNameEnum, SeverityEnum, ResourceComplianceStatusEnum,
                        ELBChecksDescriptionEnum)

__all__ = ['ChecksProcessor']

logger = get_task_logger(__name__)


class ChecksProcessor(BaseChecksProcessor):
    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.clientv2 = None
        self.classic_lbs = None
        self.lbs = None
        self.findings = {
            "http_to_https_redirect": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.HTTP_TO_HTTPS_REDIRECT.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "classic_load_balancer_az_span": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.CLASSIC_LOAD_BALANCER_AZ_SPAN.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "alb_desync_mitigation_mode": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.ALB_DESYNC_MITIGATION_MODE.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "load_balancers_az_span": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.LOAD_BALANCERS_AZ_SPAN.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "classic_load_balancer_desync_mitigation_mode": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.CLASSIC_LOAD_BALANCER_DESYNC_MITIGATION_MODE.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "classic_load_balancer_secure_listener_configuration": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.CLASSIC_LOAD_BALANCER_SECURE_LISTENER_CONFIGURATION.value,
                "severity": SeverityEnum.HIGH.value
            },
            "alb_drop_invalid_http_headers": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.ALB_DROP_INVALID_HTTP_HEADERS.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "load_balancers_logging_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.LOAD_BALANCERS_LOGGING_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "load_balancers_deletion_protection": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.LOAD_BALANCERS_DELETION_PROTECTION.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "classic_load_balancer_connection_draining": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.CLASSIC_LOAD_BALANCER_CONNECTION_DRAINING.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "classic_load_balancer_ssl_security_policy": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.CLASSIC_LOAD_BALANCER_SSL_SECURITY_POLICY.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "classic_load_balancer_cross_zone_load_balancing": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.CLASSIC_LOAD_BALANCER_CROSS_ZONE_LOAD_BALANCING.value,
                "severity": SeverityEnum.MEDIUM.value
            }
        }

    async def check_alb_http_to_https_redirect(self):
        """
        Application Load Balancer should be configured to redirect all HTTP requests to HTTPS.
        """
        all_compliant = True  # Track overall compliance status

        for lb in self.lbs.get("LoadBalancers", []):
            if lb["Type"] == "application":
                listeners = await self.clientv2.describe_listeners(LoadBalancerArn=lb["LoadBalancerArn"])
                redirect_to_https = False

                for listener in listeners.get("Listeners", []):
                    if listener["Protocol"] == "HTTP":
                        for action in listener.get("DefaultActions", []):
                            if action["Type"] == "redirect" and action["RedirectConfig"].get("Protocol") == "HTTPS":
                                redirect_to_https = True
                                break

                self.findings["http_to_https_redirect"]["details"].append({
                    "load_balancer_name": lb["LoadBalancerName"],
                    "load_balancer_arn": lb["LoadBalancerArn"],
                    "redirect_to_https": redirect_to_https,
                    "region": self.clientv2.meta.region_name,
                    "compliance": redirect_to_https
                })

                if not redirect_to_https:
                    all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["http_to_https_redirect"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_classic_load_balancer_az_span(self):
        """
        Classic Load Balancer should span multiple Availability Zones.
        """
        all_compliant = True  # Track overall compliance status

        for lb in self.classic_lbs.get("LoadBalancerDescriptions", []):
            azs = lb.get("AvailabilityZones", [])
            spans_multiple_azs = len(azs) > 1

            self.findings["classic_load_balancer_az_span"]["details"].append({
                "load_balancer_name": lb["LoadBalancerName"],
                "availability_zones": azs,
                "spans_multiple_azs": spans_multiple_azs,
                "region": self.client.meta.region_name,
                "compliance": spans_multiple_azs
            })

            if not spans_multiple_azs:
                all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["classic_load_balancer_az_span"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_alb_desync_mitigation_mode(self):
        """
        Application Load Balancer should be configured with defensive or strictest desync mitigation mode.
        """
        all_compliant = True  # Track overall compliance status

        for lb in self.lbs.get("LoadBalancers", []):
            if lb["Type"] == "application":
                attributes = await self.clientv2.describe_load_balancer_attributes(
                    LoadBalancerArn=lb["LoadBalancerArn"])
                desync_mitigation_mode = next(
                    (attr["Value"] for attr in attributes.get("Attributes", [])
                     if attr["Key"] == "routing.http.desync_mitigation_mode"),
                    None
                )

                compliant = desync_mitigation_mode in ["defensive", "strictest"]

                self.findings["alb_desync_mitigation_mode"]["details"].append({
                    "load_balancer_name": lb["LoadBalancerName"],
                    "load_balancer_arn": lb["LoadBalancerArn"],
                    "desync_mitigation_mode": desync_mitigation_mode,
                    "region": self.clientv2.meta.region_name,
                    "compliance": compliant
                })

                if not compliant:
                    all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["alb_desync_mitigation_mode"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_load_balancers_az_span(self):
        """
        Application, Network, and Gateway Load Balancers should span multiple Availability Zones.
        """
        all_compliant = True  # Track overall compliance status

        for lb in self.lbs.get("LoadBalancers", []):
            azs = lb.get("AvailabilityZones", [])
            spans_multiple_azs = len(azs) > 1

            self.findings["load_balancers_az_span"]["details"].append({
                "load_balancer_name": lb["LoadBalancerName"],
                "load_balancer_arn": lb["LoadBalancerArn"],
                "type": lb["Type"],
                "availability_zones": azs,
                "spans_multiple_azs": spans_multiple_azs,
                "region": self.clientv2.meta.region_name,
                "compliance": spans_multiple_azs
            })

            if not spans_multiple_azs:
                all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["load_balancers_az_span"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_classic_load_balancer_desync_mitigation_mode(self):
        """
        Classic Load Balancer should be configured with defensive or strictest desync mitigation mode.
        """
        all_compliant = True  # Track overall compliance status

        for lb in self.classic_lbs.get("LoadBalancerDescriptions", []):
            attributes = await self.client.describe_load_balancer_attributes(LoadBalancerName=lb["LoadBalancerName"])
            desync_mitigation_mode = next(
                (attr["Value"] for attr in attributes.get("LoadBalancerAttributes", [])
                 if attr["Key"] == "routing.http.desync_mitigation_mode"),
                None
            )

            compliant = desync_mitigation_mode in ["defensive", "strictest"]

            self.findings["classic_load_balancer_desync_mitigation_mode"]["details"].append({
                "load_balancer_name": lb["LoadBalancerName"],
                "desync_mitigation_mode": desync_mitigation_mode,
                "region": self.client.meta.region_name,
                "compliance": compliant
            })

            if not compliant:
                all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["classic_load_balancer_desync_mitigation_mode"][
                "status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_classic_load_balancer_secure_listener_configuration(self):
        """
        Classic Load Balancers should have HTTPS/SSL listeners configured with certificates from ACM.
        """
        all_compliant = True

        for lb in self.classic_lbs.get("LoadBalancerDescriptions", []):
            has_https_listener = False
            uses_acm_certificate = True

            for listener_desc in lb.get("ListenerDescriptions", []):
                listener = listener_desc["Listener"]
                if listener["Protocol"] in ["HTTPS", "SSL"]:
                    has_https_listener = True
                    # Check if the certificate is provided by ACM
                    if not listener.get("SSLCertificateId", "").startswith("arn:aws:acm:"):
                        uses_acm_certificate = False

            # If there are no HTTPS listeners, then ACM certificate check is not applicable
            if not has_https_listener:
                uses_acm_certificate = False

            # Compliant only if has HTTPS listener AND uses ACM certificate
            compliant = has_https_listener and uses_acm_certificate

            self.findings["classic_load_balancer_secure_listener_configuration"]["details"].append({
                "load_balancer_name": lb["LoadBalancerName"],
                "has_https_listener": has_https_listener,
                "uses_acm_certificate": uses_acm_certificate,
                "region": self.client.meta.region_name,
                "compliance": compliant
            })

            if not compliant:
                all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["classic_load_balancer_secure_listener_configuration"][
                "status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_alb_drop_invalid_http_headers(self):
        """
        Application Load Balancer should be configured to drop invalid HTTP headers.
        """
        all_compliant = True  # Track overall compliance status

        for lb in self.lbs.get("LoadBalancers", []):
            if lb["Type"] == "application":
                attributes = await self.clientv2.describe_load_balancer_attributes(
                    LoadBalancerArn=lb["LoadBalancerArn"]
                )
                drop_invalid_headers_enabled = next(
                    (attr["Value"] == "true" for attr in attributes.get("Attributes", [])
                     if attr["Key"] == "routing.http.drop_invalid_header_fields.enabled"),
                    False
                )

                self.findings["alb_drop_invalid_http_headers"]["details"].append({
                    "load_balancer_name": lb["LoadBalancerName"],
                    "load_balancer_arn": lb["LoadBalancerArn"],
                    "drop_invalid_headers_enabled": drop_invalid_headers_enabled,
                    "region": self.clientv2.meta.region_name,
                    "compliance": drop_invalid_headers_enabled
                })

                if not drop_invalid_headers_enabled:
                    all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["alb_drop_invalid_http_headers"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_load_balancers_logging_enabled(self):
        """
        Application and Classic Load Balancers logging should be enabled.
        """
        all_compliant = True  # Track overall compliance status

        # Check Application Load Balancers
        for lb in self.lbs.get("LoadBalancers", []):
            if lb["Type"] == "application":
                attributes = await self.clientv2.describe_load_balancer_attributes(
                    LoadBalancerArn=lb["LoadBalancerArn"]
                )
                logging_enabled = next(
                    (attr["Value"] == "true" for attr in attributes.get("Attributes", [])
                     if attr["Key"] == "access_logs.s3.enabled"),
                    False
                )

                self.findings["load_balancers_logging_enabled"]["details"].append({
                    "load_balancer_name": lb["LoadBalancerName"],
                    "load_balancer_arn": lb["LoadBalancerArn"],
                    "type": lb["Type"],
                    "logging_enabled": logging_enabled,
                    "region": self.clientv2.meta.region_name,
                    "compliance": logging_enabled
                })

                if not logging_enabled:
                    all_compliant = False

        # Check Classic Load Balancers
        for lb in self.classic_lbs.get("LoadBalancerDescriptions", []):
            attributes = await self.client.describe_load_balancer_attributes(LoadBalancerName=lb["LoadBalancerName"])
            logging_enabled = attributes.get("LoadBalancerAttributes", {}).get("AccessLog", {}).get("Enabled", False)

            self.findings["load_balancers_logging_enabled"]["details"].append({
                "load_balancer_name": lb["LoadBalancerName"],
                "load_balancer_arn": "",
                "type": "classic",
                "logging_enabled": logging_enabled,
                "region": self.client.meta.region_name,
                "compliance": logging_enabled
            })

            if not logging_enabled:
                all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["load_balancers_logging_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_load_balancers_deletion_protection(self):
        """
        Application, Gateway, and Network Load Balancers should have deletion protection enabled.
        """
        all_compliant = True  # Track overall compliance status

        for lb in self.lbs.get("LoadBalancers", []):
            attributes = await self.clientv2.describe_load_balancer_attributes(
                LoadBalancerArn=lb["LoadBalancerArn"]
            )
            deletion_protection_enabled = next(
                (attr["Value"] == "true" for attr in attributes.get("Attributes", [])
                 if attr["Key"] == "deletion_protection.enabled"),
                False
            )

            self.findings["load_balancers_deletion_protection"]["details"].append({
                "load_balancer_name": lb["LoadBalancerName"],
                "load_balancer_arn": lb["LoadBalancerArn"],
                "type": lb["Type"],
                "deletion_protection_enabled": deletion_protection_enabled,
                "region": self.clientv2.meta.region_name,
                "compliance": deletion_protection_enabled
            })

            if not deletion_protection_enabled:
                all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["load_balancers_deletion_protection"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_classic_load_balancer_connection_draining(self):
        """
        Classic Load Balancers should have connection draining enabled.
        """
        all_compliant = True  # Track overall compliance status

        for lb in self.classic_lbs.get("LoadBalancerDescriptions", []):
            attributes = await self.client.describe_load_balancer_attributes(LoadBalancerName=lb["LoadBalancerName"])
            connection_draining_enabled = attributes.get("LoadBalancerAttributes", {}).get("ConnectionDraining",
                                                                                           {}).get("Enabled", False)

            self.findings["classic_load_balancer_connection_draining"]["details"].append({
                "load_balancer_name": lb["LoadBalancerName"],
                "connection_draining_enabled": connection_draining_enabled,
                "region": self.client.meta.region_name,
                "compliance": connection_draining_enabled
            })

            if not connection_draining_enabled:
                all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["classic_load_balancer_connection_draining"][
                "status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_classic_load_balancer_ssl_security_policy(self):
        """
        Classic Load Balancers with SSL listeners should use a predefined security policy that has strong configuration.
        """
        all_compliant = True  # Track overall compliance status

        for lb in self.classic_lbs.get("LoadBalancerDescriptions", []):
            listeners = await self.client.describe_load_balancer_listeners(LoadBalancerName=lb["LoadBalancerName"])
            uses_strong_policy = True

            for listener in listeners.get("Listeners", []):
                if listener["Protocol"] == "SSL":
                    policy_name = listener.get("PolicyNames", [])
                    if not policy_name or not policy_name[0].startswith("ELBSecurityPolicy"):
                        uses_strong_policy = False
                        break

            self.findings["classic_load_balancer_ssl_security_policy"]["details"].append({
                "load_balancer_name": lb["LoadBalancerName"],
                "uses_strong_policy": uses_strong_policy,
                "region": self.client.meta.region_name,
                "compliance": uses_strong_policy
            })

            if not uses_strong_policy:
                all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["classic_load_balancer_ssl_security_policy"][
                "status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_classic_load_balancer_cross_zone_load_balancing(self):
        """
        Classic Load Balancers should have cross-zone load balancing enabled.
        """
        all_compliant = True  # Track overall compliance status

        for lb in self.classic_lbs.get("LoadBalancerDescriptions", []):
            attributes = await self.client.describe_load_balancer_attributes(LoadBalancerName=lb["LoadBalancerName"])
            cross_zone_load_balancing_enabled = attributes.get("LoadBalancerAttributes", {}).get(
                "CrossZoneLoadBalancing", {}).get("Enabled", False)

            self.findings["classic_load_balancer_cross_zone_load_balancing"]["details"].append({
                "load_balancer_name": lb["LoadBalancerName"],
                "cross_zone_load_balancing_enabled": cross_zone_load_balancing_enabled,
                "region": self.client.meta.region_name,
                "compliance": cross_zone_load_balancing_enabled
            })

            if not cross_zone_load_balancing_enabled:
                all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["classic_load_balancer_cross_zone_load_balancing"][
                "status"] = ResourceComplianceStatusEnum.FAIL.value

    def get_check_functions(self):
        return [
            self.check_alb_http_to_https_redirect,
            self.check_classic_load_balancer_az_span,
            self.check_alb_desync_mitigation_mode,
            self.check_load_balancers_az_span,
            self.check_classic_load_balancer_desync_mitigation_mode,
            self.check_classic_load_balancer_secure_listener_configuration,
            self.check_alb_drop_invalid_http_headers,
            self.check_load_balancers_logging_enabled,
            self.check_load_balancers_deletion_protection,
            self.check_classic_load_balancer_connection_draining,
            self.check_classic_load_balancer_ssl_security_policy,
            self.check_classic_load_balancer_cross_zone_load_balancing
        ]

    async def run_checks(self):
        for region in self.regions:
            logger.info(f"Scanning region: {region}")
            session = self.get_session(region)
            if not await self.is_region_accessible():
                logger.warning(f"Region {region} is not accessible. Skipping checks.")
                continue
            async with session.client(AWSServiceNameEnum.ELB.value, region_name=region) as client, \
                    session.client('elbv2', region_name=region) as clientv2:
                self.client = client
                self.clientv2 = clientv2

                self.classic_lbs = await self.client.describe_load_balancers()
                self.lbs = await self.clientv2.describe_load_balancers()

                for check_function in self.get_check_functions():
                    await check_function()
            await asyncio.sleep(5)

        return self.findings
