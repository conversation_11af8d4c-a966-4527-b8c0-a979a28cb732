import json
from celery.utils.log import get_task_logger
from ..config import BaseRemediationProcessor
from app.common import AWSServiceNameEnum, DEFAULT_RDS_ADMIN_USERNAMES

__all__ = ['RemediationProcessor']

logger = get_task_logger(__name__)


class RemediationProcessor(BaseRemediationProcessor):
    """
    Processor for remediating RDS compliance issues.
    Inherits from BaseRemediationProcessor to reuse AWS session management.
    """

    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.remediation_results = {
            "rds_custom_admin": {
                "status": False,
                "message": "",
                "field_updates": {"using_default_admin": False}
            },
            "rds_mysql_encryption_in_transit": {
                "status": False,
                "message": "",
                "field_updates": {"ssl_required": True}
            },
            "rds_enhanced_monitoring": {
                "status": False,
                "message": "",
                "field_updates": {"enhanced_monitoring_enabled": True}
            },
            "rds_custom_port": {
                "status": False,
                "message": "",
                "field_updates": {"using_default_port": False}
            },
            "rds_cluster_copy_tags_to_snapshots": {
                "status": False,
                "message": "",
                "field_updates": {"copy_tags_to_snapshots": True}
            },
            "rds_instance_copy_tags_to_snapshots": {
                "status": False,
                "message": "",
                "field_updates": {"copy_tags_to_snapshots": True}
            },
            "rds_snapshot_privacy": {
                "status": False,
                "message": "",
                "field_updates": {"publicly_accessible": False}
            },
            "rds_instance_public_access": {
                "status": False,
                "message": "",
                "field_updates": {"publicly_accessible": False}
            },
            "rds_auto_minor_version_upgrade": {
                "status": False,
                "message": "",
                "field_updates": {"auto_minor_version_upgrade": True}
            },
            "rds_instance_vpc_deployment": {
                "status": False,
                "message": "",
                "field_updates": {"in_vpc": True}
            },
            "rds_iam_authentication": {
                "status": False,
                "message": "",
                "field_updates": {"iam_auth_enabled": True}
            },
            "rds_automatic_backups": {
                "status": False,
                "message": "",
                "field_updates": {"backups_enabled": True}
            },
            "rds_cluster_iam_authentication": {
                "status": False,
                "message": "",
                "field_updates": {"iam_auth_enabled": True}
            },
            "aurora_cluster_backtracking": {
                "status": False,
                "message": "",
                "field_updates": {"backtrack_enabled": True}
            },
            "rds_cluster_multi_az": {
                "status": False,
                "message": "",
                "field_updates": {"multi_az": True}
            },
            "rds_cluster_encryption": {
                "status": False,
                "message": "",
                "field_updates": {"encrypted": True}
            },
            "rds_instance_encryption": {
                "status": False,
                "message": "",
                "field_updates": {"encrypted": True}
            },
            "aurora_mysql_audit_logging": {
                "status": False,
                "message": "",
                "field_updates": {"audit_logging_enabled": True}
            },
            "rds_cluster_auto_minor_version_upgrade": {
                "status": False,
                "message": "",
                "field_updates": {"auto_minor_version_upgrade": True}
            },
            "rds_postgresql_logging": {
                "status": False,
                "message": "",
                "field_updates": {"logging_enabled": True}
            },
            "rds_postgresql_encryption_in_transit": {
                "status": False,
                "message": "",
                "field_updates": {"ssl_required": True}
            },
            "rds_snapshot_encryption": {
                "status": False,
                "message": "",
                "field_updates": {"encrypted": True}
            },
            "rds_multi_az": {
                "status": False,
                "message": "",
                "field_updates": {"multi_az": True}
            },
            "rds_event_notifications": {
                "status": False,
                "message": "",
                "field_updates": {"has_critical_subscription": True}
            },
            "rds_instance_event_notifications": {
                "status": False,
                "message": "",
                "field_updates": {"has_critical_subscription": True}
            },
            "rds_parameter_group_event_notifications": {
                "status": False,
                "message": "",
                "field_updates": {"has_critical_subscription": True}
            },
            "rds_security_group_event_notifications": {
                "status": False,
                "message": "",
                "field_updates": {"has_critical_subscription": True}
            },
            "rds_cluster_deletion_protection": {
                "status": False,
                "message": "",
                "field_updates": {"deletion_protection": True}
            },
            "rds_instance_deletion_protection": {
                "status": False,
                "message": "",
                "field_updates": {"deletion_protection": True}
            },
            "rds_instance_logs_to_cloudwatch": {
                "status": False,
                "message": "",
                "field_updates": {"has_logging_enabled": True}
            },
            "rds_cluster_custom_admin": {
                "status": False,
                "message": "",
                "field_updates": {"using_default_admin": False}
            }
        }

    async def remediate_rds_enhanced_monitoring(self, details):
        """
        Remediate RDS instances by enabling enhanced monitoring.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            region = details.get("region", "")

            if not db_instance_id or not region:
                return False, "Missing DB instance ID or region"

            # Enable enhanced monitoring with 60-second interval
            await self.client.modify_db_instance(
                DBInstanceIdentifier=db_instance_id,
                MonitoringInterval=60,  # 60 seconds monitoring interval
                MonitoringRoleArn=f"arn:aws:iam::{self.credentials['aws_account_id']}:role/rds-monitoring-role",
                ApplyImmediately=True
            )

            logger.info(f"Successfully enabled enhanced monitoring for RDS instance {db_instance_id}")
            return True, f"Successfully enabled enhanced monitoring for RDS instance {db_instance_id}"
        except Exception as e:
            error_msg = f"Failed to enable enhanced monitoring for RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_custom_port(self, details):
        """
        Remediate RDS instances by changing from default port to a custom port.
        Note: This operation requires a DB instance reboot.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            engine = details.get("engine", "")
            region = details.get("region", "")

            if not db_instance_id or not engine or not region:
                return False, "Missing DB instance ID, engine, or region"

            # Define custom ports based on engine type
            custom_ports = {
                "mysql": 3307,
                "postgres": 5433,
                "oracle": 1522,
                "sqlserver": 1434,
                "mariadb": 3307,
                "aurora": 3307,
                "aurora-mysql": 3307,
                "aurora-postgresql": 5433
            }

            # Get custom port for the engine
            custom_port = custom_ports.get(engine.lower(), 0)
            if custom_port == 0:
                return False, f"No custom port defined for engine {engine}"

            # Modify the DB instance to use the custom port
            await self.client.modify_db_instance(
                DBInstanceIdentifier=db_instance_id,
                DBPortNumber=custom_port,
                ApplyImmediately=True
            )

            logger.info(f"Successfully changed port for RDS instance {db_instance_id} to {custom_port}")
            return True, f"Successfully changed port for RDS instance {db_instance_id} to {custom_port}"
        except Exception as e:
            error_msg = f"Failed to change port for RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_instance_copy_tags_to_snapshots(self, details):
        """
        Remediate RDS instances by enabling copy tags to snapshots.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            region = details.get("region", "")

            if not db_instance_id or not region:
                return False, "Missing DB instance ID or region"

            # Enable copy tags to snapshots
            await self.client.modify_db_instance(
                DBInstanceIdentifier=db_instance_id,
                CopyTagsToSnapshot=True,
                ApplyImmediately=True
            )

            logger.info(f"Successfully enabled copy tags to snapshots for RDS instance {db_instance_id}")
            return True, f"Successfully enabled copy tags to snapshots for RDS instance {db_instance_id}"
        except Exception as e:
            error_msg = f"Failed to enable copy tags to snapshots for RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_snapshot_privacy(self, details):
        """
        Remediate RDS snapshots by making them private.
        """
        try:
            snapshot_id = details.get("snapshot_id", "")
            region = details.get("region", "")

            if not snapshot_id or not region:
                return False, "Missing snapshot ID or region"

            # Make the snapshot private by removing all public access
            await self.client.modify_db_snapshot_attribute(
                DBSnapshotIdentifier=snapshot_id,
                AttributeName='restore',
                ValuesToRemove=['all']
            )

            logger.info(f"Successfully made RDS snapshot {snapshot_id} private")
            return True, f"Successfully made RDS snapshot {snapshot_id} private"
        except Exception as e:
            error_msg = f"Failed to make RDS snapshot {snapshot_id} private: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_instance_public_access(self, details):
        """
        Remediate RDS instances by disabling public access.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            region = details.get("region", "")

            if not db_instance_id or not region:
                return False, "Missing DB instance ID or region"

            # Disable public access
            await self.client.modify_db_instance(
                DBInstanceIdentifier=db_instance_id,
                PubliclyAccessible=False,
                ApplyImmediately=True
            )

            logger.info(f"Successfully disabled public access for RDS instance {db_instance_id}")
            return True, f"Successfully disabled public access for RDS instance {db_instance_id}"
        except Exception as e:
            error_msg = f"Failed to disable public access for RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_auto_minor_version_upgrade(self, details):
        """
        Remediate RDS instances by enabling automatic minor version upgrades.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            region = details.get("region", "")

            if not db_instance_id or not region:
                return False, "Missing DB instance ID or region"

            # Enable automatic minor version upgrades
            await self.client.modify_db_instance(
                DBInstanceIdentifier=db_instance_id,
                AutoMinorVersionUpgrade=True,
                ApplyImmediately=True
            )

            logger.info(f"Successfully enabled automatic minor version upgrades for RDS instance {db_instance_id}")
            return True, f"Successfully enabled automatic minor version upgrades for RDS instance {db_instance_id}"
        except Exception as e:
            error_msg = f"Failed to enable automatic minor version upgrades for RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_iam_authentication(self, details):
        """
        Remediate RDS instances by enabling IAM database authentication.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            region = details.get("region", "")

            if not db_instance_id or not region:
                return False, "Missing DB instance ID or region"

            # Enable IAM database authentication
            await self.client.modify_db_instance(
                DBInstanceIdentifier=db_instance_id,
                EnableIAMDatabaseAuthentication=True,
                ApplyImmediately=True
            )

            logger.info(f"Successfully enabled IAM database authentication for RDS instance {db_instance_id}")
            return True, f"Successfully enabled IAM database authentication for RDS instance {db_instance_id}"
        except Exception as e:
            error_msg = f"Failed to enable IAM database authentication for RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_automatic_backups(self, details):
        """
        Remediate RDS instances by enabling automatic backups with a 7-day retention period.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            region = details.get("region", "")

            if not db_instance_id or not region:
                return False, "Missing DB instance ID or region"

            # Enable automatic backups with a 7-day retention period
            await self.client.modify_db_instance(
                DBInstanceIdentifier=db_instance_id,
                BackupRetentionPeriod=7,
                PreferredBackupWindow='03:00-05:00',  # 3-5 AM in the instance's timezone
                ApplyImmediately=True
            )

            logger.info(f"Successfully enabled automatic backups for RDS instance {db_instance_id}")
            return True, f"Successfully enabled automatic backups for RDS instance {db_instance_id}"
        except Exception as e:
            error_msg = f"Failed to enable automatic backups for RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_cluster_iam_authentication(self, details):
        """
        Remediate RDS clusters by enabling IAM database authentication.
        """
        try:
            cluster_id = details.get("cluster_id", "")
            region = details.get("region", "")

            if not cluster_id or not region:
                return False, "Missing cluster ID or region"

            # Enable IAM database authentication for the cluster
            await self.client.modify_db_cluster(
                DBClusterIdentifier=cluster_id,
                EnableIAMDatabaseAuthentication=True,
                ApplyImmediately=True
            )

            logger.info(f"Successfully enabled IAM database authentication for RDS cluster {cluster_id}")
            return True, f"Successfully enabled IAM database authentication for RDS cluster {cluster_id}"
        except Exception as e:
            error_msg = f"Failed to enable IAM database authentication for RDS cluster {cluster_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_aurora_mysql_audit_logging(self, details):
        """
        Remediate Aurora MySQL clusters by enabling audit logging.
        """
        try:
            cluster_id = details.get("cluster_id", "")
            region = details.get("region", "")

            if not cluster_id or not region:
                return False, "Missing cluster ID or region"

            # Get current log exports
            cluster_info = await self.client.describe_db_clusters(DBClusterIdentifier=cluster_id)
            if not cluster_info.get("DBClusters"):
                return False, f"Cluster {cluster_id} not found"

            current_logs = cluster_info["DBClusters"][0].get("EnabledCloudwatchLogsExports", [])

            # Add audit log if not already enabled
            if "audit" not in current_logs:
                current_logs.append("audit")

                # Enable audit logging
                await self.client.modify_db_cluster(
                    DBClusterIdentifier=cluster_id,
                    CloudwatchLogsExportConfiguration={
                        'EnableLogTypes': current_logs
                    },
                    ApplyImmediately=True
                )

            logger.info(f"Successfully enabled audit logging for Aurora MySQL cluster {cluster_id}")
            return True, f"Successfully enabled audit logging for Aurora MySQL cluster {cluster_id}"
        except Exception as e:
            error_msg = f"Failed to enable audit logging for Aurora MySQL cluster {cluster_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_cluster_auto_minor_version(self, details):
        """
        Remediate RDS clusters by enabling automatic minor version upgrades.
        """
        try:
            cluster_id = details.get("cluster_id", "")
            region = details.get("region", "")

            if not cluster_id or not region:
                return False, "Missing cluster ID or region"

            # Enable automatic minor version upgrades for the cluster
            await self.client.modify_db_cluster(
                DBClusterIdentifier=cluster_id,
                AutoMinorVersionUpgrade=True,
                ApplyImmediately=True
            )

            logger.info(f"Successfully enabled automatic minor version upgrades for RDS cluster {cluster_id}")
            return True, f"Successfully enabled automatic minor version upgrades for RDS cluster {cluster_id}"
        except Exception as e:
            error_msg = f"Failed to enable automatic minor version upgrades for RDS cluster {cluster_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_postgresql_logging(self, details):
        """
        Remediate PostgreSQL RDS instances by enabling logging to CloudWatch Logs.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            region = details.get("region", "")

            if not db_instance_id or not region:
                return False, "Missing DB instance ID or region"

            # Get current log exports
            instance_info = await self.client.describe_db_instances(DBInstanceIdentifier=db_instance_id)
            if not instance_info.get("DBInstances"):
                return False, f"Instance {db_instance_id} not found"

            current_logs = instance_info["DBInstances"][0].get("EnabledCloudwatchLogsExports", [])

            # Add PostgreSQL logs if not already enabled
            logs_to_enable = []
            if "postgresql" not in current_logs:
                logs_to_enable.append("postgresql")
            if "upgrade" not in current_logs:
                logs_to_enable.append("upgrade")

            if logs_to_enable:
                # Enable PostgreSQL logging
                await self.client.modify_db_instance(
                    DBInstanceIdentifier=db_instance_id,
                    CloudwatchLogsExportConfiguration={
                        'EnableLogTypes': logs_to_enable
                    },
                    ApplyImmediately=True
                )

            logger.info(f"Successfully enabled PostgreSQL logging for RDS instance {db_instance_id}")
            return True, f"Successfully enabled PostgreSQL logging for RDS instance {db_instance_id}"
        except Exception as e:
            error_msg = f"Failed to enable PostgreSQL logging for RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_multi_az(self, details):
        """
        Remediate RDS instances by enabling Multi-AZ deployment.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            region = details.get("region", "")

            if not db_instance_id or not region:
                return False, "Missing DB instance ID or region"

            # Enable Multi-AZ deployment
            await self.client.modify_db_instance(
                DBInstanceIdentifier=db_instance_id,
                MultiAZ=True,
                ApplyImmediately=True
            )

            logger.info(f"Successfully enabled Multi-AZ deployment for RDS instance {db_instance_id}")
            return True, f"Successfully enabled Multi-AZ deployment for RDS instance {db_instance_id}"
        except Exception as e:
            error_msg = f"Failed to enable Multi-AZ deployment for RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_event_notifications(self, details):
        """
        Remediate RDS event notifications by creating event subscriptions for critical events.
        """
        try:
            region = details.get("region", "")

            if not region:
                return False, "Missing region"

            # Create an SNS topic for RDS notifications if it doesn't exist
            sns_client = self.get_session().client('sns', region_name=region)
            topic_name = "rds-critical-events"

            try:
                # Check if topic already exists
                topics = await sns_client.list_topics()
                topic_arn = None

                for topic in topics.get("Topics", []):
                    if topic_name in topic.get("TopicArn", ""):
                        topic_arn = topic["TopicArn"]
                        break

                if not topic_arn:
                    # Create new topic
                    topic_response = await sns_client.create_topic(Name=topic_name)
                    topic_arn = topic_response["TopicArn"]
            except Exception as e:
                logger.error(f"Failed to create SNS topic: {str(e)}")
                return False, f"Failed to create SNS topic: {str(e)}"

            # Create event subscription for critical cluster events
            subscription_name = "rds-critical-cluster-events"
            await self.client.create_event_subscription(
                SubscriptionName=subscription_name,
                SnsTopicArn=topic_arn,
                SourceType="db-cluster",
                EventCategories=["failover", "maintenance", "configuration change", "deletion"],
                Enabled=True
            )

            # Create event subscription for critical instance events
            subscription_name = "rds-critical-instance-events"
            await self.client.create_event_subscription(
                SubscriptionName=subscription_name,
                SnsTopicArn=topic_arn,
                SourceType="db-instance",
                EventCategories=["failover", "maintenance", "configuration change", "deletion"],
                Enabled=True
            )

            # Create event subscription for parameter group events
            subscription_name = "rds-parameter-group-events"
            await self.client.create_event_subscription(
                SubscriptionName=subscription_name,
                SnsTopicArn=topic_arn,
                SourceType="db-parameter-group",
                EventCategories=["configuration change", "pending reboot"],
                Enabled=True
            )

            logger.info("Successfully created RDS event subscriptions for critical events")
            return True, "Successfully created RDS event subscriptions for critical events"
        except Exception as e:
            error_msg = f"Failed to create RDS event subscriptions: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_instance_deletion_protection(self, details):
        """
        Remediate RDS instances by enabling deletion protection.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            region = details.get("region", "")

            if not db_instance_id or not region:
                return False, "Missing DB instance ID or region"

            # Enable deletion protection
            await self.client.modify_db_instance(
                DBInstanceIdentifier=db_instance_id,
                DeletionProtection=True,
                ApplyImmediately=True
            )

            logger.info(f"Successfully enabled deletion protection for RDS instance {db_instance_id}")
            return True, f"Successfully enabled deletion protection for RDS instance {db_instance_id}"
        except Exception as e:
            error_msg = f"Failed to enable deletion protection for RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_instance_logs_to_cloudwatch(self, details):
        """
        Remediate RDS instances by enabling logging to CloudWatch Logs.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            region = details.get("region", "")

            if not db_instance_id or not region:
                return False, "Missing DB instance ID or region"

            # Get instance details to determine engine type
            instance_info = await self.client.describe_db_instances(DBInstanceIdentifier=db_instance_id)
            if not instance_info.get("DBInstances"):
                return False, f"Instance {db_instance_id} not found"

            engine = instance_info["DBInstances"][0].get("Engine", "").lower()

            # Determine which logs to enable based on engine type
            logs_to_enable = []
            if engine == "mysql" or engine == "mariadb":
                logs_to_enable = ["error", "general", "slowquery"]
            elif engine == "postgres" or engine == "aurora-postgresql":
                logs_to_enable = ["postgresql", "upgrade"]
            elif engine == "oracle":
                logs_to_enable = ["alert", "audit", "listener", "trace"]
            elif engine == "sqlserver":
                logs_to_enable = ["error", "agent"]

            if logs_to_enable:
                # Enable logging to CloudWatch
                await self.client.modify_db_instance(
                    DBInstanceIdentifier=db_instance_id,
                    CloudwatchLogsExportConfiguration={
                        'EnableLogTypes': logs_to_enable
                    },
                    ApplyImmediately=True
                )

            logger.info(f"Successfully enabled CloudWatch logging for RDS instance {db_instance_id}")
            return True, f"Successfully enabled CloudWatch logging for RDS instance {db_instance_id}"
        except Exception as e:
            error_msg = f"Failed to enable CloudWatch logging for RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_mysql_encryption_in_transit(self, details):
        """
        Remediate MySQL RDS instances by enforcing SSL/TLS encryption for database connections.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            region = details.get("region", "")

            if not db_instance_id or not region:
                return False, "Missing DB instance ID or region"

            # Get the parameter group associated with the instance
            instance_info = await self.client.describe_db_instances(DBInstanceIdentifier=db_instance_id)
            if not instance_info.get("DBInstances"):
                return False, f"Instance {db_instance_id} not found"

            parameter_group_name = instance_info["DBInstances"][0].get("DBParameterGroups", [{}])[0].get(
                "DBParameterGroupName")

            if not parameter_group_name:
                return False, "No parameter group found for the instance"

            # Modify the parameter group to require SSL
            await self.client.modify_db_parameter_group(
                DBParameterGroupName=parameter_group_name,
                Parameters=[
                    {
                        'ParameterName': 'require_secure_transport',
                        'ParameterValue': 'ON',
                        'ApplyMethod': 'immediate'
                    }
                ]
            )

            logger.info(f"Successfully enforced SSL/TLS encryption for RDS instance {db_instance_id}")
            return True, f"Successfully enforced SSL/TLS encryption for RDS instance {db_instance_id}"
        except Exception as e:
            error_msg = f"Failed to enforce SSL/TLS encryption for RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_cluster_copy_tags_to_snapshots(self, details):
        """
        Remediate RDS clusters by enabling copy tags to snapshots.
        """
        try:
            cluster_id = details.get("cluster_id", "")
            region = details.get("region", "")

            if not cluster_id or not region:
                return False, "Missing cluster ID or region"

            # Enable copy tags to snapshots for the cluster
            await self.client.modify_db_cluster(
                DBClusterIdentifier=cluster_id,
                CopyTagsToSnapshot=True,
                ApplyImmediately=True
            )

            logger.info(f"Successfully enabled copy tags to snapshots for RDS cluster {cluster_id}")
            return True, f"Successfully enabled copy tags to snapshots for RDS cluster {cluster_id}"
        except Exception as e:
            error_msg = f"Failed to enable copy tags to snapshots for RDS cluster {cluster_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_instance_vpc_deployment(self, details):
        """
        Remediate RDS instances by ensuring they are deployed within a VPC.
        Note: This is a complex operation that may require creating a new instance.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            region = details.get("region", "")

            if not db_instance_id or not region:
                return False, "Missing DB instance ID or region"

            # Check if instance exists and get current configuration
            try:
                instance_info = await self.client.describe_db_instances(DBInstanceIdentifier=db_instance_id)
                if not instance_info.get("DBInstances"):
                    return False, f"Instance {db_instance_id} not found"

                # Check if already in VPC
                current_instance = instance_info["DBInstances"][0]
                if current_instance.get("DBSubnetGroup"):
                    return True, f"RDS instance {db_instance_id} is already deployed in a VPC"
            except Exception as e:
                return False, f"Failed to retrieve instance information: {str(e)}"

            # This remediation requires creating a new instance in a VPC and migrating data
            # This is a complex operation that may require manual intervention
            return False, f"Moving RDS instance {db_instance_id} to a VPC requires manual intervention. Please create a new instance in a VPC and migrate your data."
        except Exception as e:
            error_msg = f"Failed to remediate VPC deployment for RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_postgresql_encryption_in_transit(self, details):
        """
        Remediate PostgreSQL RDS instances by enforcing SSL/TLS encryption for database connections.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            region = details.get("region", "")

            if not db_instance_id or not region:
                return False, "Missing DB instance ID or region"

            # Get the parameter group associated with the instance
            instance_info = await self.client.describe_db_instances(DBInstanceIdentifier=db_instance_id)
            if not instance_info.get("DBInstances"):
                return False, f"Instance {db_instance_id} not found"

            parameter_group_name = instance_info["DBInstances"][0].get("DBParameterGroups", [{}])[0].get(
                "DBParameterGroupName")

            if not parameter_group_name:
                return False, "No parameter group found for the instance"

            # Modify the parameter group to require SSL
            await self.client.modify_db_parameter_group(
                DBParameterGroupName=parameter_group_name,
                Parameters=[
                    {
                        'ParameterName': 'rds.force_ssl',
                        'ParameterValue': '1',
                        'ApplyMethod': 'immediate'
                    }
                ]
            )

            logger.info(f"Successfully enabled SSL requirement for PostgreSQL RDS instance {db_instance_id}")
            return True, f"Successfully enabled SSL requirement for PostgreSQL RDS instance {db_instance_id}"
        except Exception as e:
            error_msg = f"Failed to enable SSL requirement for PostgreSQL RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_custom_admin(self, details):
        """
        Remediate RDS instances by creating a new admin user and disabling the default admin.
        Note: This is a complex operation that requires creating a new admin user and updating applications.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            region = details.get("region", "")
            engine = details.get("engine", "")

            if not db_instance_id or not region or not engine:
                return False, "Missing DB instance ID, region, or engine"

            # Check if instance exists
            try:
                instance_info = await self.client.describe_db_instances(DBInstanceIdentifier=db_instance_id)
                if not instance_info.get("DBInstances"):
                    return False, f"Instance {db_instance_id} not found"

                # Get current admin username
                current_instance = instance_info["DBInstances"][0]
                current_admin = current_instance.get("MasterUsername", "")

                # Check if already using a custom admin
                if current_admin.lower() not in DEFAULT_RDS_ADMIN_USERNAMES:
                    return True, f"RDS instance {db_instance_id} is already using a custom admin username"

            except Exception as e:
                return False, f"Failed to retrieve instance information: {str(e)}"

            # This remediation requires creating a new admin user and updating applications
            # This is a complex operation that requires manual intervention
            return False, f"Changing the admin username for RDS instance {db_instance_id} requires manual intervention. Please create a new admin user, update application connection strings, and consider rotating the master password."
        except Exception as e:
            error_msg = f"Failed to remediate custom admin for RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_aurora_cluster_backtracking(self, details):
        """
        Remediate Aurora clusters by enabling backtracking.
        """
        try:
            cluster_id = details.get("cluster_id", "")
            region = details.get("region", "")

            if not cluster_id or not region:
                return False, "Missing cluster ID or region"

            # Check if cluster exists and get current configuration
            try:
                cluster_info = await self.client.describe_db_clusters(DBClusterIdentifier=cluster_id)
                if not cluster_info.get("DBClusters"):
                    return False, f"Cluster {cluster_id} not found"

                # Check if already enabled
                current_cluster = cluster_info["DBClusters"][0]
                if current_cluster.get("BacktrackWindow", 0) > 0:
                    return True, f"Backtracking is already enabled for Aurora cluster {cluster_id}"

                # Check if the engine supports backtracking
                engine = current_cluster.get("Engine", "")
                if engine != "aurora" and engine != "aurora-mysql":
                    return False, f"Backtracking is only supported for Aurora MySQL clusters, not {engine}"

            except Exception as e:
                return False, f"Failed to retrieve cluster information: {str(e)}"

            # Enable backtracking with a 24-hour window (86400 seconds)
            await self.client.modify_db_cluster(
                DBClusterIdentifier=cluster_id,
                BacktrackWindow=86400,
                ApplyImmediately=True
            )

            logger.info(f"Successfully enabled backtracking for Aurora cluster {cluster_id}")
            return True, f"Successfully enabled backtracking for Aurora cluster {cluster_id}"
        except Exception as e:
            error_msg = f"Failed to enable backtracking for Aurora cluster {cluster_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_cluster_multi_az(self, details):
        """
        Remediate RDS clusters by enabling Multi-AZ deployment.
        """
        try:
            cluster_id = details.get("cluster_id", "")
            region = details.get("region", "")

            if not cluster_id or not region:
                return False, "Missing cluster ID or region"

            # Check if cluster exists and get current configuration
            try:
                cluster_info = await self.client.describe_db_clusters(DBClusterIdentifier=cluster_id)
                if not cluster_info.get("DBClusters"):
                    return False, f"Cluster {cluster_id} not found"

                # Check if already multi-AZ
                current_cluster = cluster_info["DBClusters"][0]
                availability_zones = current_cluster.get("AvailabilityZones", [])
                if len(availability_zones) > 1:
                    return True, f"RDS cluster {cluster_id} is already deployed across multiple AZs"

            except Exception as e:
                return False, f"Failed to retrieve cluster information: {str(e)}"

            # Enable Multi-AZ by adding a new AZ
            # For Aurora clusters, we need to add a new DB instance in a different AZ
            # This is a complex operation that may require manual intervention
            return False, f"Enabling Multi-AZ for RDS cluster {cluster_id} requires adding DB instances in different AZs. Please add at least one more instance in a different AZ."
        except Exception as e:
            error_msg = f"Failed to enable Multi-AZ for RDS cluster {cluster_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_cluster_encryption(self, details):
        """
        Remediate RDS clusters by enabling encryption.
        Note: This requires creating a new encrypted cluster and migrating data.
        """
        try:
            cluster_id = details.get("cluster_id", "")
            region = details.get("region", "")

            if not cluster_id or not region:
                return False, "Missing cluster ID or region"

            # Check if cluster exists and get current configuration
            try:
                cluster_info = await self.client.describe_db_clusters(DBClusterIdentifier=cluster_id)
                if not cluster_info.get("DBClusters"):
                    return False, f"Cluster {cluster_id} not found"

                # Check if already encrypted
                current_cluster = cluster_info["DBClusters"][0]
                if current_cluster.get("StorageEncrypted", False):
                    return True, f"RDS cluster {cluster_id} is already encrypted"

            except Exception as e:
                return False, f"Failed to retrieve cluster information: {str(e)}"

            # Encryption cannot be enabled on an existing cluster
            # This requires creating a new encrypted cluster and migrating data
            return False, f"Enabling encryption for RDS cluster {cluster_id} requires creating a new encrypted cluster and migrating data. This operation requires manual intervention."
        except Exception as e:
            error_msg = f"Failed to enable encryption for RDS cluster {cluster_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_instance_encryption(self, details):
        """
        Remediate RDS instances by enabling encryption.
        Note: This requires creating a new encrypted instance and migrating data.
        """
        try:
            db_instance_id = details.get("db_instance_id", "")
            region = details.get("region", "")

            if not db_instance_id or not region:
                return False, "Missing DB instance ID or region"

            # Check if instance exists and get current configuration
            try:
                instance_info = await self.client.describe_db_instances(DBInstanceIdentifier=db_instance_id)
                if not instance_info.get("DBInstances"):
                    return False, f"Instance {db_instance_id} not found"

                # Check if already encrypted
                current_instance = instance_info["DBInstances"][0]
                if current_instance.get("StorageEncrypted", False):
                    return True, f"RDS instance {db_instance_id} is already encrypted"

            except Exception as e:
                return False, f"Failed to retrieve instance information: {str(e)}"

            # Encryption cannot be enabled on an existing instance
            # This requires creating a new encrypted instance and migrating data
            return False, f"Enabling encryption for RDS instance {db_instance_id} requires creating a new encrypted instance and migrating data. This operation requires manual intervention."
        except Exception as e:
            error_msg = f"Failed to enable encryption for RDS instance {db_instance_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_snapshot_encryption(self, details):
        """
        Remediate RDS snapshots by creating encrypted copies.
        """
        try:
            snapshot_id = details.get("snapshot_id", "")
            region = details.get("region", "")

            if not snapshot_id or not region:
                return False, "Missing snapshot ID or region"

            # Check if snapshot exists and get current configuration
            try:
                snapshot_info = await self.client.describe_db_snapshots(DBSnapshotIdentifier=snapshot_id)
                if not snapshot_info.get("DBSnapshots"):
                    return False, f"Snapshot {snapshot_id} not found"

                # Check if already encrypted
                current_snapshot = snapshot_info["DBSnapshots"][0]
                if current_snapshot.get("Encrypted", False):
                    return True, f"RDS snapshot {snapshot_id} is already encrypted"

                # Get source DB instance identifier for the new snapshot name
                source_db_id = current_snapshot.get("DBInstanceIdentifier", "unknown")

            except Exception as e:
                return False, f"Failed to retrieve snapshot information: {str(e)}"

            # Create an encrypted copy of the snapshot
            account_id = self.credentials.get("aws_account_id", "")
            kms_key_id = f"arn:aws:kms:{region}:{account_id}:alias/aws/rds"

            encrypted_snapshot_id = f"{snapshot_id}-encrypted"

            await self.client.copy_db_snapshot(
                SourceDBSnapshotIdentifier=snapshot_id,
                TargetDBSnapshotIdentifier=encrypted_snapshot_id,
                KmsKeyId=kms_key_id,
                CopyTags=True
            )

            logger.info(f"Successfully created encrypted copy of RDS snapshot {snapshot_id} as {encrypted_snapshot_id}")
            return True, f"Successfully created encrypted copy of RDS snapshot {snapshot_id} as {encrypted_snapshot_id}"
        except Exception as e:
            error_msg = f"Failed to create encrypted copy of RDS snapshot {snapshot_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_instance_event_notifications(self, details):
        """
        Remediate RDS instance event notifications by creating event subscriptions for critical events.
        """
        try:
            region = details.get("region", "")

            if not region:
                return False, "Missing region"

            # Check if critical event subscriptions already exist
            try:
                event_subscriptions = await self.client.describe_event_subscriptions()

                # Check for existing instance event subscriptions
                for subscription in event_subscriptions.get("EventSubscriptionsList", []):
                    source_type = subscription.get("SourceType", "")
                    event_categories = set(subscription.get("EventCategoriesList", []))

                    critical_events = {"failure", "failover", "maintenance", "configuration change"}
                    if source_type == "db-instance" and critical_events.intersection(event_categories):
                        return True, "Critical RDS instance event notifications are already configured"

            except Exception as e:
                return False, f"Failed to retrieve event subscriptions: {str(e)}"

            # Create an SNS topic for notifications if it doesn't exist
            account_id = self.credentials.get("aws_account_id", "")
            sns_client = self.get_client("sns", region)

            topic_name = "rds-critical-events"
            topic_arn = f"arn:aws:sns:{region}:{account_id}:{topic_name}"

            try:
                await sns_client.create_topic(Name=topic_name)
            except Exception:
                # Topic might already exist
                pass

            # Create event subscription for critical instance events
            subscription_name = "rds-instance-critical-events"

            await self.client.create_event_subscription(
                SubscriptionName=subscription_name,
                SnsTopicArn=topic_arn,
                SourceType="db-instance",
                EventCategories=["failure", "failover", "maintenance", "configuration change"],
                Enabled=True
            )

            logger.info(f"Successfully created event subscription for critical RDS instance events")
            return True, f"Successfully created event subscription for critical RDS instance events"
        except Exception as e:
            error_msg = f"Failed to create event subscription for critical RDS instance events: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_parameter_group_event_notifications(self, details):
        """
        Remediate RDS parameter group event notifications by creating event subscriptions for critical events.
        """
        try:
            region = details.get("region", "")

            if not region:
                return False, "Missing region"

            # Check if critical event subscriptions already exist
            try:
                event_subscriptions = await self.client.describe_event_subscriptions()

                # Check for existing parameter group event subscriptions
                for subscription in event_subscriptions.get("EventSubscriptionsList", []):
                    source_type = subscription.get("SourceType", "")
                    event_categories = set(subscription.get("EventCategoriesList", []))

                    critical_events = {"configuration change", "pending reboot"}
                    if source_type == "db-parameter-group" and critical_events.intersection(event_categories):
                        return True, "Critical RDS parameter group event notifications are already configured"

            except Exception as e:
                return False, f"Failed to retrieve event subscriptions: {str(e)}"

            # Create an SNS topic for notifications if it doesn't exist
            account_id = self.credentials.get("aws_account_id", "")
            sns_client = self.get_client("sns", region)

            topic_name = "rds-critical-events"
            topic_arn = f"arn:aws:sns:{region}:{account_id}:{topic_name}"

            try:
                await sns_client.create_topic(Name=topic_name)
            except Exception:
                # Topic might already exist
                pass

            # Create event subscription for critical parameter group events
            subscription_name = "rds-parameter-group-critical-events"

            await self.client.create_event_subscription(
                SubscriptionName=subscription_name,
                SnsTopicArn=topic_arn,
                SourceType="db-parameter-group",
                EventCategories=["configuration change"],
                Enabled=True
            )

            logger.info(f"Successfully created event subscription for critical RDS parameter group events")
            return True, f"Successfully created event subscription for critical RDS parameter group events"
        except Exception as e:
            error_msg = f"Failed to create event subscription for critical RDS parameter group events: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_security_group_event_notifications(self, details):
        """
        Remediate RDS security group event notifications by creating event subscriptions for critical events.
        """
        try:
            region = details.get("region", "")

            if not region:
                return False, "Missing region"

            # Check if critical event subscriptions already exist
            try:
                event_subscriptions = await self.client.describe_event_subscriptions()

                # Check for existing security group event subscriptions
                for subscription in event_subscriptions.get("EventSubscriptionsList", []):
                    source_type = subscription.get("SourceType", "")
                    event_categories = set(subscription.get("EventCategoriesList", []))

                    critical_events = {"configuration change", "failure"}
                    if source_type == "db-security-group" and critical_events.intersection(event_categories):
                        return True, "Critical RDS security group event notifications are already configured"

            except Exception as e:
                return False, f"Failed to retrieve event subscriptions: {str(e)}"

            # Create an SNS topic for notifications if it doesn't exist
            account_id = self.credentials.get("aws_account_id", "")
            sns_client = self.get_client("sns", region)

            topic_name = "rds-critical-events"
            topic_arn = f"arn:aws:sns:{region}:{account_id}:{topic_name}"

            try:
                await sns_client.create_topic(Name=topic_name)
            except Exception:
                # Topic might already exist
                pass

            # Create event subscription for critical security group events
            subscription_name = "rds-security-group-critical-events"

            await self.client.create_event_subscription(
                SubscriptionName=subscription_name,
                SnsTopicArn=topic_arn,
                SourceType="db-security-group",
                EventCategories=["configuration change", "failure"],
                Enabled=True
            )

            logger.info(f"Successfully created event subscription for critical RDS security group events")
            return True, f"Successfully created event subscription for critical RDS security group events"
        except Exception as e:
            error_msg = f"Failed to create event subscription for critical RDS security group events: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_cluster_deletion_protection(self, details):
        """
        Remediate RDS clusters by enabling deletion protection.
        """
        try:
            cluster_id = details.get("cluster_id", "")
            region = details.get("region", "")

            if not cluster_id or not region:
                return False, "Missing cluster ID or region"

            # Check if cluster exists and get current configuration
            try:
                cluster_info = await self.client.describe_db_clusters(DBClusterIdentifier=cluster_id)
                if not cluster_info.get("DBClusters"):
                    return False, f"Cluster {cluster_id} not found"

                # Check if deletion protection is already enabled
                current_cluster = cluster_info["DBClusters"][0]
                if current_cluster.get("DeletionProtection", False):
                    return True, f"Deletion protection is already enabled for RDS cluster {cluster_id}"

            except Exception as e:
                return False, f"Failed to retrieve cluster information: {str(e)}"

            # Enable deletion protection
            await self.client.modify_db_cluster(
                DBClusterIdentifier=cluster_id,
                DeletionProtection=True,
                ApplyImmediately=True
            )

            logger.info(f"Successfully enabled deletion protection for RDS cluster {cluster_id}")
            return True, f"Successfully enabled deletion protection for RDS cluster {cluster_id}"
        except Exception as e:
            error_msg = f"Failed to enable deletion protection for RDS cluster {cluster_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_rds_cluster_custom_admin(self, details):
        """
        Remediate RDS clusters by creating a new admin user and disabling the default admin.
        Note: This is a complex operation that requires creating a new admin user and updating applications.
        """
        try:
            cluster_id = details.get("cluster_id", "")
            region = details.get("region", "")
            engine = details.get("engine", "")

            if not cluster_id or not region or not engine:
                return False, "Missing cluster ID, region, or engine"

            # Check if cluster exists
            try:
                cluster_info = await self.client.describe_db_clusters(DBClusterIdentifier=cluster_id)
                if not cluster_info.get("DBClusters"):
                    return False, f"Cluster {cluster_id} not found"

                # Get current admin username
                current_cluster = cluster_info["DBClusters"][0]
                current_admin = current_cluster.get("MasterUsername", "")

                # Check if already using a custom admin
                if current_admin.lower() not in DEFAULT_RDS_ADMIN_USERNAMES:
                    return True, f"RDS cluster {cluster_id} is already using a custom admin username"

            except Exception as e:
                return False, f"Failed to retrieve cluster information: {str(e)}"

            # This remediation requires creating a new admin user and updating applications
            # This is a complex operation that requires manual intervention
            return False, f"Changing the admin username for RDS cluster {cluster_id} requires manual intervention. Please create a new admin user, update application connection strings, and consider rotating the master password."
        except Exception as e:
            error_msg = f"Failed to remediate custom admin for RDS cluster {cluster_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def get_remediation_functions(self):
        """
        Return a mapping of policy checks to remediation functions.
        """
        return {
            "rds_custom_admin": self.remediate_rds_custom_admin,
            "rds_mysql_encryption_in_transit": self.remediate_rds_mysql_encryption_in_transit,
            "rds_enhanced_monitoring": self.remediate_rds_enhanced_monitoring,
            "rds_custom_port": self.remediate_rds_custom_port,
            "rds_cluster_copy_tags_to_snapshots": self.remediate_rds_cluster_copy_tags_to_snapshots,
            "rds_instance_copy_tags_to_snapshots": self.remediate_rds_instance_copy_tags_to_snapshots,
            "rds_snapshot_privacy": self.remediate_rds_snapshot_privacy,
            "rds_instance_public_access": self.remediate_rds_instance_public_access,
            "rds_auto_minor_version_upgrade": self.remediate_rds_auto_minor_version_upgrade,
            "rds_instance_vpc_deployment": self.remediate_rds_instance_vpc_deployment,
            "rds_iam_authentication": self.remediate_rds_iam_authentication,
            "rds_automatic_backups": self.remediate_rds_automatic_backups,
            "rds_cluster_iam_authentication": self.remediate_rds_cluster_iam_authentication,
            "aurora_cluster_backtracking": self.remediate_aurora_cluster_backtracking,
            "rds_cluster_multi_az": self.remediate_rds_cluster_multi_az,
            "rds_cluster_encryption": self.remediate_rds_cluster_encryption,
            "rds_instance_encryption": self.remediate_rds_instance_encryption,
            "aurora_mysql_audit_logging": self.remediate_aurora_mysql_audit_logging,
            "rds_cluster_auto_minor_version_upgrade": self.remediate_rds_cluster_auto_minor_version,
            "rds_postgresql_logging": self.remediate_rds_postgresql_logging,
            "rds_postgresql_encryption_in_transit": self.remediate_rds_postgresql_encryption_in_transit,
            "rds_snapshot_encryption": self.remediate_rds_snapshot_encryption,
            "rds_multi_az": self.remediate_rds_multi_az,
            "rds_event_notifications": self.remediate_rds_event_notifications,
            "rds_instance_event_notifications": self.remediate_rds_instance_event_notifications,
            "rds_parameter_group_event_notifications": self.remediate_rds_parameter_group_event_notifications,
            "rds_security_group_event_notifications": self.remediate_rds_security_group_event_notifications,
            "rds_cluster_deletion_protection": self.remediate_rds_cluster_deletion_protection,
            "rds_instance_deletion_protection": self.remediate_rds_instance_deletion_protection,
            "rds_instance_logs_to_cloudwatch": self.remediate_rds_instance_logs_to_cloudwatch,
            "rds_cluster_custom_admin": self.remediate_rds_cluster_custom_admin,
        }

    async def remediate(self, policy_check, details):
        """
        Run the appropriate remediation function based on the policy check.
        Updates the details object with remediation results and returns it.
        """
        logger.info(f"Starting remediation for policy check: {policy_check}")
        logger.info(f"Details: {details}")

        # Create a copy of the details to update
        updated_details = details.copy()

        # Initialize AWS client
        session = self.get_session()
        async with session.client(AWSServiceNameEnum.RDS.value,
                                  region_name=details.get("region", "us-east-1")) as client:
            self.client = client

            # Get the appropriate remediation function
            remediation_functions = self.get_remediation_functions()
            if policy_check not in remediation_functions:
                error_msg = f"No remediation function found for policy check: {policy_check}"
                logger.warning(error_msg)

                # Update remediation results
                if policy_check in self.remediation_results:
                    self.remediation_results[policy_check]["status"] = False
                    self.remediation_results[policy_check]["message"] = error_msg

                # Add remediation failure information
                updated_details["remediate"] = {
                    "status": "fail",
                    "message": f"Error: {error_msg}"
                }

                return False, error_msg, updated_details

            # Call the appropriate remediation function
            remediation_function = remediation_functions[policy_check]
            success, message = await remediation_function(details)

            # Update remediation results
            if policy_check in self.remediation_results:
                self.remediation_results[policy_check]["status"] = success
                self.remediation_results[policy_check]["message"] = message

            # Add remediation information to details
            updated_details["remediate"] = {
                "status": "pass" if success else "fail",
                "message": message
            }

            # If successful, update field values in details
            if success and policy_check in self.remediation_results:
                for field, value in self.remediation_results[policy_check]["field_updates"].items():
                    updated_details[field] = value

                # Update compliance status
                updated_details["compliance"] = True

            return success, message, updated_details
