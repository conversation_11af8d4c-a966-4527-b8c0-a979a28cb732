from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "ebs_encryption": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.EBS_ENCRYPTION.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            volumes = (cached.get("volumes") or {}).get("Volumes", [])

            for volume in volumes:
                volume_id = volume.get("VolumeId")
                encrypted = volume.get("Encrypted")
                instance_id = None
                attachments = volume.get("Attachments") or []
                if attachments:
                    instance_id = attachments[0].get("InstanceId")

                if findings["ebs_encryption"]["status"] == ResourceComplianceStatusEnum.PASS.value and not encrypted:
                    findings["ebs_encryption"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["ebs_encryption"]["details"].append(
                    {
                        "volume_id": volume_id,
                        "encrypted": encrypted,
                        "region": region,
                        "instance_id": instance_id,
                        "compliance": True if encrypted else False,
                    }
                )
    

        return findings


    def remediate(self):
        pass


