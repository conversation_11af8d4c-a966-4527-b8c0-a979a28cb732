import asyncio
import datetime
from celery.utils.log import get_task_logger
from ..config import BaseChecksProcessor
from app.common import (AWSServiceNameEnum, SeverityEnum, ResourceComplianceStatusEnum,
                        EC2ChecksDescriptionEnum, STOPPED_EC2_INSTANCE_CLEANUP_TIME)

__all__ = ['ChecksProcessor']

logger = get_task_logger(__name__)


class ChecksProcessor(BaseChecksProcessor):
    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.instances = None
        self.findings = {
            "ebs_encryption": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.EBS_ENCRYPTION.value,
                "severity": SeverityEnum.HIGH.value
            },
            "imdsv2": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.IMDSV2.value,
                "severity": SeverityEnum.HIGH.value
            },
            "public_access": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.PUBLIC_ACCESS.value,
                "severity": SeverityEnum.CRITICAL.value
            },
            "termination_protection": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.TERMINATION_PROTECTION.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "vpc_endpoints": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.VPC_ENDPOINTS.value,
                "severity": SeverityEnum.HIGH.value
            },
            "vpc_block_igw_traffic": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.VPC_BLOCK_IGW_TRAFFIC.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "ebs_public_snapshots": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.EBS_PUBLIC_SNAPSHOTS.value,
                "severity": SeverityEnum.CRITICAL.value
            },
            "unrestricted_ports": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.UNRESTRICTED_PORTS.value,
                "severity": SeverityEnum.CRITICAL.value
            },
            "vpc_default_security_group": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.VPC_DEFAULT_SECURITY_GROUP.value,
                "severity": SeverityEnum.HIGH.value
            },
            "transit_gateway_auto_accept": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.TRANSIT_GATEWAY_AUTO_ACCEPT.value,
                "severity": SeverityEnum.HIGH.value
            },
            "subnet_launch_template_public_ip": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.SUBNET_LAUNCH_TEMPLATE_PUBLIC_IP.value,
                "severity": SeverityEnum.HIGH.value
            },
            "vpn_configuration": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.VPN_CONFIGURATION.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "nacl_unrestricted_ssh_rdp": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.NACL_UNRESTRICTED_SSH_RDP.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "paravirtual_instances": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.PARAVIRTUAL_INSTANCES.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "stopped_instances_cleanup": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.STOPPED_INSTANCES_CLEANUP.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "vpc_flow_logs": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.VPC_FLOW_LOGS.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "ebs_default_encryption": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.EBS_DEFAULT_ENCRYPTION.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "unused_nacls": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.UNUSED_NACLS.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "ec2_multiple_enis": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.EC2_MULTIPLE_ENIS.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "ec2_launch_templates_imdsv2": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.EC2_LAUNCH_TEMPLATES_IMDSV2.value,
                "severity": SeverityEnum.LOW.value
            },
            "ec2_client_vpn_logging": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.EC2_CLIENT_VPN_LOGGING.value,
                "severity": SeverityEnum.LOW.value
            }
        }

    async def get_instances_details(self):
        return await self.client.describe_instances()

    async def check_ebs_encryption(self):
        volumes = await self.client.describe_volumes()
        for volume in volumes["Volumes"]:
            volume_id = volume["VolumeId"]
            encrypted = volume["Encrypted"]
            instance_id = volume["Attachments"][0]["InstanceId"] if volume["Attachments"] else None
            if self.findings["ebs_encryption"]["status"] == ResourceComplianceStatusEnum.PASS.value and not encrypted:
                self.findings["ebs_encryption"]["status"] = ResourceComplianceStatusEnum.FAIL.value
            self.findings["ebs_encryption"]["details"].append({
                "volume_id": volume_id,
                "encrypted": encrypted,
                "region": self.client.meta.region_name,
                "instance_id": instance_id,
                "compliance": True if encrypted else False
            })

    async def check_imdsv2(self):
        for reservation in self.instances["Reservations"]:
            for instance in reservation["Instances"]:
                instance_id = instance["InstanceId"]
                imdsv2_enabled = instance.get("MetadataOptions", {}).get("HttpTokens") == "required"
                if self.findings["imdsv2"]["status"] == ResourceComplianceStatusEnum.PASS.value and not imdsv2_enabled:
                    self.findings["imdsv2"]["status"] = ResourceComplianceStatusEnum.FAIL.value
                self.findings["imdsv2"]["details"].append({
                    "instance_id": instance_id,
                    "imdsv2_enabled": imdsv2_enabled,
                    "region": self.client.meta.region_name,
                    "compliance": True if imdsv2_enabled else False
                })

    async def check_public_access(self):
        for reservation in self.instances["Reservations"]:
            for instance in reservation["Instances"]:
                instance_id = instance["InstanceId"]
                has_public_access = instance.get("PublicIpAddress") is not None
                if self.findings["public_access"]["status"] == ResourceComplianceStatusEnum.PASS.value and has_public_access:
                    self.findings["public_access"]["status"] = ResourceComplianceStatusEnum.FAIL.value
                self.findings["public_access"]["details"].append({
                    "instance_id": instance_id,
                    "has_public_access": has_public_access,
                    "region": self.client.meta.region_name,
                    "compliance": False if has_public_access else True
                })

    async def check_termination_protection(self):
        for reservation in self.instances["Reservations"]:
            for instance in reservation["Instances"]:
                instance_id = instance["InstanceId"]
                termination_protection = instance.get("DisableApiTermination", False)
                if (self.findings["termination_protection"]["status"] == ResourceComplianceStatusEnum.PASS.value and
                        not termination_protection):
                    self.findings["termination_protection"]["status"] = ResourceComplianceStatusEnum.FAIL.value
                self.findings["termination_protection"]["details"].append({
                    "instance_id": instance_id,
                    "termination_protection": termination_protection,
                    "region": self.client.meta.region_name,
                    "compliance": True if termination_protection else False
                })

    async def check_vpc_block_internet_gateway_traffic(self):
        """
        VPC-level Block Public Access settings are enabled to block internet traffic
        through the Internet Gateway (IGW)
        """
        # Get Internet Gateways in the region
        igw_response = await self.client.describe_internet_gateways()
        igw_attachments = {}

        for igw in igw_response.get("InternetGateways", []):
            for attachment in igw.get("Attachments", []):
                vpc_id = attachment.get("VpcId")
                if vpc_id:
                    igw_attachments[vpc_id] = igw["InternetGatewayId"]

        # Get route tables
        rt_response = await self.client.describe_route_tables()
        vpcs_with_public_routes = set()

        for rt in rt_response["RouteTables"]:
            for route in rt.get("Routes", []):
                if (
                        route.get("DestinationCidrBlock") == "0.0.0.0/0"
                        and route.get("GatewayId", "").startswith("igw-")
                ):
                    for assoc in rt.get("Associations", []):
                        if assoc.get("Main") or assoc.get("SubnetId"):
                            vpc_id = rt.get("VpcId")
                            if vpc_id:
                                vpcs_with_public_routes.add(vpc_id)

        # Compare VPCs with IGWs and public routes
        findings = []
        for vpc_id, igw_id in igw_attachments.items():
            is_exposed = vpc_id in vpcs_with_public_routes
            findings.append({
                "vpc_id": vpc_id,
                "internet_gateway_id": igw_id,
                "exposes_to_internet": is_exposed,
                "region": self.client.meta.region_name,
                "compliance": not is_exposed
            })

        # Set compliance based on whether any VPCs expose to the internet
        status = (
            ResourceComplianceStatusEnum.FAIL.value
            if any(f["exposes_to_internet"] for f in findings)
            else ResourceComplianceStatusEnum.PASS.value
        )

        self.findings["vpc_block_igw_traffic"]["status"] = status
        self.findings["vpc_block_igw_traffic"]["details"] = findings

    async def check_ebs_public_snapshots(self):
        """
        All Amazon EBS snapshots are private and not publicly accessible
        """
        # Get all EBS snapshots owned by the account
        snapshots_response = await self.client.describe_snapshots(OwnerIds=["self"])

        findings = []
        public_snapshots = []

        for snapshot in snapshots_response.get("Snapshots", []):
            snapshot_id = snapshot["SnapshotId"]

            # Check if the snapshot is publicly accessible
            attr_response = await self.client.describe_snapshot_attribute(
                SnapshotId=snapshot_id, Attribute="createVolumePermission"
            )
            public_access = any(
                perm.get("Group") == "all" for perm in attr_response.get("CreateVolumePermissions", [])
            )

            # Update findings
            findings.append({
                "snapshot_id": snapshot_id,
                "publicly_restorable": public_access,
                "region": self.client.meta.region_name,
                "compliance": not public_access
            })

            if public_access:
                public_snapshots.append(snapshot_id)

        # Determine compliance status
        status = (
            ResourceComplianceStatusEnum.FAIL.value
            if public_snapshots else ResourceComplianceStatusEnum.PASS.value
        )

        self.findings["ebs_public_snapshots"]["status"] = status
        self.findings["ebs_public_snapshots"]["details"] = findings

    async def check_unrestricted_ports(self):
        """
        AWS security groups should not allow unrestricted access to high-risk ports or unauthorized ports
        """
        # Define high-risk ports and authorized ports
        high_risk_ports = [22, 3389, 3306, 5432, 27017, 6379, 5900, 9200]
        authorized_ports = {80, 443, 123, 53}

        # Get all security groups
        security_groups = await self.client.describe_security_groups()

        findings = []
        vulnerable_sgs = []

        for sg in security_groups.get("SecurityGroups", []):
            sg_id = sg["GroupId"]
            sg_name = sg["GroupName"]
            vpc_id = sg["VpcId"]
            
            # Track violations for this security group
            high_risk_violations = []
            unauthorized_violations = []
            has_violations = False

            # Check inbound rules for unrestricted access
            for rule in sg.get("IpPermissions", []):
                port = rule.get("FromPort")

                if port:
                    # Check for unrestricted access (0.0.0.0/0 or ::/0)
                    is_unrestricted = (
                        any(ip_range.get("CidrIp") == "0.0.0.0/0" for ip_range in rule.get("IpRanges", [])) or
                        any(ipv6_range.get("CidrIpv6") == "::/0" for ipv6_range in rule.get("Ipv6Ranges", []))
                    )
                    
                    if is_unrestricted:
                        is_high_risk = port in high_risk_ports
                        is_unauthorized = port not in authorized_ports
                        
                        if is_high_risk:
                            high_risk_violations.append(port)
                            has_violations = True
                        elif is_unauthorized:
                            unauthorized_violations.append(port)
                            has_violations = True

            # Add finding for this security group
            findings.append({
                "security_group_id": sg_id,
                "security_group_name": sg_name,
                "vpc_id": vpc_id,
                "has_violations": has_violations,
                "compliance": not has_violations,
                "region": self.client.meta.region_name
            })

            if has_violations:
                vulnerable_sgs.append(sg_id)

        # Determine compliance status
        status = (
            ResourceComplianceStatusEnum.FAIL.value
            if vulnerable_sgs else ResourceComplianceStatusEnum.PASS.value
        )

        self.findings["unrestricted_ports"]["status"] = status
        self.findings["unrestricted_ports"]["details"] = findings

    async def check_vpc_default_security_group_rules(self):
        """
        The default security groups (SGs) in all VPCs are not allowing any inbound or outbound traffic
        """
        # Get all VPCs
        vpcs_response = await self.client.describe_vpcs()
        vpc_ids = [vpc["VpcId"] for vpc in vpcs_response.get("Vpcs", [])]

        findings = []
        non_compliant_sgs = []

        for vpc_id in vpc_ids:
            # Get the default security group for the VPC
            sg_response = await self.client.describe_security_groups(
                Filters=[{"Name": "group-name", "Values": ["default"]},
                         {"Name": "vpc-id", "Values": [vpc_id]}]
            )

            for sg in sg_response.get("SecurityGroups", []):
                sg_id = sg["GroupId"]
                inbound_rules = sg.get("IpPermissions", [])
                outbound_rules = sg.get("IpPermissionsEgress", [])

                # Check if the default SG has any inbound or outbound rules
                if inbound_rules or outbound_rules:
                    findings.append({
                        "vpc_id": vpc_id,
                        "security_group_id": sg_id,
                        "inbound_rules": len(inbound_rules),
                        "outbound_rules": len(outbound_rules),
                        "compliance": False,
                        "region": self.client.meta.region_name
                    })
                    non_compliant_sgs.append(sg_id)

        # Determine compliance status
        status = (
            ResourceComplianceStatusEnum.FAIL.value
            if non_compliant_sgs else ResourceComplianceStatusEnum.PASS.value
        )

        self.findings["vpc_default_security_group"]["status"] = status
        self.findings["vpc_default_security_group"]["details"] = findings

    async def check_transit_gateway_auto_accept(self):
        """
        AWS Transit Gateways (TGWs) are configured to manually review and accept attachment requests instead of
        automatically allowing VPCs to connect
        """
        # Get all Transit Gateways
        tgw_response = await self.client.describe_transit_gateways()

        findings = []
        non_compliant_tgws = []

        for tgw in tgw_response.get("TransitGateways", []):
            tgw_id = tgw["TransitGatewayId"]
            auto_accept = tgw["Options"]["AutoAcceptSharedAttachments"]

            # Check if auto-accept is enabled
            if auto_accept == "enable":
                findings.append({
                    "transit_gateway_id": tgw_id,
                    "auto_accept": auto_accept,
                    "compliance": False,
                    "region": self.client.meta.region_name
                })
                non_compliant_tgws.append(tgw_id)

        # Determine compliance status
        status = (
            ResourceComplianceStatusEnum.FAIL.value
            if non_compliant_tgws else ResourceComplianceStatusEnum.PASS.value
        )

        self.findings["transit_gateway_auto_accept"]["status"] = status
        self.findings["transit_gateway_auto_accept"]["details"] = findings

    async def check_vpn_configuration(self):
        """
        AWS Site-to-Site VPN connections should have logging enabled and all tunnels in UP state
        """
        # Get all VPN connections
        vpn_response = await self.client.describe_vpn_connections()

        findings = []
        non_compliant_vpns = []

        for vpn in vpn_response.get("VpnConnections", []):
            vpn_id = vpn["VpnConnectionId"]
            logging_options = vpn.get("Options", {}).get("CloudWatchLogOptions", {})
            tunnel_statuses = [tunnel["Status"] for tunnel in vpn["VgwTelemetry"]]

            # Check if logging is enabled
            log_enabled = logging_options.get("LogEnabled", False)
            
            # Check if any tunnel is down
            has_down_tunnel = "DOWN" in tunnel_statuses

            compliance = log_enabled and not has_down_tunnel

            findings.append({
                "vpn_connection_id": vpn_id,
                "logging_enabled": log_enabled,
                "has_down_tunnel": has_down_tunnel,
                "compliance": compliance,
                "region": self.client.meta.region_name
            })

            if not compliance:
                non_compliant_vpns.append(vpn_id)

        # Determine compliance status
        status = (
            ResourceComplianceStatusEnum.FAIL.value
            if non_compliant_vpns else ResourceComplianceStatusEnum.PASS.value
        )

        self.findings["vpn_configuration"]["status"] = status
        self.findings["vpn_configuration"]["details"] = findings

    async def check_nacl_unrestricted_ssh_rdp(self):
        """
        Network ACLs (NACLs) do not allow unrestricted inbound access to SSH (port 22) or RDP (port 3389)
        from anywhere on the internet
        """
        # Get all network ACLs
        nacl_response = await self.client.describe_network_acls()

        findings = []
        non_compliant_nacls = []

        for nacl in nacl_response.get("NetworkAcls", []):
            nacl_id = nacl["NetworkAclId"]

            for entry in nacl.get("Entries", []):
                # Check if the rule allows traffic (not DENY)
                if entry["RuleAction"] == "allow":
                    # Check if the rule allows ingress (inbound traffic)
                    if entry["Egress"] is False:
                        # Check if the rule allows traffic from anywhere (0.0.0.0/0)
                        if entry["CidrBlock"] == "0.0.0.0/0":
                            # Check if the rule applies to port 22 (SSH) or 3389 (RDP)
                            port_range = entry.get("PortRange", {})
                            if port_range.get("From") in [22, 3389] or port_range.get("To") in [22, 3389]:
                                findings.append({
                                    "nacl_id": nacl_id,
                                    "rule_number": entry["RuleNumber"],
                                    "protocol": entry["Protocol"],
                                    "port_from": port_range.get("From"),
                                    "port_to": port_range.get("To"),
                                    "compliance": False,
                                    "region": self.client.meta.region_name,
                                })
                                non_compliant_nacls.append(nacl_id)

        # Determine compliance status
        status = (
            ResourceComplianceStatusEnum.FAIL.value
            if non_compliant_nacls else ResourceComplianceStatusEnum.PASS.value
        )

        self.findings["nacl_unrestricted_ssh_rdp"]["status"] = status
        self.findings["nacl_unrestricted_ssh_rdp"]["details"] = findings

    async def check_paravirtual_instances(self):
        """
        EC2 instances should not use Paravirtual virtualization type
        """
        # Get all EC2 instances
        instances_response = await self.client.describe_instances()

        findings = []
        non_compliant_instances = []

        for reservation in instances_response.get("Reservations", []):
            for instance in reservation.get("Instances", []):
                instance_id = instance["InstanceId"]
                virtualization_type = instance.get("VirtualizationType", "")
                instance_type = instance["InstanceType"]

                # Check if the instance is Paravirtual
                if virtualization_type.lower() == "paravirtual":
                    findings.append({
                        "instance_id": instance_id,
                        "virtualization_type": virtualization_type,
                        "instance_type": instance_type,
                        "compliance": False,
                        "region": self.client.meta.region_name
                    })
                    non_compliant_instances.append(instance_id)
                else:
                    findings.append({
                        "instance_id": instance_id,
                        "virtualization_type": virtualization_type,
                        "instance_type": instance_type,
                        "compliance": True,
                        "region": self.client.meta.region_name
                    })

        # Determine compliance status
        status = (
            ResourceComplianceStatusEnum.FAIL.value
            if non_compliant_instances else ResourceComplianceStatusEnum.PASS.value
        )

        self.findings["paravirtual_instances"]["status"] = status
        self.findings["paravirtual_instances"]["details"] = findings

    async def check_stopped_instances_cleanup(self):
        """
        EC2 instances that have been stopped for more than the specified cleanup time should be terminated
        """
        # Get all EC2 instances
        instances_response = await self.client.describe_instances()
        current_time = datetime.datetime.utcnow()

        findings = []
        non_compliant_instances = []

        for reservation in instances_response.get("Reservations", []):
            for instance in reservation.get("Instances", []):
                instance_id = instance["InstanceId"]
                state = instance["State"]["Name"]
                launch_time = instance["LaunchTime"]
                instance_type = instance["InstanceType"]

                # If the instance is stopped, calculate how long it has been stopped
                if state == "stopped":
                    stopped_days = (current_time - launch_time.replace(tzinfo=None)).days

                    if stopped_days > STOPPED_EC2_INSTANCE_CLEANUP_TIME:
                        findings.append({
                            "instance_id": instance_id,
                            "stopped_days": stopped_days,
                            "instance_type": instance_type,
                            "compliance": False,
                            "region": self.client.meta.region_name
                        })
                        non_compliant_instances.append(instance_id)
                    else:
                        findings.append({
                            "instance_id": instance_id,
                            "stopped_days": stopped_days,
                            "instance_type": instance_type,
                            "compliance": True,
                            "region": self.client.meta.region_name
                        })
                else:
                    findings.append({
                        "instance_id": instance_id,
                        "stopped_days": 0,
                        "instance_type": instance_type,
                        "compliance": True,
                        "region": self.client.meta.region_name
                    })

        # Determine compliance status
        status = (
            ResourceComplianceStatusEnum.FAIL.value
            if non_compliant_instances else ResourceComplianceStatusEnum.PASS.value
        )

        self.findings["stopped_instances_cleanup"]["status"] = status
        self.findings["stopped_instances_cleanup"]["details"] = findings

    async def check_vpc_flow_logs(self):
        """
        VPC Flow Logs are enabled for all VPCs in a region
        """
        # Get all VPCs
        vpcs_response = await self.client.describe_vpcs()
        vpcs = vpcs_response.get("Vpcs", [])

        findings = []
        non_compliant_vpcs = []

        for vpc in vpcs:
            vpc_id = vpc["VpcId"]

            # Check if Flow Logs are enabled for this VPC
            flow_logs_response = await self.client.describe_flow_logs(
                Filters=[{"Name": "resource-id", "Values": [vpc_id]}]
            )

            flow_logs = flow_logs_response.get("FlowLogs", [])

            # If no Flow Logs exist for the VPC, mark as non-compliant
            if not flow_logs:
                findings.append({
                    "vpc_id": vpc_id,
                    "region": self.client.meta.region_name,
                    "flow_logs_enabled": False,
                    "compliance": False
                })
                non_compliant_vpcs.append(vpc_id)
            else:
                findings.append({
                    "vpc_id": vpc_id,
                    "region": self.client.meta.region_name,
                    "flow_logs_enabled": True,
                    "compliance": True
                })

        # Determine compliance status
        status = (
            ResourceComplianceStatusEnum.FAIL.value
            if non_compliant_vpcs else ResourceComplianceStatusEnum.PASS.value
        )

        self.findings["vpc_flow_logs"]["status"] = status
        self.findings["vpc_flow_logs"]["details"] = findings

    async def check_ebs_default_encryption(self):
        """
        Amazon EBS encryption by default is enabled to ensure all new EBS volumes are encrypted
        when created in the region
        """
        # Check EBS encryption by default status
        encryption_response = await self.client.get_ebs_encryption_by_default()

        # Determine if encryption is enabled
        encryption_enabled = encryption_response.get("EbsEncryptionByDefault", False)

        # Update findings
        self.findings["ebs_default_encryption"]["status"] = (
            ResourceComplianceStatusEnum.PASS.value if encryption_enabled else ResourceComplianceStatusEnum.FAIL.value
        )
        self.findings["ebs_default_encryption"]["details"] = [{
            "encryption_enabled": encryption_enabled,
            "region": self.client.meta.region_name,
            "compliance": encryption_enabled
        }]

    async def check_unused_nacls(self):
        """
        NACLs not associated with any subnets are deleted to prevent security risks and resource clutter
        """
        # Get all Network ACLs
        nacls_response = await self.client.describe_network_acls()
        nacls = nacls_response.get("NetworkAcls", [])

        findings = []
        unused_nacls = []

        for nacl in nacls:
            nacl_id = nacl["NetworkAclId"]
            associated_subnets = nacl.get("Associations", [])

            # If no subnets are associated with the NACL, mark as unused
            if not associated_subnets:
                findings.append({
                    "nacl_id": nacl_id,
                    "region": self.client.meta.region_name,
                    "associated_subnets_count": 0,
                    "compliance": False
                })
                unused_nacls.append(nacl_id)
            else:
                findings.append({
                    "nacl_id": nacl_id,
                    "region": self.client.meta.region_name,
                    "associated_subnets_count": len(associated_subnets),
                    "compliance": True
                })

        # Determine compliance status
        status = (
            ResourceComplianceStatusEnum.FAIL.value
            if unused_nacls else ResourceComplianceStatusEnum.PASS.value
        )

        self.findings["unused_nacls"]["status"] = status
        self.findings["unused_nacls"]["details"] = findings

    async def check_ec2_multiple_enis(self):
        """
        EC2 instances should not have multiple Elastic Network Interfaces (ENIs) attached
        """
        # Get all EC2 instances
        instances_response = await self.client.describe_instances()
        instances = [reservation["Instances"] for reservation in instances_response.get("Reservations", [])]
        instances = [instance for sublist in instances for instance in sublist]

        findings = []
        non_compliant_instances = []

        for instance in instances:
            instance_id = instance["InstanceId"]
            network_interfaces = instance.get("NetworkInterfaces", [])

            # If an instance has more than one ENI, mark as non-compliant
            if len(network_interfaces) > 1:
                findings.append({
                    "instance_id": instance_id,
                    "region": self.client.meta.region_name,
                    "network_interfaces_count": len(network_interfaces),
                    "compliance": False
                })
                non_compliant_instances.append(instance_id)
            else:
                findings.append({
                    "instance_id": instance_id,
                    "region": self.client.meta.region_name,
                    "network_interfaces_count": len(network_interfaces),
                    "compliance": True
                })

        # Determine compliance status
        status = (
            ResourceComplianceStatusEnum.FAIL.value
            if non_compliant_instances else ResourceComplianceStatusEnum.PASS.value
        )

        self.findings["ec2_multiple_enis"]["status"] = status
        self.findings["ec2_multiple_enis"]["details"] = findings

    async def check_ec2_launch_templates_imdsv2(self):
        """
        EC2 launch templates are configured to enforce the use of Instance Metadata Service v2 (IMDSv2)
        """
        # Get all EC2 launch templates
        templates_response = await self.client.describe_launch_templates()
        launch_templates = templates_response.get("LaunchTemplates", [])

        findings = []
        non_compliant_templates = []

        for template in launch_templates:
            template_id = template["LaunchTemplateId"]
            template_name = template["LaunchTemplateName"]

            # Get the latest version of the launch template
            version_response = await self.client.describe_launch_template_versions(
                LaunchTemplateId=template_id,
                Versions=["$Latest"]
            )
            latest_version = version_response.get("LaunchTemplateVersions", [])[0]
            metadata_options = latest_version.get("LaunchTemplateData", {}).get("MetadataOptions", {})

            # Check if IMDSv2 is enforced
            http_tokens_required = metadata_options.get("HttpTokens", "optional") == "required"

            if not http_tokens_required:
                findings.append({
                    "launch_template_id": template_id,
                    "launch_template_name": template_name,
                    "region": self.client.meta.region_name,
                    "imdsv2_enforced": http_tokens_required,
                    "compliance": False
                })
                non_compliant_templates.append(template_id)
            else:
                findings.append({
                    "launch_template_id": template_id,
                    "launch_template_name": template_name,
                    "region": self.client.meta.region_name,
                    "imdsv2_enforced": http_tokens_required,
                    "compliance": True
                })

        # Determine compliance status
        status = (
            ResourceComplianceStatusEnum.FAIL.value
            if non_compliant_templates else ResourceComplianceStatusEnum.PASS.value
        )

        self.findings["ec2_launch_templates_imdsv2"]["status"] = status
        self.findings["ec2_launch_templates_imdsv2"]["details"] = findings

    async def check_ec2_client_vpn_logging(self):
        """
        AWS Client VPN endpoints log client connections for security auditing and compliance
        """
        # Get all Client VPN endpoints
        vpn_response = await self.client.describe_client_vpn_endpoints()
        vpn_endpoints = vpn_response.get("ClientVpnEndpoints", [])

        findings = []
        non_compliant_vpns = []

        for vpn in vpn_endpoints:
            vpn_id = vpn["ClientVpnEndpointId"]
            cloudwatch_log_group = vpn.get("ConnectionLogOptions", {}).get("CloudwatchLogGroup", None)
            logging_enabled = bool(cloudwatch_log_group)

            # If logging is not enabled, flag as non-compliant
            if not logging_enabled:
                findings.append({
                    "vpn_id": vpn_id,
                    "region": self.client.meta.region_name,
                    "logging_enabled": logging_enabled,
                    "compliance": False
                })
                non_compliant_vpns.append(vpn_id)

        # Determine compliance status
        status = (
            ResourceComplianceStatusEnum.FAIL.value
            if non_compliant_vpns else ResourceComplianceStatusEnum.PASS.value
        )

        self.findings["ec2_client_vpn_logging"]["status"] = status
        self.findings["ec2_client_vpn_logging"]["details"] = findings

    async def check_subnet_launch_template_public_ip(self):
        """
        Subnets and launch templates should be configured to prevent automatic assignment of public IP addresses
        """
        findings = []
        non_compliant_resources = []

        # Check subnets
        subnets_response = await self.client.describe_subnets()
        for subnet in subnets_response.get("Subnets", []):
            subnet_id = subnet["SubnetId"]
            vpc_id = subnet["VpcId"]
            auto_assign_public_ip = subnet["MapPublicIpOnLaunch"]

            findings.append({
                "resource_type": "subnet",
                "resource_id": subnet_id,
                "resource_name": None,
                "vpc_id": vpc_id,
                "assigns_public_ip": auto_assign_public_ip,
                "compliance": not auto_assign_public_ip,
                "region": self.client.meta.region_name
            })

            if auto_assign_public_ip:
                non_compliant_resources.append(subnet_id)

        # Check launch templates
        templates_response = await self.client.describe_launch_templates()
        for template in templates_response.get("LaunchTemplates", []):
            template_id = template["LaunchTemplateId"]
            template_name = template["LaunchTemplateName"]

            versions_response = await self.client.describe_launch_template_versions(
                LaunchTemplateId=template_id
            )

            for version in versions_response.get("LaunchTemplateVersions", []):
                network_interfaces = version.get("LaunchTemplateData", {}).get("NetworkInterfaces", [])

                assigns_public_ip = any(
                    interface.get("AssociatePublicIpAddress", False) for interface in network_interfaces
                )

                findings.append({
                    "resource_type": "launch_template",
                    "resource_id": template_id,
                    # "resource_name": template_name,
                    "vpc_id": None,
                    "assigns_public_ip": assigns_public_ip,
                    "compliance": not assigns_public_ip,
                    "region": self.client.meta.region_name
                })

                if assigns_public_ip:
                    non_compliant_resources.append(template_id)

        # Determine compliance status
        status = (
            ResourceComplianceStatusEnum.FAIL.value
            if non_compliant_resources else ResourceComplianceStatusEnum.PASS.value
        )

        self.findings["subnet_launch_template_public_ip"]["status"] = status
        self.findings["subnet_launch_template_public_ip"]["details"] = findings

    async def check_vpc_endpoints(self):
        """
        Check if required VPC endpoints are configured for various AWS services including EC2, ECR, SSM, etc.
        """
        # Get the list of existing VPC endpoints
        vpc_endpoints = await self.client.describe_vpc_endpoints()
        existing_endpoints = {vp["ServiceName"]: vp for vp in vpc_endpoints["VpcEndpoints"]}

        # Define required endpoints with their types
        required_endpoints = {
            "ec2": {
                "service_name": f"com.amazonaws.{self.client.meta.region_name}.ec2",
                "description": "EC2 API endpoint"
            },
            "ecr_api": {
                "service_name": f"com.amazonaws.{self.client.meta.region_name}.ecr.api",
                "description": "ECR API endpoint"
            },
            "ecr_dkr": {
                "service_name": f"com.amazonaws.{self.client.meta.region_name}.ecr.dkr",
                "description": "ECR Docker Registry endpoint"
            },
            "ssm": {
                "service_name": f"com.amazonaws.{self.client.meta.region_name}.ssm",
                "description": "SSM endpoint"
            },
            "ssm_messages": {
                "service_name": f"com.amazonaws.{self.client.meta.region_name}.ssmmessages",
                "description": "SSM Messages endpoint"
            },
            "ec2_messages": {
                "service_name": f"com.amazonaws.{self.client.meta.region_name}.ec2messages",
                "description": "EC2 Messages endpoint"
            },
            "ssm_contacts": {
                "service_name": f"com.amazonaws.{self.client.meta.region_name}.ssm-contacts",
                "description": "SSM Contacts endpoint"
            },
            "ssm_incidents": {
                "service_name": f"com.amazonaws.{self.client.meta.region_name}.ssm-incidents",
                "description": "SSM Incidents endpoint"
            }
        }

        findings = []
        overall_compliant = True

        for endpoint_type, config in required_endpoints.items():
            service_name = config["service_name"]
            existing_endpoint = existing_endpoints.get(service_name)

            if existing_endpoint:
                # Endpoint exists
                findings.append({
                    "endpoint_type": endpoint_type,
                    "service_name": service_name,
                    "description": config["description"],
                    "vpc_endpoint_id": existing_endpoint["VpcEndpointId"],
                    "region": self.client.meta.region_name,
                    "endpoint_exists": True,
                    "compliance": True
                })
            else:
                # Endpoint doesn't exist
                findings.append({
                    "endpoint_type": endpoint_type,
                    "service_name": service_name,
                    "description": config["description"],
                    "vpc_endpoint_id": None,
                    "region": self.client.meta.region_name,
                    "endpoint_exists": False,
                    "compliance": False
                })
                overall_compliant = False

        # Update findings
        self.findings["vpc_endpoints"]["status"] = (
            ResourceComplianceStatusEnum.PASS.value if overall_compliant
            else ResourceComplianceStatusEnum.FAIL.value
        )
        self.findings["vpc_endpoints"]["details"] = findings

    def get_check_functions(self):
        return [
            self.check_ebs_encryption,
            self.check_imdsv2,
            self.check_public_access,
            self.check_termination_protection,
            self.check_vpc_endpoints,
            self.check_vpc_block_internet_gateway_traffic,
            self.check_ebs_public_snapshots,
            self.check_unrestricted_ports,
            self.check_vpc_default_security_group_rules,
            self.check_transit_gateway_auto_accept,
            self.check_subnet_launch_template_public_ip,
            self.check_vpn_configuration,
            self.check_nacl_unrestricted_ssh_rdp,
            self.check_paravirtual_instances,
            self.check_stopped_instances_cleanup,
            self.check_vpc_flow_logs,
            self.check_ebs_default_encryption,
            self.check_unused_nacls,
            self.check_ec2_multiple_enis,
            self.check_ec2_launch_templates_imdsv2,
            self.check_ec2_client_vpn_logging
        ]

    async def run_checks(self):
        for region in self.regions:
            logger.info(f"Scanning region: {region}")
            session = self.get_session(region)
            if not await self.is_region_accessible():
                logger.warning(f"Region {region} is not accessible. Skipping checks.")
                continue
            async with session.client(AWSServiceNameEnum.EC2.value, region_name=region) as client:
                self.client = client
                self.instances = await self.get_instances_details()

                for check_function in self.get_check_functions():
                    logger.info(f"Running check: {check_function.__name__}")
                    await check_function()
            await asyncio.sleep(5)

        return self.findings
