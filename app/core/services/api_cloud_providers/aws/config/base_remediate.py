from app.core.services.api_cloud_providers import AWSServiceFactory

__all__ = ["BaseRemediationProcessor"]


class BaseRemediationProcessor:
    def __init__(self, credentials, regions=[]):
        self.credentials = credentials
        self.client = None
        self.regions = regions if regions else []

    def get_session(self):
        aws_service_factory = AWSServiceFactory(self.credentials)
        return aws_service_factory.get_session()
