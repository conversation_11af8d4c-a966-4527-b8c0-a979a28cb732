"""
Enhanced Base Check Processor with Resource Explorer Integration

This module provides an enhanced base class for CloudAudit checks that integrates
with Resource Explorer to intelligently skip regions without resources and
automatically mark checks as PASSED for those regions.
"""

import logging
from typing import Dict, Any, List, Optional
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.resource_explorer import ResourceExplorerService
from app.common import ResourceComplianceStatusEnum

logger = logging.getLogger(__name__)


class EnhancedBaseChecksProcessor(BaseChecksProcessor):
    """
    Enhanced base class for checks that supports intelligent region filtering
    using Resource Explorer integration.
    """
    
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")
        self._service_region_mapping: Optional[Dict[str, List[str]]] = None
        self._resource_explorer: Optional[ResourceExplorerService] = None
        
    def _get_service_name(self) -> str:
        """
        Extract service name from the module path.
        Override this method in subclasses if needed.
        """
        module_path = self.__module__
        # Extract service name from path like: app.core.services.api_cloud_providers.aws.ec2.checks.check_name
        parts = module_path.split('.')
        if len(parts) >= 6 and parts[5] in ['ec2', 'rds', 'lambda', 'ecs', 'eks', 's3', 'iam', 'elb', 'efs', 'elasticache']:
            return parts[5]
        return 'unknown'
    
    async def _initialize_resource_explorer(self):
        """Initialize Resource Explorer service if not already done."""
        if self._resource_explorer is None:
            try:
                self._resource_explorer = ResourceExplorerService(self.credentials)
                # Get service-specific region mapping
                service_name = self._get_service_name()
                if service_name != 'unknown':
                    enabled_services = [{'name': service_name}]
                    self._service_region_mapping = await self._resource_explorer.get_service_specific_regions(enabled_services)
                    logger.info(f"🔍 Resource Explorer initialized for {service_name}")
                else:
                    logger.warning("⚠️ Could not determine service name for Resource Explorer integration")
            except Exception as e:
                logger.warning(f"⚠️ Resource Explorer initialization failed: {e}")
                self._resource_explorer = None
                self._service_region_mapping = None
    
    def _should_skip_region(self, region: str) -> bool:
        """
        Determine if a region should be skipped for this service.
        
        Args:
            region: Region to check
            
        Returns:
            True if region should be skipped, False if it should be scanned
        """
        if not self._resource_explorer or not self._service_region_mapping:
            return False  # Fallback to scanning all regions
            
        service_name = self._get_service_name()
        if service_name == 'unknown':
            return False  # Fallback to scanning all regions
            
        return self._resource_explorer.should_skip_region_for_service(
            service_name, region, self._service_region_mapping
        )
    
    def _get_regions_to_scan(self) -> List[str]:
        """
        Get the list of regions that should actually be scanned.
        
        Returns:
            List of regions to scan (excludes skipped regions)
        """
        if not self._resource_explorer or not self._service_region_mapping:
            return self.regions  # Fallback to all regions
            
        service_name = self._get_service_name()
        if service_name == 'unknown':
            return self.regions  # Fallback to all regions
            
        regions_to_scan = []
        skipped_regions = []
        
        for region in self.regions:
            if self._should_skip_region(region):
                skipped_regions.append(region)
            else:
                regions_to_scan.append(region)
        
        if skipped_regions:
            logger.info(f"🚀 {service_name}: Scanning {len(regions_to_scan)} regions with resources: {regions_to_scan}")
            logger.info(f"⏭️  {service_name}: Skipping {len(skipped_regions)} regions without resources: {skipped_regions}")
        else:
            logger.info(f"🔍 {service_name}: Scanning all {len(regions_to_scan)} regions (no optimization available)")
            
        return regions_to_scan
    
    def _get_skipped_regions(self) -> List[str]:
        """
        Get the list of regions that were skipped.
        
        Returns:
            List of skipped regions
        """
        if not self._resource_explorer or not self._service_region_mapping:
            return []  # No regions skipped if Resource Explorer not available
            
        skipped_regions = []
        for region in self.regions:
            if self._should_skip_region(region):
                skipped_regions.append(region)
                
        return skipped_regions
    
    def _create_auto_pass_entry(self, region: str, check_name: str) -> Dict[str, Any]:
        """
        Create an auto-pass entry for a skipped region.
        
        Args:
            region: Region that was skipped
            check_name: Name of the check
            
        Returns:
            Dictionary representing an auto-passed check result
        """
        return {
            "region": region,
            "status": ResourceComplianceStatusEnum.PASS.value,
            "compliance": True,
            "auto_passed": True,
            "reason": "No resources found in region",
            "resource_count": 0,
            "message": f"Region {region} skipped - no {self._get_service_name()} resources detected"
        }
    
    def _add_auto_pass_entries_to_findings(self, findings: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add auto-pass entries for skipped regions to the findings.
        
        Args:
            findings: Original findings dictionary
            
        Returns:
            Enhanced findings with auto-pass entries for skipped regions
        """
        skipped_regions = self._get_skipped_regions()
        if not skipped_regions:
            return findings  # No regions were skipped
            
        service_name = self._get_service_name()
        logger.info(f"✅ {service_name}: Auto-passing checks for {len(skipped_regions)} skipped regions: {skipped_regions}")
        
        # Add auto-pass entries to each check in findings
        for check_name, check_data in findings.items():
            if isinstance(check_data, dict) and "details" in check_data:
                # Add auto-pass entries for each skipped region
                for region in skipped_regions:
                    auto_pass_entry = self._create_auto_pass_entry(region, check_name)
                    check_data["details"].append(auto_pass_entry)
                    
                # Log the auto-pass additions
                auto_pass_count = len(skipped_regions)
                original_count = len(check_data["details"]) - auto_pass_count
                logger.debug(f"  📝 {check_name}: Added {auto_pass_count} auto-pass entries ({original_count} scanned + {auto_pass_count} auto-passed)")
        
        return findings
    
    async def enhanced_check(self) -> Dict[str, Any]:
        """
        Enhanced check method that handles Resource Explorer integration.
        
        This method should be called instead of the regular check() method
        to get the benefits of intelligent region filtering.
        
        Returns:
            Complete findings including auto-passed entries for skipped regions
        """
        # Initialize Resource Explorer
        await self._initialize_resource_explorer()
        
        # Get regions to actually scan
        original_regions = self.regions.copy()
        self.regions = self._get_regions_to_scan()
        
        try:
            # Call the original check method
            findings = self.check()
            
            # Restore original regions list
            self.regions = original_regions
            
            # Add auto-pass entries for skipped regions
            enhanced_findings = self._add_auto_pass_entries_to_findings(findings)
            
            return enhanced_findings
            
        except Exception as e:
            # Restore original regions list in case of error
            self.regions = original_regions
            raise e
    
    def check(self) -> Dict[str, Any]:
        """
        Original check method - should be overridden by subclasses.
        
        This method should contain the actual check logic and will be called
        by enhanced_check() after region filtering is applied.
        """
        raise NotImplementedError("Subclasses must implement the check() method")
