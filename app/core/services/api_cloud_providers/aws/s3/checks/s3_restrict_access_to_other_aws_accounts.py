import json
from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, S3ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.s3.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "restrict_access_to_other_aws_accounts": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": S3ChecksDescriptionEnum.RESTRICT_ACCESS_TO_OTHER_AWS_ACCOUNTS.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        # For S3 (global service), process buckets only once to avoid duplicates
        # Use the first available region's cached data since S3 data is replicated across all regions
        processed_buckets = set()

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            bucket_details = cached.get("bucket_details", {})

            for bucket_name, bucket_info in bucket_details.items():
                # Skip if we've already processed this bucket
                if bucket_name in processed_buckets:
                    continue
                processed_buckets.add(bucket_name)
                bucket_region = bucket_info.get("region", region)
                policy_data = bucket_info.get("policy")
                unrestricted_access = False

                if policy_data:
                    try:
                        policy = json.loads(policy_data.get("Policy", '{}'))

                        for statement in policy.get("Statement", []):
                            if statement.get("Effect") != "Allow":
                                continue

                            principal = statement.get("Principal", {})
                            
                            # Only check AWS principals (not service principals or other types)
                            if not isinstance(principal, dict) or "AWS" not in principal:
                                continue

                            aws_principal = principal["AWS"]
                            
                            # Check string principal that's another AWS account
                            if isinstance(aws_principal, str):
                                if aws_principal == "*" or (aws_principal != self.aws_account_id and aws_principal.startswith("arn:aws:iam::")):
                                    unrestricted_access = True
                                    break
                            
                            # Check list principal for other AWS accounts
                            elif isinstance(aws_principal, list):
                                for principal_entry in aws_principal:
                                    if principal_entry == "*" or (principal_entry != self.aws_account_id and 
                                                                 isinstance(principal_entry, str) and 
                                                                 principal_entry.startswith("arn:aws:iam::")):
                                        unrestricted_access = True
                                        break
                                
                                if unrestricted_access:
                                    break
                    except (json.JSONDecodeError, AttributeError):
                        # No policy or error parsing policy - considered compliant
                        pass

                compliance = not unrestricted_access

                if findings["restrict_access_to_other_aws_accounts"]["status"] == ResourceComplianceStatusEnum.PASS.value and not compliance:
                    findings["restrict_access_to_other_aws_accounts"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["restrict_access_to_other_aws_accounts"]["details"].append({
                    "bucket_name": bucket_name,
                    "region": bucket_region,
                    "compliance": compliance,
                })

        return findings

    def remediate(self):
        pass
