from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, S3ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.s3.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "access_points_block_public_access": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": S3ChecksDescriptionEnum.ACCESS_POINTS_BLOCK_PUBLIC_ACCESS.value,
                "severity": SeverityEnum.CRITICAL.value,
            }
        }

        # For S3 (global service), process access points only once to avoid duplicates
        # Use the first available region's cached data since S3 data is replicated across all regions
        processed_access_points = set()

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            access_points = cached.get("access_points", [])

            for access_point in access_points:
                access_point_name = access_point.get("Name")
                # Skip if we've already processed this access point
                if access_point_name in processed_access_points:
                    continue
                processed_access_points.add(access_point_name)

                policy_status = access_point.get("policy_status")

                # Check if all block public access settings are enabled
                if policy_status:
                    block_settings = policy_status.get("PublicAccessBlockConfiguration", {})
                    block_public_access_enabled = all([
                        block_settings.get("BlockPublicAcls", False),
                        block_settings.get("IgnorePublicAcls", False),
                        block_settings.get("BlockPublicPolicy", False),
                        block_settings.get("RestrictPublicBuckets", False)
                    ])
                else:
                    block_public_access_enabled = False

                compliance = block_public_access_enabled

                if findings["access_points_block_public_access"]["status"] == ResourceComplianceStatusEnum.PASS.value and not compliance:
                    findings["access_points_block_public_access"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["access_points_block_public_access"]["details"].append({
                    "access_point_name": access_point_name,
                    "region": region,
                    "compliance": compliance,
                })

        return findings

    def remediate(self):
        pass
