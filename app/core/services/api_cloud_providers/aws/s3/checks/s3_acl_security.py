from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, S3ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.s3.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "s3_acl_security": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": S3ChecksDescriptionEnum.S3_ACL_SECURITY.value,
                "severity": SeverityEnum.CRITICAL.value,
            }
        }

        # For S3 (global service), process buckets only once to avoid duplicates
        # Use the first available region's cached data since S3 data is replicated across all regions
        processed_buckets = set()

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            bucket_details = cached.get("bucket_details", {})

            for bucket_name, bucket_info in bucket_details.items():
                # Skip if we've already processed this bucket
                if bucket_name in processed_buckets:
                    continue
                processed_buckets.add(bucket_name)
                bucket_region = bucket_info.get("region", region)
                acl = bucket_info.get("acl")

                acls_used = False
                public_read_access = False
                public_write_access = False

                if acl:
                    grants = acl.get("Grants", [])

                    # Check if any ACL grants are present (for non-canonical users)
                    acls_used = any(grant.get("Grantee", {}).get("Type") != "CanonicalUser" for grant in grants)

                    # Check if any grant allows public read access
                    public_read_access = any(
                        grant.get("Grantee", {}).get("Type") in ["Group"] and
                        grant.get("Grantee", {}).get("URI") == "http://acs.amazonaws.com/groups/global/AllUsers" and
                        grant.get("Permission") == "READ"
                        for grant in grants
                    )

                    # Check if any grant allows public write access
                    public_write_access = any(
                        grant.get("Grantee", {}).get("Type") in ["Group"] and
                        grant.get("Grantee", {}).get("URI") == "http://acs.amazonaws.com/groups/global/AllUsers" and
                        grant.get("Permission") == "WRITE"
                        for grant in grants
                    )

                # Determine overall compliance
                compliance = not (acls_used or public_read_access or public_write_access)

                if findings["s3_acl_security"]["status"] == ResourceComplianceStatusEnum.PASS.value and not compliance:
                    findings["s3_acl_security"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["s3_acl_security"]["details"].append({
                    "bucket_name": bucket_name,
                    "acls_used": acls_used,
                    "public_read_access": public_read_access,
                    "public_write_access": public_write_access,
                    "region": bucket_region,
                    "compliance": compliance,
                })

        return findings

    def remediate(self):
        pass
