import json
from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, S3ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.s3.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "require_ssl_for_requests": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": S3ChecksDescriptionEnum.REQUIRE_SSL_FOR_REQUESTS.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        # For S3 (global service), process buckets only once to avoid duplicates
        # Use the first available region's cached data since S3 data is replicated across all regions
        processed_buckets = set()

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            bucket_details = cached.get("bucket_details", {})

            for bucket_name, bucket_info in bucket_details.items():
                # Skip if we've already processed this bucket
                if bucket_name in processed_buckets:
                    continue
                processed_buckets.add(bucket_name)
                bucket_region = bucket_info.get("region", region)
                policy_data = bucket_info.get("policy")

                ssl_required = False
                if policy_data:
                    try:
                        policy = json.loads(policy_data.get("Policy", '{}'))
                        policy_statements = policy.get("Statement", [])

                        # Check if any statement explicitly denies non-SSL requests
                        ssl_required = any(
                            statement.get("Effect") == "Deny" and
                            "aws:SecureTransport" in statement.get("Condition", {}).get("Bool", {}) and
                            statement["Condition"]["Bool"]["aws:SecureTransport"] == "false"
                            for statement in policy_statements
                        )
                    except (json.JSONDecodeError, AttributeError):
                        ssl_required = False

                compliance = ssl_required

                if findings["require_ssl_for_requests"]["status"] == ResourceComplianceStatusEnum.PASS.value and not compliance:
                    findings["require_ssl_for_requests"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["require_ssl_for_requests"]["details"].append({
                    "bucket_name": bucket_name,
                    "region": bucket_region,
                    "compliance": compliance,
                })

        return findings

    def remediate(self):
        pass
