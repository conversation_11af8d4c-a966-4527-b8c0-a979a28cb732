from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, S3ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.s3.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "lifecycle_configurations_required": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": S3ChecksDescriptionEnum.LIFECYCLE_CONFIGURATIONS_REQUIRED.value,
                "severity": SeverityEnum.LOW.value,
            }
        }

        # For S3 (global service), process buckets only once to avoid duplicates
        # Use the first available region's cached data since S3 data is replicated across all regions
        processed_buckets = set()

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            bucket_details = cached.get("bucket_details", {})

            for bucket_name, bucket_info in bucket_details.items():
                # Skip if we've already processed this bucket
                if bucket_name in processed_buckets:
                    continue
                processed_buckets.add(bucket_name)
                bucket_region = bucket_info.get("region", region)
                lifecycle_configuration = bucket_info.get("lifecycle_configuration")

                # Check if the bucket has a lifecycle configuration
                has_lifecycle_config = bool(lifecycle_configuration and lifecycle_configuration.get("Rules", []))
                compliance = has_lifecycle_config

                if findings["lifecycle_configurations_required"]["status"] == ResourceComplianceStatusEnum.PASS.value and not compliance:
                    findings["lifecycle_configurations_required"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["lifecycle_configurations_required"]["details"].append({
                    "bucket_name": bucket_name,
                    "region": bucket_region,
                    "compliance": compliance,
                })

        return findings

    def remediate(self):
        pass
