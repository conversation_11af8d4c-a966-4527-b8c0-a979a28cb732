"""
Service-Level Region Intelligence Manager

This module provides intelligent region filtering capabilities that can be integrated
into the CloudAudit scanning workflow without modifying individual data_fetch.py files.
"""

import logging
from typing import Dict, List, Any, Optional, Callable
from app.core.services.resource_explorer import ResourceExplorerService
from app.common import ResourceComplianceStatusEnum

logger = logging.getLogger(__name__)


class RegionIntelligenceManager:
    """
    Manages intelligent region filtering for CloudAudit services using Resource Explorer.
    """
    
    def __init__(self, credentials: dict):
        """
        Initialize the Region Intelligence Manager.
        
        Args:
            credentials: AWS credentials dictionary
        """
        self.credentials = credentials
        self.resource_explorer: Optional[ResourceExplorerService] = None
        self.service_region_mapping: Dict[str, List[str]] = {}
        self._initialized = False
    
    async def initialize(self, enabled_services: List[Dict]) -> bool:
        """
        Initialize Resource Explorer and discover service-region mappings.
        
        Args:
            enabled_services: List of enabled services with 'name' key
            
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            self.resource_explorer = ResourceExplorerService(self.credentials)
            self.service_region_mapping = await self.resource_explorer.get_service_specific_regions(enabled_services)
            self._initialized = True
            
            # Log comprehensive region intelligence summary
            self._log_region_intelligence_summary(enabled_services)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Region Intelligence Manager initialization failed: {e}")
            self.resource_explorer = None
            self.service_region_mapping = {}
            self._initialized = False
            return False
    
    def _log_region_intelligence_summary(self, enabled_services: List[Dict]):
        """Log detailed region intelligence summary."""
        logger.info("🧠 Region Intelligence Summary:")
        
        total_services = len(enabled_services)
        services_with_resources = 0
        total_regions_with_resources = set()
        optimization_stats = {}
        
        for service in enabled_services:
            service_name = service['name']
            service_regions = self.service_region_mapping.get(service_name, [])
            
            if service_regions:
                services_with_resources += 1
                if service_name not in self.resource_explorer.GLOBAL_SERVICES:
                    total_regions_with_resources.update(service_regions)
                
                # Calculate potential optimization
                all_regions = self.resource_explorer._get_all_aws_regions()
                if service_name in self.resource_explorer.GLOBAL_SERVICES:
                    optimization_stats[service_name] = {
                        'regions_with_resources': len(all_regions),
                        'regions_to_scan': len(all_regions),
                        'regions_skipped': 0,
                        'optimization_percent': 0
                    }
                    logger.info(f"  🌍 {service_name.upper()}: Global service - processing all {len(all_regions)} regions")
                else:
                    regions_skipped = len(all_regions) - len(service_regions)
                    optimization_percent = (regions_skipped / len(all_regions)) * 100 if all_regions else 0
                    optimization_stats[service_name] = {
                        'regions_with_resources': len(service_regions),
                        'regions_to_scan': len(service_regions),
                        'regions_skipped': regions_skipped,
                        'optimization_percent': optimization_percent
                    }
                    logger.info(f"  🎯 {service_name.upper()}: Resources in {len(service_regions)} regions {service_regions}")
                    logger.info(f"      💡 Optimization: Skip {regions_skipped} empty regions ({optimization_percent:.1f}% reduction)")
            else:
                optimization_stats[service_name] = {
                    'regions_with_resources': 0,
                    'regions_to_scan': len(self.resource_explorer._get_all_aws_regions()),
                    'regions_skipped': 0,
                    'optimization_percent': 0
                }
                logger.warning(f"  ⚠️  {service_name.upper()}: No regions discovered - will scan all regions as fallback")
        
        # Overall summary
        logger.info(f"📊 Overall Intelligence Summary:")
        logger.info(f"   🔍 Services analyzed: {total_services}")
        logger.info(f"   ✅ Services with discovered resources: {services_with_resources}")
        logger.info(f"   🌍 Unique regions with resources: {len(total_regions_with_resources)}")
        
        # Calculate average optimization
        total_optimization = sum(stats['optimization_percent'] for stats in optimization_stats.values())
        avg_optimization = total_optimization / len(optimization_stats) if optimization_stats else 0
        logger.info(f"   ⚡ Average scan optimization: {avg_optimization:.1f}% regions skipped")
    
    def get_regions_for_service(self, service_name: str, all_regions: List[str]) -> Dict[str, Any]:
        """
        Get optimized region list for a specific service.
        
        Args:
            service_name: Name of the service
            all_regions: List of all possible regions
            
        Returns:
            Dictionary with regions_to_scan, regions_to_skip, and metadata
        """
        if not self._initialized or not self.resource_explorer:
            return {
                'regions_to_scan': all_regions,
                'regions_to_skip': [],
                'optimization_enabled': False,
                'reason': 'Resource Explorer not initialized'
            }
        
        service_regions = self.service_region_mapping.get(service_name, [])
        
        # Handle global services
        if service_name in self.resource_explorer.GLOBAL_SERVICES:
            return {
                'regions_to_scan': all_regions,
                'regions_to_skip': [],
                'optimization_enabled': True,
                'reason': 'Global service - process all regions',
                'service_type': 'global'
            }
        
        # Handle services with no discovered regions (fallback)
        if not service_regions:
            return {
                'regions_to_scan': all_regions,
                'regions_to_skip': [],
                'optimization_enabled': False,
                'reason': 'No regions discovered - fallback to all regions',
                'service_type': 'regional'
            }
        
        # Handle regional services with discovered regions
        regions_to_scan = [r for r in all_regions if r in service_regions]
        regions_to_skip = [r for r in all_regions if r not in service_regions]
        
        return {
            'regions_to_scan': regions_to_scan,
            'regions_to_skip': regions_to_skip,
            'optimization_enabled': True,
            'reason': f'Resource Explorer discovered {len(service_regions)} regions with resources',
            'service_type': 'regional',
            'discovered_regions': service_regions
        }
    
    def create_auto_pass_findings(self, service_name: str, regions_to_skip: List[str], check_names: List[str]) -> Dict[str, Any]:
        """
        Create auto-pass findings for skipped regions.
        
        Args:
            service_name: Name of the service
            regions_to_skip: List of regions that were skipped
            check_names: List of check names to auto-pass
            
        Returns:
            Dictionary of auto-pass findings
        """
        if not regions_to_skip or not check_names:
            return {}
        
        auto_pass_findings = {}
        
        for check_name in check_names:
            auto_pass_findings[check_name] = {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": f"Auto-passed for regions without {service_name} resources",
                "severity": "INFO"
            }
            
            # Add auto-pass entries for each skipped region
            for region in regions_to_skip:
                auto_pass_findings[check_name]["details"].append({
                    "region": region,
                    "status": ResourceComplianceStatusEnum.PASS.value,
                    "compliance": True,
                    "auto_passed": True,
                    "reason": "No resources found in region",
                    "resource_count": 0,
                    "message": f"Region {region} skipped - no {service_name} resources detected"
                })
        
        logger.info(f"✅ {service_name}: Created auto-pass findings for {len(check_names)} checks across {len(regions_to_skip)} skipped regions")
        
        return auto_pass_findings
    
    def merge_findings(self, scanned_findings: Dict[str, Any], auto_pass_findings: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge scanned findings with auto-pass findings.
        
        Args:
            scanned_findings: Findings from actual scanning
            auto_pass_findings: Auto-pass findings for skipped regions
            
        Returns:
            Merged findings dictionary
        """
        if not auto_pass_findings:
            return scanned_findings
        
        merged_findings = scanned_findings.copy()
        
        for check_name, auto_pass_data in auto_pass_findings.items():
            if check_name in merged_findings:
                # Merge details from auto-pass into existing check
                merged_findings[check_name]["details"].extend(auto_pass_data["details"])
            else:
                # Add new auto-pass check
                merged_findings[check_name] = auto_pass_data
        
        return merged_findings
    
    def log_service_optimization_summary(self, service_name: str, regions_info: Dict[str, Any]):
        """
        Log optimization summary for a specific service.
        
        Args:
            service_name: Name of the service
            regions_info: Region information from get_regions_for_service()
        """
        if not regions_info['optimization_enabled']:
            logger.info(f"🔄 {service_name}: No optimization - {regions_info['reason']}")
            return
        
        regions_to_scan = regions_info['regions_to_scan']
        regions_to_skip = regions_info['regions_to_skip']
        
        if regions_to_skip:
            optimization_percent = (len(regions_to_skip) / (len(regions_to_scan) + len(regions_to_skip))) * 100
            logger.info(f"🚀 {service_name}: Optimized scan - {len(regions_to_scan)} regions with resources: {regions_to_scan}")
            logger.info(f"⏭️  {service_name}: Skipping {len(regions_to_skip)} empty regions ({optimization_percent:.1f}% reduction): {regions_to_skip}")
        else:
            logger.info(f"🔍 {service_name}: Scanning all {len(regions_to_scan)} regions - {regions_info['reason']}")
    
    def is_initialized(self) -> bool:
        """Check if the manager is properly initialized."""
        return self._initialized
    
    def get_service_region_mapping(self) -> Dict[str, List[str]]:
        """Get the complete service-region mapping."""
        return self.service_region_mapping.copy()
