from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, ElastiCacheChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.elasticache.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "encrypted_at_rest": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.ENCRYPTED_AT_REST.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            replication_groups = cached.get("replication_groups", {}).get("ReplicationGroups", [])

            for group in replication_groups:
                at_rest_encryption_enabled = group.get("AtRestEncryptionEnabled", False)

                if findings["encrypted_at_rest"]["status"] == ResourceComplianceStatusEnum.PASS.value and not at_rest_encryption_enabled:
                    findings["encrypted_at_rest"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["encrypted_at_rest"]["details"].append({
                    "replication_group_id": group["ReplicationGroupId"],
                    "region": region,
                    # "at_rest_encryption_enabled": at_rest_encryption_enabled,
                    "compliance": at_rest_encryption_enabled,
                })

        return findings

    def remediate(self):
        pass
