from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, ElastiCacheChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.elasticache.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "redis_multi_az_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.REDIS_MULTI_AZ_ENABLED.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            replication_groups = cached.get("replication_groups", {}).get("ReplicationGroups", [])

            for replication_group in replication_groups:
                replication_group_id = replication_group.get("ReplicationGroupId", "Unknown")
                
                # Check Multi-AZ configuration - this is the key field from Prowler reference
                multi_az_status = replication_group.get("MultiAZ", "disabled")
                multi_az_enabled = multi_az_status.lower() == "enabled"
                
                # Get additional information for reporting
                automatic_failover = replication_group.get("AutomaticFailover", "disabled")
                cluster_enabled = replication_group.get("ClusterEnabled", False)
                num_cache_clusters = replication_group.get("NumCacheClusters", 0)
                
                compliance = multi_az_enabled
                status = ResourceComplianceStatusEnum.PASS.value if compliance else ResourceComplianceStatusEnum.FAIL.value

                if findings["redis_multi_az_enabled"]["status"] == ResourceComplianceStatusEnum.PASS.value and not compliance:
                    findings["redis_multi_az_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["redis_multi_az_enabled"]["details"].append({
                    "replication_group_id": replication_group_id,
                    "region": region,
                    # "multi_az_enabled": multi_az_enabled,
                    # "multi_az_status": multi_az_status,
                    # "automatic_failover": automatic_failover,
                    # "cluster_enabled": cluster_enabled,
                    # "num_cache_clusters": num_cache_clusters,
                    # "status": status,
                    "compliance": compliance,
                })

        return findings

    def remediate(self):
        pass