from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, ElastiCacheChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.elasticache.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "automatic_minor_version_upgrades_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.AUTOMATIC_MINOR_VERSION_UPGRADES_ENABLED.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            clusters = cached.get("clusters", {}).get("CacheClusters", [])

            for cluster in clusters:
                auto_minor_version_upgrade = cluster.get("AutoMinorVersionUpgrade", False)

                if findings["automatic_minor_version_upgrades_enabled"]["status"] == ResourceComplianceStatusEnum.PASS.value and not auto_minor_version_upgrade:
                    findings["automatic_minor_version_upgrades_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["automatic_minor_version_upgrades_enabled"]["details"].append({
                    "cluster_id": cluster["CacheClusterId"],
                    "region": region,
                    # "auto_minor_version_upgrade": auto_minor_version_upgrade,
                    "compliance": auto_minor_version_upgrade,
                })

        return findings

    def remediate(self):
        pass
