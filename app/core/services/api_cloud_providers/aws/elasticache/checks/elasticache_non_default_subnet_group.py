from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, ElastiCacheChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.elasticache.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "non_default_subnet_group": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.NON_DEFAULT_SUBNET_GROUP.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            clusters = cached.get("clusters", {}).get("CacheClusters", [])

            for cluster in clusters:
                subnet_group_name = cluster.get("CacheSubnetGroupName", "default")
                non_default_subnet_group = subnet_group_name != "default"

                if findings["non_default_subnet_group"]["status"] == ResourceComplianceStatusEnum.PASS.value and not non_default_subnet_group:
                    findings["non_default_subnet_group"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["non_default_subnet_group"]["details"].append({
                    "cluster_id": cluster["CacheClusterId"],
                    "region": region,
                    "subnet_group_name": subnet_group_name,
                    # "non_default_subnet_group": non_default_subnet_group,
                    "compliance": non_default_subnet_group,
                })

        return findings

    def remediate(self):
        pass
