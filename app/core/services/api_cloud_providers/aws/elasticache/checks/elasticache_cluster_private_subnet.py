from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, ElastiCacheChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.elasticache.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def _is_subnet_public(self, subnet_id: str, ec2_data: Dict[str, Any]) -> bool:
        """
        Helper method to determine if a subnet is public by checking route tables.
        A subnet is considered public if it has a route to an Internet Gateway (0.0.0.0/0 -> igw-*).
        """
        try:
            route_tables = ec2_data.get("route_tables", {}).get("RouteTables", [])
            subnets = ec2_data.get("subnets", {}).get("Subnets", [])
            
            # Find the VPC for this subnet
            vpc_id = None
            for subnet in subnets:
                if subnet.get("SubnetId") == subnet_id:
                    vpc_id = subnet.get("VpcId")
                    break
            
            if not vpc_id:
                return True  # Assume public if we can't determine
            
            # Check route tables associated with this subnet
            associated_route_tables = []
            for rt in route_tables:
                associations = rt.get("Associations", [])
                for assoc in associations:
                    if assoc.get("SubnetId") == subnet_id:
                        associated_route_tables.append(rt)
                        break
            
            # If no explicit association, use main route table for VPC
            if not associated_route_tables:
                for rt in route_tables:
                    if rt.get("VpcId") == vpc_id:
                        associations = rt.get("Associations", [])
                        for assoc in associations:
                            if assoc.get("Main", False):
                                associated_route_tables.append(rt)
                                break
            
            # Check routes for Internet Gateway
            for route_table in associated_route_tables:
                routes = route_table.get("Routes", [])
                for route in routes:
                    destination = route.get("DestinationCidrBlock", "")
                    gateway_id = route.get("GatewayId", "")
                    
                    # Check if there's a route to 0.0.0.0/0 via Internet Gateway
                    if destination == "0.0.0.0/0" and gateway_id.startswith("igw-"):
                        return True  # Subnet is public
            
            return False  # Subnet is private
            
        except Exception:
            # If we can't determine, assume it's public for security
            return True

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "cluster_private_subnet": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.CLUSTER_PRIVATE_SUBNET.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            clusters = cached.get("clusters", {}).get("CacheClusters", [])
            subnet_groups = cached.get("subnet_groups", {})
            ec2_data = cached.get("ec2_data", {})

            for cluster in clusters:
                cluster_id = cluster.get("CacheClusterId", "Unknown")
                engine = cluster.get("Engine", "unknown")
                subnet_group_name = cluster.get("CacheSubnetGroupName")

                if subnet_group_name and subnet_group_name in subnet_groups:
                    subnet_group = subnet_groups[subnet_group_name]
                    subnets = subnet_group.get("Subnets", [])
                    
                    public_subnets = []
                    private_subnets_only = True
                    
                    # Check each subnet to see if it's public
                    for subnet in subnets:
                        subnet_id = subnet.get("SubnetIdentifier", "")
                        is_public = self._is_subnet_public(subnet_id, ec2_data)
                        if is_public:
                            public_subnets.append(subnet_id)
                            private_subnets_only = False
                    
                    compliance = private_subnets_only
                    status = ResourceComplianceStatusEnum.PASS.value if compliance else ResourceComplianceStatusEnum.FAIL.value
                    
                    findings["cluster_private_subnet"]["details"].append({
                        "cluster_id": cluster_id,
                        "engine": engine,
                        "region": region,
                        "subnet_group_name": subnet_group_name,
                        # "private_subnets_only": private_subnets_only,
                        # "public_subnets_count": len(public_subnets),
                        # "total_subnets": len(subnets),
                        # "status": status,
                        "compliance": compliance,
                    })
                    
                    if not compliance:
                        findings["cluster_private_subnet"]["status"] = ResourceComplianceStatusEnum.FAIL.value
                        
                elif subnet_group_name:
                    # Subnet group specified but not found in cache
                    findings["cluster_private_subnet"]["details"].append({
                        "cluster_id": cluster_id,
                        "engine": engine,
                        "region": region,
                        "subnet_group_name": subnet_group_name,
                        # "private_subnets_only": False,
                        # "public_subnets_count": 0,
                        # "total_subnets": 0,
                        # "status": ResourceComplianceStatusEnum.FAIL.value,
                        "compliance": False,
                        # "note": "Subnet group not found in cache",
                    })
                    findings["cluster_private_subnet"]["status"] = ResourceComplianceStatusEnum.FAIL.value
                    
                else:
                    # No subnet group specified (likely EC2-Classic)
                    findings["cluster_private_subnet"]["details"].append({
                        "cluster_id": cluster_id,
                        "engine": engine,
                        "region": region,
                        "subnet_group_name": "None",
                        # "private_subnets_only": False,
                        # "public_subnets_count": 0,
                        # "total_subnets": 0,
                        # "status": ResourceComplianceStatusEnum.FAIL.value,
                        "compliance": False,
                        # "note": "No subnet group specified - likely EC2-Classic deployment",
                    })
                    findings["cluster_private_subnet"]["status"] = ResourceComplianceStatusEnum.FAIL.value

        return findings

    def remediate(self):
        pass
