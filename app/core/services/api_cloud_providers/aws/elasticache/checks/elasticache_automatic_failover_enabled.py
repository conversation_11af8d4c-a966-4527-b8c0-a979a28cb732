from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, ElastiCacheChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.elasticache.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "automatic_failover_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.AUTOMATIC_FAILOVER_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            replication_groups = cached.get("replication_groups", {}).get("ReplicationGroups", [])

            for group in replication_groups:
                automatic_failover_status = group.get("AutomaticFailover", "disabled")
                failover_enabled = automatic_failover_status == "enabled"

                if findings["automatic_failover_enabled"]["status"] == ResourceComplianceStatusEnum.PASS.value and not failover_enabled:
                    findings["automatic_failover_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["automatic_failover_enabled"]["details"].append({
                    "replication_group_id": group["ReplicationGroupId"],
                    "region": region,
                    # "automatic_failover_status": automatic_failover_status,
                    "compliance": failover_enabled,
                })

        return findings

    def remediate(self):
        pass
