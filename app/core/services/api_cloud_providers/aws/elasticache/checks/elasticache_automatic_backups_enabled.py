from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, ElastiCacheChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.elasticache.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "automatic_backups_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.AUTOMATIC_BACKUPS_ENABLED.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            clusters = cached.get("clusters", {}).get("CacheClusters", [])

            for cluster in clusters:
                if cluster["Engine"] == "redis":
                    snapshot_retention_limit = cluster.get("SnapshotRetentionLimit", 0)
                    backups_enabled = snapshot_retention_limit > 0

                    if findings["automatic_backups_enabled"]["status"] == ResourceComplianceStatusEnum.PASS.value and not backups_enabled:
                        findings["automatic_backups_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                    findings["automatic_backups_enabled"]["details"].append({
                        "cluster_id": cluster["CacheClusterId"],
                        "region": region,
                        # "backups_enabled": backups_enabled,
                        # "snapshot_retention_limit": snapshot_retention_limit,
                        "compliance": backups_enabled,
                    })

        return findings

    def remediate(self):
        pass
