from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, ElastiCacheChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.elasticache.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "redis_auth_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.REDIS_AUTH_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            replication_groups = cached.get("replication_groups", {}).get("ReplicationGroups", [])

            for group in replication_groups:
                # Check if the replication group is Redis OSS and version 5.x
                # Redis 6.x and later have more advanced authentication mechanisms
                if group.get("Engine") == "redis" and group.get("EngineVersion", "").startswith("5"):
                    auth_token_enabled = group.get("AuthTokenEnabled", False)

                    if findings["redis_auth_enabled"]["status"] == ResourceComplianceStatusEnum.PASS.value and not auth_token_enabled:
                        findings["redis_auth_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                    findings["redis_auth_enabled"]["details"].append({
                        "replication_group_id": group["ReplicationGroupId"],
                        "region": region,
                        # "auth_token_enabled": auth_token_enabled,
                        "engine_version": group.get("EngineVersion", ""),
                        "compliance": auth_token_enabled,
                    })

        return findings

    def remediate(self):
        pass
