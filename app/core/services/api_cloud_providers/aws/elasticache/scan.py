import asyncio
from celery.utils.log import get_task_logger
from ..config import BaseChecksProcessor
from app.common import (AWSServiceNameEnum, SeverityEnum, ResourceComplianceStatusEnum,
                        ElastiCacheChecksDescriptionEnum)

__all__ = ['ChecksProcessor']

logger = get_task_logger(__name__)


class ChecksProcessor(BaseChecksProcessor):
    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.clusters = None
        self.replication_groups = None
        self.findings = {
            "automatic_backups_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.AUTOMATIC_BACKUPS_ENABLED.value,
                "severity": SeverityEnum.HIGH.value
            },
            "automatic_minor_version_upgrades_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.AUTOMATIC_MINOR_VERSION_UPGRADES_ENABLED.value,
                "severity": SeverityEnum.HIGH.value
            },
            "automatic_failover_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.AUTOMATIC_FAILOVER_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "encrypted_at_rest": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.ENCRYPTED_AT_REST.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "encrypted_in_transit": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.ENCRYPTED_IN_TRANSIT.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "redis_auth_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.REDIS_AUTH_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "non_default_subnet_group": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.NON_DEFAULT_SUBNET_GROUP.value,
                "severity": SeverityEnum.HIGH.value
            },
            "cluster_private_subnet": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.CLUSTER_PRIVATE_SUBNET.value,
                "severity": SeverityEnum.HIGH.value
            },
            "redis_multi_az_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.REDIS_MULTI_AZ_ENABLED.value,
                "severity": SeverityEnum.HIGH.value
            }
        }

    async def check_automatic_backups_enabled(self):
        """
        ElastiCache (Redis OSS) clusters should have automatic backups enabled.
        """
        all_compliant = True  # Track overall compliance status

        for cluster in self.clusters.get("CacheClusters", []):
            if cluster["Engine"] == "redis":
                snapshot_retention_limit = cluster.get("SnapshotRetentionLimit", 0)
                backups_enabled = snapshot_retention_limit > 0

                self.findings["automatic_backups_enabled"]["details"].append({
                    "cluster_id": cluster["CacheClusterId"],
                    "region": self.client.meta.region_name,
                    "backups_enabled": backups_enabled,
                    "snapshot_retention_limit": snapshot_retention_limit,
                    "compliance": backups_enabled
                })

                if not backups_enabled:
                    all_compliant = False

        # Set the main status for the check
        if self.findings["automatic_backups_enabled"]["status"] and not all_compliant:
            self.findings["automatic_backups_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_automatic_minor_version_upgrades_enabled(self):
        """
        ElastiCache clusters should have automatic minor version upgrades enabled.
        """
        all_compliant = True  # Track overall compliance status

        for cluster in self.clusters.get("CacheClusters", []):
            auto_minor_version_upgrade = cluster.get("AutoMinorVersionUpgrade", False)

            self.findings["automatic_minor_version_upgrades_enabled"]["details"].append({
                "cluster_id": cluster["CacheClusterId"],
                "region": self.client.meta.region_name,
                "auto_minor_version_upgrade": auto_minor_version_upgrade,
                "compliance": auto_minor_version_upgrade
            })

            if not auto_minor_version_upgrade:
                all_compliant = False

        # Set the main status for the check
        if self.findings["automatic_minor_version_upgrades_enabled"]["status"] and not all_compliant:
            self.findings["automatic_minor_version_upgrades_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_automatic_failover_enabled(self):
        """
        ElastiCache replication groups should have automatic failover enabled.
        """
        all_compliant = True  # Track overall compliance status

        for group in self.replication_groups.get("ReplicationGroups", []):
            automatic_failover_status = group.get("AutomaticFailover", "disabled")
            failover_enabled = automatic_failover_status == "enabled"

            self.findings["automatic_failover_enabled"]["details"].append({
                "replication_group_id": group["ReplicationGroupId"],
                "region": self.client.meta.region_name,
                "automatic_failover_status": automatic_failover_status,
                "compliance": failover_enabled
            })

            if not failover_enabled:
                all_compliant = False

        # Set the main status for the check
        if self.findings["automatic_failover_enabled"]["status"] and not all_compliant:
            self.findings["automatic_failover_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_encrypted_at_rest(self):
        """
        ElastiCache replication groups should be encrypted at rest.
        """
        all_compliant = True  # Track overall compliance status

        for group in self.replication_groups.get("ReplicationGroups", []):
            at_rest_encryption_enabled = group.get("AtRestEncryptionEnabled", False)

            self.findings["encrypted_at_rest"]["details"].append({
                "replication_group_id": group["ReplicationGroupId"],
                "region": self.client.meta.region_name,
                "at_rest_encryption_enabled": at_rest_encryption_enabled,
                "compliance": at_rest_encryption_enabled
            })

            if not at_rest_encryption_enabled:
                all_compliant = False

        # Set the main status for the check
        if self.findings["encrypted_at_rest"]["status"] and not all_compliant:
            self.findings["encrypted_at_rest"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_encrypted_in_transit(self):
        """
        ElastiCache replication groups should be encrypted in transit.
        """
        all_compliant = True  # Track overall compliance status

        for group in self.replication_groups.get("ReplicationGroups", []):
            transit_encryption_enabled = group.get("TransitEncryptionEnabled", False)

            self.findings["encrypted_in_transit"]["details"].append({
                "replication_group_id": group["ReplicationGroupId"],
                "region": self.client.meta.region_name,
                "transit_encryption_enabled": transit_encryption_enabled,
                "compliance": transit_encryption_enabled
            })

            if not transit_encryption_enabled:
                all_compliant = False

        # Set the main status for the check
        if self.findings["encrypted_in_transit"]["status"] and not all_compliant:
            self.findings["encrypted_in_transit"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_redis_auth_enabled(self):
        """
        ElastiCache (Redis OSS) replication groups of earlier versions should have Redis OSS AUTH enabled.
        Redis AUTH is particularly important for Redis 5.x versions as they don't have the more
        advanced security features of Redis 6.x and later.
        """
        all_compliant = True  # Track overall compliance status

        for group in self.replication_groups.get("ReplicationGroups", []):
            # Check if the replication group is Redis OSS and version 5.x
            # Redis 6.x and later have more advanced authentication mechanisms
            if group.get("Engine") == "redis" and group.get("EngineVersion", "").startswith("5"):
                auth_token_enabled = group.get("AuthTokenEnabled", False)

                self.findings["redis_auth_enabled"]["details"].append({
                    "replication_group_id": group["ReplicationGroupId"],
                    "region": self.client.meta.region_name,
                    "auth_token_enabled": auth_token_enabled,
                    "engine_version": group.get("EngineVersion", ""),
                    "compliance": auth_token_enabled
                })

                if not auth_token_enabled:
                    all_compliant = False

        # Set the main status for the check
        if self.findings["redis_auth_enabled"]["status"] and not all_compliant:
            self.findings["redis_auth_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_non_default_subnet_group(self):
        """
        ElastiCache clusters should not use the default subnet group.
        """
        all_compliant = True  # Track overall compliance status

        for cluster in self.clusters.get("CacheClusters", []):
            subnet_group_name = cluster.get("CacheSubnetGroupName", "default")
            non_default_subnet_group = subnet_group_name != "default"

            self.findings["non_default_subnet_group"]["details"].append({
                "cluster_id": cluster["CacheClusterId"],
                "region": self.client.meta.region_name,
                "subnet_group_name": subnet_group_name,
                "non_default_subnet_group": non_default_subnet_group,
                "compliance": non_default_subnet_group
            })

            if not non_default_subnet_group:
                all_compliant = False

        # Set the main status for the check
        if self.findings["non_default_subnet_group"]["status"] and not all_compliant:
            self.findings["non_default_subnet_group"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_cluster_private_subnet(self):
        """
        ElastiCache clusters should be deployed only in private subnets.
        Ensures clusters are not accessible from the internet through public subnets.
        """
        all_compliant = True  # Track overall compliance status

        # Check cache clusters
        for cluster in self.clusters.get("CacheClusters", []):
            cluster_id = cluster.get("CacheClusterId", "Unknown")
            engine = cluster.get("Engine", "unknown")
            subnet_group_name = cluster.get("CacheSubnetGroupName")

            try:
                public_subnets = []
                private_subnets_only = True

                if subnet_group_name:
                    # Get subnet group details
                    subnet_groups_response = await self.client.describe_cache_subnet_groups(
                        CacheSubnetGroupName=subnet_group_name
                    )
                    subnet_groups = subnet_groups_response.get("CacheSubnetGroups", [])

                    if subnet_groups:
                        subnet_group = subnet_groups[0]
                        subnets = subnet_group.get("Subnets", [])

                        # Check each subnet to see if it's public
                        for subnet in subnets:
                            subnet_id = subnet.get("SubnetIdentifier", "")

                            # Check if subnet is public by examining route tables
                            is_public = await self._is_subnet_public(subnet_id)
                            if is_public:
                                public_subnets.append(subnet_id)
                                private_subnets_only = False

                        resource_status = ResourceComplianceStatusEnum.PASS.value if private_subnets_only else ResourceComplianceStatusEnum.FAIL.value

                        self.findings["cluster_private_subnet"]["details"].append({
                            "cluster_id": cluster_id,
                            "engine": engine,
                            "region": self.client.meta.region_name,
                            "subnet_group_name": subnet_group_name,
                            "private_subnets_only": private_subnets_only,
                            "public_subnets_count": len(public_subnets),
                            "total_subnets": len(subnets),
                            "status": resource_status,
                            "compliance": private_subnets_only
                        })

                        if not private_subnets_only:
                            all_compliant = False
                    else:
                        # No subnet group found
                        self.findings["cluster_private_subnet"]["details"].append({
                            "cluster_id": cluster_id,
                            "engine": engine,
                            "region": self.client.meta.region_name,
                            "subnet_group_name": subnet_group_name,
                            "private_subnets_only": False,
                            "public_subnets_count": 0,
                            "total_subnets": 0,
                            "status": ResourceComplianceStatusEnum.FAIL.value,
                            "compliance": False,
                            "note": "Subnet group not found"
                        })
                        all_compliant = False
                else:
                    # No subnet group specified (likely EC2-Classic)
                    self.findings["cluster_private_subnet"]["details"].append({
                        "cluster_id": cluster_id,
                        "engine": engine,
                        "region": self.client.meta.region_name,
                        "subnet_group_name": "None",
                        "private_subnets_only": False,
                        "public_subnets_count": 0,
                        "total_subnets": 0,
                        "status": ResourceComplianceStatusEnum.FAIL.value,
                        "compliance": False,
                        "note": "No subnet group specified - likely EC2-Classic deployment"
                    })
                    all_compliant = False

            except Exception as e:
                logger.error(f"Error checking subnet configuration for cluster {cluster_id}: {str(e)}")
                self.findings["cluster_private_subnet"]["details"].append({
                    "cluster_id": cluster_id,
                    "engine": engine,
                    "region": self.client.meta.region_name,
                    "subnet_group_name": subnet_group_name or "Unknown",
                    "private_subnets_only": False,
                    "public_subnets_count": 0,
                    "total_subnets": 0,
                    "status": ResourceComplianceStatusEnum.FAIL.value,
                    "compliance": False,
                    "error": str(e)
                })
                all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["cluster_private_subnet"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_redis_multi_az_enabled(self):
        """
        ElastiCache Redis clusters should have Multi-AZ enabled for high availability.
        Ensures Redis replication groups have Multi-AZ enabled.
        """
        all_compliant = True  # Track overall compliance status

        # Check Redis replication groups for Multi-AZ
        for replication_group in self.replication_groups.get("ReplicationGroups", []):
            replication_group_id = replication_group.get("ReplicationGroupId", "Unknown")

            try:
                # Check Multi-AZ configuration - this is the key field from Prowler reference
                multi_az_status = replication_group.get("MultiAZ", "disabled")
                multi_az_enabled = multi_az_status.lower() == "enabled"

                # Get additional information for reporting
                automatic_failover = replication_group.get("AutomaticFailover", "disabled")
                cluster_enabled = replication_group.get("ClusterEnabled", False)
                num_cache_clusters = replication_group.get("NumCacheClusters", 0)

                resource_status = ResourceComplianceStatusEnum.PASS.value if multi_az_enabled else ResourceComplianceStatusEnum.FAIL.value

                self.findings["redis_multi_az_enabled"]["details"].append({
                    "replication_group_id": replication_group_id,
                    "region": self.client.meta.region_name,
                    "multi_az_enabled": multi_az_enabled,
                    "multi_az_status": multi_az_status,
                    "automatic_failover": automatic_failover,
                    "cluster_enabled": cluster_enabled,
                    "num_cache_clusters": num_cache_clusters,
                    "status": resource_status,
                    "compliance": multi_az_enabled
                })

                if not multi_az_enabled:
                    all_compliant = False

            except Exception as e:
                logger.error(f"Error checking Multi-AZ for replication group {replication_group_id}: {str(e)}")
                self.findings["redis_multi_az_enabled"]["details"].append({
                    "replication_group_id": replication_group_id,
                    "region": self.client.meta.region_name,
                    "multi_az_enabled": False,
                    "multi_az_status": "error",
                    "automatic_failover": "unknown",
                    "cluster_enabled": False,
                    "num_cache_clusters": 0,
                    "status": ResourceComplianceStatusEnum.FAIL.value,
                    "compliance": False,
                    "error": str(e)
                })
                all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["redis_multi_az_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def _is_subnet_public(self, subnet_id: str) -> bool:
        """
        Helper method to determine if a subnet is public by checking route tables.
        A subnet is considered public if it has a route to an Internet Gateway (0.0.0.0/0 -> igw-*).
        """
        try:
            # Use EC2 client to check route tables
            session = self.get_session(self.client.meta.region_name)
            async with session.client("ec2", region_name=self.client.meta.region_name) as ec2_client:
                # Get route tables associated with this subnet
                route_tables_response = await ec2_client.describe_route_tables(
                    Filters=[
                        {
                            'Name': 'association.subnet-id',
                            'Values': [subnet_id]
                        }
                    ]
                )

                route_tables = route_tables_response.get("RouteTables", [])

                # If no explicit association, check the main route table for the VPC
                if not route_tables:
                    # Get subnet details to find VPC
                    subnets_response = await ec2_client.describe_subnets(SubnetIds=[subnet_id])
                    subnets = subnets_response.get("Subnets", [])
                    if subnets:
                        vpc_id = subnets[0].get("VpcId")
                        if vpc_id:
                            # Get main route table for VPC
                            main_rt_response = await ec2_client.describe_route_tables(
                                Filters=[
                                    {'Name': 'vpc-id', 'Values': [vpc_id]},
                                    {'Name': 'association.main', 'Values': ['true']}
                                ]
                            )
                            route_tables = main_rt_response.get("RouteTables", [])

                # Check routes for Internet Gateway
                for route_table in route_tables:
                    routes = route_table.get("Routes", [])
                    for route in routes:
                        destination = route.get("DestinationCidrBlock", "")
                        gateway_id = route.get("GatewayId", "")

                        # Check if there's a route to 0.0.0.0/0 via Internet Gateway
                        if destination == "0.0.0.0/0" and gateway_id.startswith("igw-"):
                            return True  # Subnet is public

                return False  # Subnet is private

        except Exception as e:
            logger.error(f"Error checking subnet {subnet_id}: {str(e)}")
            # If we can't determine, assume it's public for security
            return True

    def get_check_functions(self):
        return [
            self.check_automatic_backups_enabled,
            self.check_automatic_minor_version_upgrades_enabled,
            self.check_automatic_failover_enabled,
            self.check_encrypted_at_rest,
            self.check_encrypted_in_transit,
            self.check_redis_auth_enabled,
            self.check_non_default_subnet_group,
            self.check_cluster_private_subnet,
            self.check_redis_multi_az_enabled
        ]

    async def run_checks(self):
        for region in self.regions:
            logger.info(f"Scanning region: {region}")
            session = self.get_session(region)
            if not await self.is_region_accessible():
                logger.warning(f"Region {region} is not accessible. Skipping checks.")
                continue
            async with session.client(AWSServiceNameEnum.ElastiCache.value, region_name=region) as client:
                self.client = client
                self.clusters = await self.client.describe_cache_clusters(ShowCacheNodeInfo=False)
                self.replication_groups = await self.client.describe_replication_groups()

                for check_function in self.get_check_functions():
                    await check_function()
            await asyncio.sleep(5)

        return self.findings
