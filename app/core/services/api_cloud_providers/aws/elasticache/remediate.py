from celery.utils.log import get_task_logger
from ..config import BaseRemediationProcessor
from app.common import AWSServiceNameEnum

__all__ = ['RemediationProcessor']

logger = get_task_logger(__name__)


class RemediationProcessor(BaseRemediationProcessor):
    """
    Processor for remediating ElastiCache compliance issues.
    Inherits from BaseRemediationProcessor to reuse AWS session management.
    """

    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.remediation_results = {
            "automatic_backups_enabled": {
                "status": False,
                "message": "",
                "field_updates": {"backups_enabled": True, "snapshot_retention_limit": 7}
            },
            "automatic_minor_version_upgrades_enabled": {
                "status": False,
                "message": "",
                "field_updates": {"auto_minor_version_upgrade": True}
            },
            "automatic_failover_enabled": {
                "status": False,
                "message": "",
                "field_updates": {"automatic_failover_status": "enabled"}
            },
            "encrypted_at_rest": {
                "status": False,
                "message": "",
                "field_updates": {"at_rest_encryption_enabled": True}
            },
            "encrypted_in_transit": {
                "status": False,
                "message": "",
                "field_updates": {"transit_encryption_enabled": True}
            },
            "redis_auth_enabled": {
                "status": False,
                "message": "",
                "field_updates": {"auth_token_enabled": True}
            },
            "non_default_subnet_group": {
                "status": False,
                "message": "",
                "field_updates": {"non_default_subnet_group": True, "subnet_group_name": ""}
            }
        }

    async def remediate_automatic_backups_enabled(self, details):
        """
        Remediate ElastiCache by enabling automatic backups.
        """
        try:
            cluster_id = details.get("cluster_id", "")
            region = details.get("region", "")

            if not cluster_id or not region:
                return False, "Missing cluster ID or region"

            # Get current cluster configuration
            response = await self.client.describe_cache_clusters(
                CacheClusterId=cluster_id,
                ShowCacheNodeInfo=False
            )

            if not response.get("CacheClusters"):
                return False, f"Cluster {cluster_id} not found"

            cluster = response["CacheClusters"][0]
            current_retention = cluster.get("SnapshotRetentionLimit", 0)

            # Check if backups are already enabled
            if current_retention > 0:
                self.remediation_results["automatic_backups_enabled"]["field_updates"]["snapshot_retention_limit"] = current_retention
                return True, f"Automatic backups are already enabled for ElastiCache cluster {cluster_id}"

            # Enable automatic backups with a retention period of 7 days
            await self.client.modify_cache_cluster(
                CacheClusterId=cluster_id,
                SnapshotRetentionLimit=self.remediation_results["automatic_backups_enabled"]["field_updates"][
                    "snapshot_retention_limit"],
                ApplyImmediately=True
            )

            logger.info(f"Successfully enabled automatic backups for ElastiCache cluster {cluster_id}")
            return True, f"Successfully enabled automatic backups for ElastiCache cluster {cluster_id}"
        except Exception as e:
            error_msg = f"Failed to enable automatic backups: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_automatic_minor_version_upgrades_enabled(self, details):
        """
        Remediate ElastiCache by enabling automatic minor version upgrades.
        """
        try:
            cluster_id = details.get("cluster_id", "")
            region = details.get("region", "")

            if not cluster_id or not region:
                return False, "Missing cluster ID or region"

            # Get current cluster configuration
            response = await self.client.describe_cache_clusters(
                CacheClusterId=cluster_id,
                ShowCacheNodeInfo=False
            )

            if not response.get("CacheClusters"):
                return False, f"Cluster {cluster_id} not found"

            cluster = response["CacheClusters"][0]
            auto_minor_version_upgrade = cluster.get("AutoMinorVersionUpgrade", False)

            # Check if automatic minor version upgrades are already enabled
            if auto_minor_version_upgrade:
                return True, f"Automatic minor version upgrades are already enabled for ElastiCache cluster {cluster_id}"

            # Enable automatic minor version upgrades
            await self.client.modify_cache_cluster(
                CacheClusterId=cluster_id,
                AutoMinorVersionUpgrade=True,
                ApplyImmediately=True
            )

            logger.info(f"Successfully enabled automatic minor version upgrades for ElastiCache cluster {cluster_id}")
            return True, f"Successfully enabled automatic minor version upgrades for ElastiCache cluster {cluster_id}"
        except Exception as e:
            error_msg = f"Failed to enable automatic minor version upgrades: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_automatic_failover_enabled(self, details):
        """
        Remediate ElastiCache by enabling automatic failover for replication groups.
        """
        try:
            replication_group_id = details.get("replication_group_id", "")
            region = details.get("region", "")

            if not replication_group_id or not region:
                return False, "Missing replication group ID or region"

            # Get current replication group configuration
            response = await self.client.describe_replication_groups(
                ReplicationGroupId=replication_group_id
            )

            if not response.get("ReplicationGroups"):
                return False, f"Replication group {replication_group_id} not found"

            group = response["ReplicationGroups"][0]
            automatic_failover_status = group.get("AutomaticFailover", "disabled")

            # Check if automatic failover is already enabled
            if automatic_failover_status == "enabled":
                return True, f"Automatic failover is already enabled for ElastiCache replication group {replication_group_id}"

            # Enable automatic failover
            await self.client.modify_replication_group(
                ReplicationGroupId=replication_group_id,
                AutomaticFailoverEnabled=True,
                ApplyImmediately=True
            )

            logger.info(f"Successfully enabled automatic failover for ElastiCache replication group {replication_group_id}")
            return True, f"Successfully enabled automatic failover for ElastiCache replication group {replication_group_id}"
        except Exception as e:
            error_msg = f"Failed to enable automatic failover: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_encrypted_at_rest(self, details):
        """
        Remediate ElastiCache by enabling encryption at rest.
        Note: This typically requires creating a new replication group as it cannot be modified on existing ones.
        """
        try:
            replication_group_id = details.get("replication_group_id", "")
            region = details.get("region", "")

            if not replication_group_id or not region:
                return False, "Missing replication group ID or region"

            # Get the current replication group details
            replication_group = await self.client.describe_replication_groups(
                ReplicationGroupId=replication_group_id
            )

            if not replication_group.get("ReplicationGroups"):
                return False, f"Replication group {replication_group_id} not found"

            group = replication_group["ReplicationGroups"][0]

            # Check if encryption at rest is already enabled
            if group.get("AtRestEncryptionEnabled", False):
                return True, f"Encryption at rest is already enabled for ElastiCache replication group {replication_group_id}"

            # Cannot modify encryption at rest on existing replication groups
            return False, "Encryption at rest cannot be enabled on existing ElastiCache replication groups. " \
                          "You need to create a new replication group with encryption enabled and migrate your data."

        except Exception as e:
            error_msg = f"Failed to enable encryption at rest: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_encrypted_in_transit(self, details):
        """
        Remediate ElastiCache by enabling encryption in transit.
        Note: This typically requires creating a new replication group as it cannot be modified on existing ones.
        """
        try:
            replication_group_id = details.get("replication_group_id", "")
            region = details.get("region", "")

            if not replication_group_id or not region:
                return False, "Missing replication group ID or region"

            # Get the current replication group details
            replication_group = await self.client.describe_replication_groups(
                ReplicationGroupId=replication_group_id
            )

            if not replication_group.get("ReplicationGroups"):
                return False, f"Replication group {replication_group_id} not found"

            group = replication_group["ReplicationGroups"][0]

            # Check if encryption in transit is already enabled
            if group.get("TransitEncryptionEnabled", False):
                return True, f"Encryption in transit is already enabled for ElastiCache replication group {replication_group_id}"

            # Cannot modify encryption in transit on existing replication groups
            return False, "Encryption in transit cannot be enabled on existing ElastiCache replication groups. " \
                          "You need to create a new replication group with encryption enabled and migrate your data."

        except Exception as e:
            error_msg = f"Failed to enable encryption in transit: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_redis_auth_enabled(self, details):
        """
        Remediate ElastiCache by enabling Redis AUTH.
        Note: This typically requires creating a new replication group as it cannot be modified on existing ones.
        """
        try:
            replication_group_id = details.get("replication_group_id", "")
            region = details.get("region", "")

            if not replication_group_id or not region:
                return False, "Missing replication group ID or region"

            # Get the current replication group details
            replication_group = await self.client.describe_replication_groups(
                ReplicationGroupId=replication_group_id
            )

            if not replication_group.get("ReplicationGroups"):
                return False, f"Replication group {replication_group_id} not found"

            group = replication_group["ReplicationGroups"][0]

            # Check if AUTH is already enabled
            if group.get("AuthTokenEnabled", False):
                return True, f"Redis AUTH is already enabled for ElastiCache replication group {replication_group_id}"

            # Cannot modify AUTH token on existing replication groups
            return False, "Redis AUTH cannot be enabled on existing ElastiCache replication groups. " \
                          "You need to create a new replication group with AUTH enabled and migrate your data."

        except Exception as e:
            error_msg = f"Failed to enable Redis AUTH: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_non_default_subnet_group(self, details):
        """
        Remediate ElastiCache by creating and assigning a non-default subnet group.
        """
        try:
            cluster_id = details.get("cluster_id", "")
            region = details.get("region", "")

            if not cluster_id or not region:
                return False, "Missing cluster ID or region"

            # Get the current cluster details
            response = await self.client.describe_cache_clusters(
                CacheClusterId=cluster_id,
                ShowCacheNodeInfo=False
            )

            if not response.get("CacheClusters"):
                return False, f"Cluster {cluster_id} not found"

            cluster = response["CacheClusters"][0]
            subnet_group_name = cluster.get("CacheSubnetGroupName", "")

            # Check if already using a non-default subnet group
            if subnet_group_name and subnet_group_name != "default":
                self.remediation_results["non_default_subnet_group"]["field_updates"]["subnet_group_name"] = subnet_group_name
                return True, f"Cluster {cluster_id} already uses a non-default subnet group: {subnet_group_name}"

            # Get VPC information
            session = self.get_session()
            async with session.client(AWSServiceNameEnum.EC2.value, region_name=region) as ec2_client:
                # First try to find the VPC where the cluster is deployed
                vpc_id = None

                # Try to get the VPC ID from the subnet group if it exists
                if subnet_group_name:
                    try:
                        subnet_group_response = await self.client.describe_cache_subnet_groups(
                            CacheSubnetGroupName=subnet_group_name
                        )
                        if subnet_group_response.get("CacheSubnetGroups"):
                            subnet_group = subnet_group_response["CacheSubnetGroups"][0]
                            if subnet_group.get("VpcId"):
                                vpc_id = subnet_group.get("VpcId")
                    except Exception as e:
                        logger.warning(f"Could not determine VPC from subnet group: {str(e)}")

                # If we couldn't determine the VPC, try to get the default VPC
                if not vpc_id:
                    # 'isDefault' is a filter name in AWS API to find the default VPC
                    vpc_response = await ec2_client.describe_vpcs(
                        Filters=[
                            {
                                'Name': 'isDefault',
                                'Values': ['true']
                            }
                        ]
                    )

                    if not vpc_response.get("Vpcs"):
                        # If no default VPC, try to get any available VPC
                        vpc_response = await ec2_client.describe_vpcs()
                        if not vpc_response.get("Vpcs"):
                            return False, "No VPCs found in the region"

                    # Get the VPC ID - we use the first one as we need to select one
                    vpc_id = vpc_response["Vpcs"][0]["VpcId"]

                # Get subnets in the VPC
                # 'vpc-id' is a filter name in AWS API to find subnets in a specific VPC
                subnet_response = await ec2_client.describe_subnets(
                    Filters=[
                        {
                            'Name': 'vpc-id',
                            'Values': [vpc_id]
                        }
                    ]
                )

                if not subnet_response.get("Subnets") or len(subnet_response["Subnets"]) < 2:
                    return False, "Not enough subnets found in the VPC. ElastiCache requires at least 2 subnets."

                # Use the first two subnets
                subnet_ids = [subnet["SubnetId"] for subnet in subnet_response["Subnets"][:2]]

            # Create a new subnet group
            new_subnet_group_name = f"custom-subnet-group-{cluster_id}"

            await self.client.create_cache_subnet_group(
                CacheSubnetGroupName=new_subnet_group_name,
                CacheSubnetGroupDescription=f"Custom subnet group for {cluster_id}",
                SubnetIds=subnet_ids
            )

            # Modify the cluster to use the new subnet group
            await self.client.modify_cache_cluster(
                CacheClusterId=cluster_id,
                CacheSubnetGroupName=new_subnet_group_name,
                ApplyImmediately=True
            )

            self.remediation_results["non_default_subnet_group"]["field_updates"]["subnet_group_name"] = subnet_group_name

            logger.info(f"Successfully created and assigned non-default subnet group for ElastiCache cluster {cluster_id}")
            return True, f"Successfully created and assigned non-default subnet group for ElastiCache cluster {cluster_id}"

        except Exception as e:
            error_msg = f"Failed to assign non-default subnet group: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def get_remediation_functions(self):
        """
        Return a mapping of policy checks to remediation functions.
        """
        return {
            "automatic_backups_enabled": self.remediate_automatic_backups_enabled,
            "automatic_minor_version_upgrades_enabled": self.remediate_automatic_minor_version_upgrades_enabled,
            "automatic_failover_enabled": self.remediate_automatic_failover_enabled,
            "encrypted_at_rest": self.remediate_encrypted_at_rest,
            "encrypted_in_transit": self.remediate_encrypted_in_transit,
            "redis_auth_enabled": self.remediate_redis_auth_enabled,
            "non_default_subnet_group": self.remediate_non_default_subnet_group
        }

    async def remediate(self, policy_check, details):
        """
        Main remediation method that delegates to specific remediation functions.
        """
        logger.info(f"Starting remediation for ElastiCache policy check: {policy_check}")

        # Initialize updated details with the original details
        updated_details = details.copy()

        # Initialize AWS client
        session = self.get_session()
        async with session.client(AWSServiceNameEnum.ElastiCache.value, region_name=details.get("region", "us-east-1")) as client:
            self.client = client

            # Get the appropriate remediation function
            remediation_functions = self.get_remediation_functions()
            if policy_check not in remediation_functions:
                error_msg = f"No remediation function found for policy check: {policy_check}"
                logger.warning(error_msg)

                # Update remediation results
                if policy_check in self.remediation_results:
                    self.remediation_results[policy_check]["status"] = False
                    self.remediation_results[policy_check]["message"] = error_msg

                # Add remediation failure information
                updated_details["remediate"] = {
                    "status": "fail",
                    "message": f"Error: {error_msg}"
                }

                return False, error_msg, updated_details

            # Call the appropriate remediation function
            success, message = await remediation_functions[policy_check](details)

            # Update remediation results
            if policy_check in self.remediation_results:
                self.remediation_results[policy_check]["status"] = success
                self.remediation_results[policy_check]["message"] = message

            # Update the details with remediation information
            updated_details["remediate"] = {
                "status": "pass" if success else "fail",
                "message": message
            }

            # If successful, update the field values
            if success and policy_check in self.remediation_results:
                for field, value in self.remediation_results[policy_check]["field_updates"].items():
                    updated_details[field] = value

                # Update compliance status
                updated_details["compliance"] = True

            return success, message, updated_details
