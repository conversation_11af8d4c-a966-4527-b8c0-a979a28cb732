import asyncio
from celery.utils.log import get_task_logger
from ..config import BaseChecksProcessor
from app.common import (AWSServiceNameEnum, SeverityEnum, ResourceComplianceStatusEnum,
                        EFSChecksDescriptionEnum)

__all__ = ['ChecksProcessor']

logger = get_task_logger(__name__)


class ChecksProcessor(BaseChecksProcessor):
    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.file_systems = None
        self.findings = {
            "encrypted_with_kms": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.ENCRYPTED_WITH_KMS.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "included_in_backup_plans": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.INCLUDED_IN_BACKUP_PLANS.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "secure_access_points": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.SECURE_ACCESS_POINTS.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "mount_targets_not_in_public_subnet": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.MOUNT_TARGETS_NOT_IN_PUBLIC_SUBNET.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "automatic_backups_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.AUTOMATIC_BACKUPS_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "multi_az_configuration": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.MULTI_AZ_CONFIGURATION.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "vpc_access_policy_restriction": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.VPC_ACCESS_POLICY_RESTRICTION.value,
                "severity": SeverityEnum.MEDIUM.value
            }
        }

    async def check_encrypted_with_kms(self):
        """
        Elastic File System should be configured to encrypt file data at-rest using AWS KMS.
        """
        all_compliant = True

        for fs in self.file_systems.get("FileSystems", []):
            fs_id = fs.get("FileSystemId")
            encrypted = fs.get("Encrypted", False)
            kms_key_id = fs.get("KmsKeyId")
            is_encrypted_kms = encrypted and kms_key_id is not None

            self.findings["encrypted_with_kms"]["details"].append({
                "file_system_id": fs_id,
                "encrypted": encrypted,
                "kms_key_id": kms_key_id,
                "compliance": is_encrypted_kms,
                "region": self.client.meta.region_name
            })

            if not is_encrypted_kms:
                all_compliant = False

        if self.findings["encrypted_with_kms"]["status"] and not all_compliant:
            self.findings["encrypted_with_kms"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_included_in_backup_plans(self):
        """
        Amazon EFS volumes should be in backup plans.
        """
        all_compliant = True

        # Fetch all protected resources from AWS Backup
        protected_resources = []
        session = self.get_session()
        async with session.client(AWSServiceNameEnum.BACKUP.value,
                                  region_name=self.client.meta.region_name) as backup_client:
            paginator = backup_client.get_paginator("list_protected_resources")
            async for page in paginator.paginate():
                protected_resources.extend(page.get("Results", []))

        for fs in self.file_systems.get("FileSystems", []):
            fs_id = fs.get("FileSystemId")
            is_in_backup_plan = any(
                resource.get("ResourceArn", "").endswith(fs_id) for resource in protected_resources
            )

            self.findings["included_in_backup_plans"]["details"].append({
                "file_system_id": fs_id,
                # "is_in_backup_plan": is_in_backup_plan,
                "compliance": is_in_backup_plan,
                "region": self.client.meta.region_name
            })

            if not is_in_backup_plan:
                all_compliant = False

        if self.findings["included_in_backup_plans"]["status"] and not all_compliant:
            self.findings["included_in_backup_plans"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_secure_access_points(self):
        """
        EFS access points should enforce both a root directory and user identity.
        """
        all_compliant = True

        for fs in self.file_systems.get("FileSystems", []):
            fs_id = fs.get("FileSystemId")
            access_points = await self.client.describe_access_points(FileSystemId=fs_id)

            for ap in access_points.get("AccessPoints", []):
                ap_id = ap.get("AccessPointId")

                # Check root directory enforcement
                root_directory = ap.get("RootDirectory", {})
                path = root_directory.get("Path")
                is_root_directory_enforced = bool(path)

                # Check user identity enforcement
                posix_user = ap.get("PosixUser", {})
                uid = posix_user.get("Uid")
                gid = posix_user.get("Gid")
                is_user_identity_enforced = uid is not None and gid is not None

                # Both conditions must be met for compliance
                is_compliant = is_root_directory_enforced and is_user_identity_enforced

                self.findings["secure_access_points"]["details"].append({
                    "file_system_id": fs_id,
                    "access_point_id": ap_id,
                    # "root_directory_path": path,
                    "is_root_directory_enforced": is_root_directory_enforced,
                    # "uid": uid,
                    # "gid": gid,
                    "is_user_identity_enforced": is_user_identity_enforced,
                    "compliance": is_compliant,
                    "region": self.client.meta.region_name
                })

                if not is_compliant:
                    all_compliant = False

        if self.findings["secure_access_points"]["status"] and not all_compliant:
            self.findings["secure_access_points"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_mount_targets_not_in_public_subnet(self):
        """
        EFS mount targets should not be associated with a public subnet.
        """
        all_compliant = True

        for fs in self.file_systems.get("FileSystems", []):
            fs_id = fs.get("FileSystemId")
            mount_targets = await self.client.describe_mount_targets(FileSystemId=fs_id)

            for mt in mount_targets.get("MountTargets", []):
                mt_id = mt.get("MountTargetId")
                subnet_id = mt.get("SubnetId")

                # Fetch subnet details
                session = self.get_session()
                async with session.client('ec2', region_name=self.client.meta.region_name) as ec2_client:
                    subnet_details = await ec2_client.describe_subnets(SubnetIds=[subnet_id])
                    is_public_subnet = any(
                        subnet.get("MapPublicIpOnLaunch", False) for subnet in subnet_details.get("Subnets", [])
                    )

                self.findings["mount_targets_not_in_public_subnet"]["details"].append({
                    "file_system_id": fs_id,
                    "mount_target_id": mt_id,
                    "subnet_id": subnet_id,
                    # "is_public_subnet": is_public_subnet,
                    "compliance": not is_public_subnet,
                    "region": self.client.meta.region_name
                })

                if is_public_subnet:
                    all_compliant = False

        if self.findings["mount_targets_not_in_public_subnet"]["status"] and not all_compliant:
            self.findings["mount_targets_not_in_public_subnet"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_automatic_backups_enabled(self):
        """
        EFS file systems should have automatic backups enabled.
        """
        all_compliant = True

        for fs in self.file_systems.get("FileSystems", []):
            fs_id = fs.get("FileSystemId")
            backup_policy = await self.client.describe_backup_policy(FileSystemId=fs_id)
            is_backup_enabled = backup_policy.get("BackupPolicy", {}).get("Status") == "ENABLED"

            self.findings["automatic_backups_enabled"]["details"].append({
                "file_system_id": fs_id,
                # "is_backup_enabled": is_backup_enabled,
                "compliance": is_backup_enabled,
                "region": self.client.meta.region_name
            })

            if not is_backup_enabled:
                all_compliant = False

        if self.findings["automatic_backups_enabled"]["status"] and not all_compliant:
            self.findings["automatic_backups_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_multi_az_configuration(self):
        """
        Check if EFS file systems are configured with Multi-AZ for high availability.

        This check verifies that EFS file systems have mount targets in multiple
        Availability Zones to ensure high availability and fault tolerance.
        """
        all_compliant = True
        region = self.client.meta.region_name

        for fs in self.file_systems.get("FileSystems", []):
            fs_id = fs.get("FileSystemId", "")
            fs_name = fs.get("Name", "")
            throughput_mode = fs.get("ThroughputMode", "")

            try:
                # Get mount targets for this file system
                mount_targets_response = await self.client.describe_mount_targets(FileSystemId=fs_id)
                mount_targets = mount_targets_response.get("MountTargets", [])

                # Analyze Multi-AZ configuration
                az_analysis = await self._analyze_multi_az_configuration(mount_targets, region)

                # Determine compliance
                is_multi_az = az_analysis["is_multi_az"]
                compliance_status = is_multi_az

                # Update overall compliance status
                if not compliance_status:
                    all_compliant = False

                # Add finding details
                self.findings["multi_az_configuration"]["details"].append({
                    "file_system_id": fs_id,
                    "file_system_name": fs_name,
                    "throughput_mode": throughput_mode,
                    "region": region,
                    "mount_targets_count": az_analysis["mount_targets_count"],
                    "availability_zones_count": az_analysis["availability_zones_count"],
                    # "is_multi_az": is_multi_az,
                    "compliance": compliance_status,
                })

            except Exception as e:
                logger.error(f"Error checking Multi-AZ configuration for EFS {fs_id}: {str(e)}")
                all_compliant = False

                # Add error finding
                self.findings["multi_az_configuration"]["details"].append({
                    "file_system_id": fs_id,
                    "file_system_name": fs_name,
                    "region": region,
                    "mount_targets_count": 0,
                    "availability_zones_count": 0,
                    "is_multi_az": False,
                    "compliance": False,
                    "error": str(e),
                })

        # Update overall finding status
        if not all_compliant:
            self.findings["multi_az_configuration"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def _analyze_multi_az_configuration(self, mount_targets, region):
        """
        Analyze the Multi-AZ configuration of an EFS file system based on its mount targets.

        Args:
            mount_targets: List of mount targets for the EFS file system
            region: AWS region name

        Returns:
            dict: Analysis results including AZ distribution and Multi-AZ status
        """
        availability_zones = set()
        mount_target_details = []

        # Get EC2 client to fetch subnet details
        session = self.get_session()

        try:
            async with session.client('ec2', region_name=region) as ec2_client:
                for mt in mount_targets:
                    mt_id = mt.get("MountTargetId", "")
                    subnet_id = mt.get("SubnetId", "")
                    ip_address = mt.get("IpAddress", "")
                    network_interface_id = mt.get("NetworkInterfaceId", "")

                    # Get subnet details to determine AZ
                    try:
                        subnet_response = await ec2_client.describe_subnets(SubnetIds=[subnet_id])
                        subnets = subnet_response.get("Subnets", [])

                        if subnets:
                            subnet = subnets[0]
                            az = subnet.get("AvailabilityZone", "")
                            vpc_id = subnet.get("VpcId", "")

                            if az:
                                availability_zones.add(az)

                            mount_target_details.append({
                                "mount_target_id": mt_id,
                                "subnet_id": subnet_id,
                                "availability_zone": az,
                                "vpc_id": vpc_id,
                                "ip_address": ip_address,
                                "network_interface_id": network_interface_id
                            })
                        else:
                            # Subnet not found, add with unknown AZ
                            mount_target_details.append({
                                "mount_target_id": mt_id,
                                "subnet_id": subnet_id,
                                "availability_zone": "unknown",
                                "vpc_id": "unknown",
                                "ip_address": ip_address,
                                "network_interface_id": network_interface_id
                            })

                    except Exception as e:
                        logger.warning(f"Error getting subnet details for {subnet_id}: {str(e)}")
                        mount_target_details.append({
                            "mount_target_id": mt_id,
                            "subnet_id": subnet_id,
                            "availability_zone": "error",
                            "vpc_id": "error",
                            "ip_address": ip_address,
                            "network_interface_id": network_interface_id,
                            "error": str(e)
                        })

        except Exception as e:
            logger.error(f"Error creating EC2 client for Multi-AZ analysis: {str(e)}")

        # Determine if Multi-AZ (requires at least 2 AZs)
        availability_zones_list = sorted(list(availability_zones))
        is_multi_az = len(availability_zones_list) >= 2

        return {
            "mount_targets_count": len(mount_targets),
            "availability_zones_count": len(availability_zones_list),
            "is_multi_az": is_multi_az,
        }

    async def check_vpc_access_policy_restriction(self):
        """
        Check if EFS file systems have policies that allow unrestricted access to any client within the VPC.

        This check examines EFS resource policies to identify overly permissive configurations
        that could allow unauthorized access from any client within the VPC.
        """
        all_compliant = True
        region = self.client.meta.region_name

        for fs in self.file_systems.get("FileSystems", []):
            fs_id = fs.get("FileSystemId", "")
            fs_name = fs.get("Name", "")
            performance_mode = fs.get("PerformanceMode", "")

            try:
                # Get the file system policy
                policy_analysis = await self._analyze_efs_policy(fs_id)

                # Determine compliance
                has_unrestricted_vpc_access = policy_analysis["has_unrestricted_vpc_access"]
                compliance_status = not has_unrestricted_vpc_access

                # Update overall compliance status
                if not compliance_status:
                    all_compliant = False

                # Add finding details
                self.findings["vpc_access_policy_restriction"]["details"].append({
                    "file_system_id": fs_id,
                    "file_system_name": fs_name,
                    "performance_mode": performance_mode,
                    "region": region,
                    # "has_policy": policy_analysis["has_policy"],
                    # "has_unrestricted_vpc_access": has_unrestricted_vpc_access,
                    "compliance": compliance_status,
                })

            except Exception as e:
                logger.error(f"Error checking VPC access policy for EFS {fs_id}: {str(e)}")
                all_compliant = False

                # Add error finding
                self.findings["vpc_access_policy_restriction"]["details"].append({
                    "file_system_id": fs_id,
                    "file_system_name": fs_name,
                    "region": region,
                    "has_policy": False,
                    "has_unrestricted_vpc_access": False,
                    "compliance": False,
                    "error": str(e),
                })

        # Update overall finding status
        if not all_compliant:
            self.findings["vpc_access_policy_restriction"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def _analyze_efs_policy(self, fs_id):
        """
        Analyze the EFS file system policy for unrestricted VPC access.

        Args:
            fs_id: EFS file system ID

        Returns:
            dict: Policy analysis results
        """
        import json

        policy_analysis = {
            "has_policy": False,
            "policy_document": None,
            "has_unrestricted_vpc_access": False,
            "analysis_details": {}
        }

        try:
            # Get the file system policy
            policy_response = await self.client.describe_file_system_policy(FileSystemId=fs_id)
            policy_document = policy_response.get("Policy")

            if not policy_document:
                policy_analysis["analysis_details"]["status"] = "no_policy"
                policy_analysis["analysis_details"]["message"] = "No resource policy configured"
                return policy_analysis

            policy_analysis["has_policy"] = True

            # Parse the policy document
            try:
                policy_json = json.loads(policy_document)
                policy_analysis["policy_document"] = policy_json
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse policy document for EFS {fs_id}: {str(e)}")
                policy_analysis["analysis_details"]["status"] = "parse_error"
                policy_analysis["analysis_details"]["message"] = f"Failed to parse policy document: {str(e)}"
                return policy_analysis

            # Analyze policy statements for unrestricted VPC access
            statements = policy_json.get("Statement", [])
            if not isinstance(statements, list):
                statements = [statements]


            for i, statement in enumerate(statements):
                statement_analysis = self._analyze_policy_statement(statement, i)

                if statement_analysis["is_unrestricted_vpc_access"]:
                    policy_analysis["has_unrestricted_vpc_access"] = True

            policy_analysis["analysis_details"]["status"] = "analyzed"
            policy_analysis["analysis_details"]["total_statements"] = len(statements)

        except Exception as e:
            if "PolicyNotFound" in str(e) or "does not have a policy" in str(e):
                policy_analysis["analysis_details"]["status"] = "no_policy"
                policy_analysis["analysis_details"]["message"] = "No resource policy configured"
            else:
                logger.error(f"Error retrieving policy for EFS {fs_id}: {str(e)}")
                policy_analysis["analysis_details"]["status"] = "error"
                policy_analysis["analysis_details"]["message"] = str(e)

        return policy_analysis

    def _analyze_policy_statement(self, statement, statement_index):
        """
        Analyze a single policy statement for unrestricted VPC access patterns.

        Args:
            statement: Policy statement to analyze
            statement_index: Index of the statement in the policy

        Returns:
            dict: Statement analysis results
        """
        analysis = {
            "statement_index": statement_index,
            "statement": statement,
            "is_unrestricted_vpc_access": False,
            "risk_factors": [],
            "details": {}
        }

        # Check if this is an Allow statement
        effect = statement.get("Effect", "").lower()
        if effect != "allow":
            analysis["details"]["effect"] = effect
            analysis["details"]["reason"] = "Not an Allow statement"
            return analysis

        # Analyze Principal
        principal = statement.get("Principal", {})
        principal_analysis = self._analyze_principal(principal)
        analysis["details"]["principal_analysis"] = principal_analysis

        # Analyze Condition
        condition = statement.get("Condition", {})
        condition_analysis = self._analyze_condition(condition)
        analysis["details"]["condition_analysis"] = condition_analysis

        # Analyze Action
        action = statement.get("Action", [])
        action_analysis = self._analyze_action(action)
        analysis["details"]["action_analysis"] = action_analysis

        # Determine if this represents unrestricted VPC access
        risk_factors = []

        # Check for overly broad principals
        if principal_analysis["is_overly_broad"]:
            risk_factors.append("overly_broad_principal")

        # Check for missing or weak conditions
        if not condition_analysis["has_restrictive_conditions"]:
            risk_factors.append("missing_restrictive_conditions")

        # Check for broad actions
        if action_analysis["has_broad_actions"]:
            risk_factors.append("broad_actions")

        # Check for VPC-wide access patterns
        if condition_analysis["allows_vpc_wide_access"]:
            risk_factors.append("vpc_wide_access")

        analysis["risk_factors"] = risk_factors

        # Determine if this is unrestricted VPC access
        # Consider it unrestricted if it has multiple risk factors
        if len(risk_factors) >= 2:
            analysis["is_unrestricted_vpc_access"] = True

        return analysis

    def _analyze_principal(self, principal):
        """Analyze the Principal field of a policy statement"""
        analysis = {
            "principal": principal,
            "is_overly_broad": False,
            "principal_type": "unknown",
            "details": []
        }

        if principal == "*":
            analysis["is_overly_broad"] = True
            analysis["principal_type"] = "wildcard"
            analysis["details"].append("Allows access from any principal")
        elif isinstance(principal, dict):
            if principal.get("AWS") == "*":
                analysis["is_overly_broad"] = True
                analysis["principal_type"] = "aws_wildcard"
                analysis["details"].append("Allows access from any AWS principal")
            elif "AWS" in principal:
                aws_principals = principal["AWS"]
                if isinstance(aws_principals, list):
                    analysis["principal_type"] = "aws_list"
                    analysis["details"].append(f"Allows access from {len(aws_principals)} AWS principals")
                else:
                    analysis["principal_type"] = "aws_single"
                    analysis["details"].append("Allows access from specific AWS principal")

        return analysis

    def _analyze_condition(self, condition):
        """Analyze the Condition field of a policy statement"""
        analysis = {
            "condition": condition,
            "has_restrictive_conditions": False,
            "allows_vpc_wide_access": False,
            "condition_types": [],
            "details": []
        }

        if not condition:
            analysis["details"].append("No conditions specified")
            analysis["allows_vpc_wide_access"] = True
            return analysis

        # Check for common restrictive conditions
        restrictive_conditions = [
            "IpAddress", "IpAddressIfExists", "StringEquals", "StringLike",
            "DateGreaterThan", "DateLessThan", "Bool"
        ]

        for condition_type in condition.keys():
            analysis["condition_types"].append(condition_type)

            if condition_type in restrictive_conditions:
                analysis["has_restrictive_conditions"] = True

            # Check for VPC-wide access patterns
            if condition_type == "IpAddress":
                ip_conditions = condition[condition_type]
                if isinstance(ip_conditions, dict):
                    for key, value in ip_conditions.items():
                        if isinstance(value, str) and ("10.0.0.0/8" in value or "**********/12" in value or "***********/16" in value):
                            analysis["allows_vpc_wide_access"] = True
                            analysis["details"].append(f"Allows access from broad IP range: {value}")

        return analysis

    def _analyze_action(self, action):
        """Analyze the Action field of a policy statement"""
        analysis = {
            "action": action,
            "has_broad_actions": False,
            "action_count": 0,
            "details": []
        }

        actions = action if isinstance(action, list) else [action]
        analysis["action_count"] = len(actions)

        # Check for overly broad actions
        broad_actions = ["*", "elasticfilesystem:*", "efs:*"]

        for act in actions:
            if act in broad_actions:
                analysis["has_broad_actions"] = True
                analysis["details"].append(f"Broad action: {act}")

        return analysis

    def get_check_functions(self):
        return [
            self.check_encrypted_with_kms,
            self.check_included_in_backup_plans,
            self.check_secure_access_points,
            self.check_mount_targets_not_in_public_subnet,
            self.check_automatic_backups_enabled,
            self.check_multi_az_configuration,
            self.check_vpc_access_policy_restriction
        ]

    async def run_checks(self):
        for region in self.regions:
            logger.info(f"Scanning region: {region}")
            session = self.get_session(region)
            if not await self.is_region_accessible():
                logger.warning(f"Region {region} is not accessible. Skipping checks.")
                continue
            async with session.client(AWSServiceNameEnum.EFS.value, region_name=region) as client:
                self.client = client

                self.file_systems = await self.client.describe_file_systems()

                for check_function in self.get_check_functions():
                    await check_function()
            await asyncio.sleep(5)

        return self.findings
