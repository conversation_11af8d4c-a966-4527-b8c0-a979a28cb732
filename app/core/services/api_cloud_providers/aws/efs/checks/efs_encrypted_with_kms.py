from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EFSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.efs.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "encrypted_with_kms": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.ENCRYPTED_WITH_KMS.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            file_systems = cached.get("file_systems", {}).get("FileSystems", [])

            for fs in file_systems:
                fs_id = fs.get("FileSystemId")
                encrypted = fs.get("Encrypted", False)
                kms_key_id = fs.get("KmsKeyId")
                is_encrypted_kms = encrypted and kms_key_id is not None

                if findings["encrypted_with_kms"]["status"] == ResourceComplianceStatusEnum.PASS.value and not is_encrypted_kms:
                    findings["encrypted_with_kms"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["encrypted_with_kms"]["details"].append({
                    "file_system_id": fs_id,
                    # "encrypted": encrypted,
                    # "kms_key_id": kms_key_id,
                    "compliance": is_encrypted_kms,
                    "region": region,
                })

        return findings

    def remediate(self):
        pass