from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EFSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.efs.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "multi_az_configuration": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.MULTI_AZ_CONFIGURATION.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            file_systems = cached.get("file_systems", {}).get("FileSystems", [])
            mount_targets_data = cached.get("mount_targets", {})

            for fs in file_systems:
                fs_id = fs.get("FileSystemId", "")
                fs_name = fs.get("Name", "")
                throughput_mode = fs.get("ThroughputMode", "")

                try:
                    # Get mount targets for this file system from cached data
                    mount_targets = mount_targets_data.get(fs_id, {}).get("MountTargets", [])

                    # Simple Multi-AZ check based on mount target count
                    # A proper multi-AZ setup should have at least 2 mount targets
                    mount_targets_count = len(mount_targets)
                    is_multi_az = mount_targets_count >= 2

                    if findings["multi_az_configuration"]["status"] == ResourceComplianceStatusEnum.PASS.value and not is_multi_az:
                        findings["multi_az_configuration"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                    # Add finding details
                    findings["multi_az_configuration"]["details"].append({
                        "file_system_id": fs_id,
                        "file_system_name": fs_name,
                        "throughput_mode": throughput_mode,
                        "region": region,
                        "mount_targets_count": mount_targets_count,
                        "compliance": is_multi_az,
                    })

                except Exception as e:
                    if findings["multi_az_configuration"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                        findings["multi_az_configuration"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                    # Add error finding
                    findings["multi_az_configuration"]["details"].append({
                        "file_system_id": fs_id,
                        "file_system_name": fs_name,
                        "region": region,
                        "mount_targets_count": 0,
                        "compliance": False,
                        "error": str(e),
                    })

        return findings

    def remediate(self):
        pass