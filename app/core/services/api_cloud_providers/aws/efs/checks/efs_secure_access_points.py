from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EFSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.efs.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "secure_access_points": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.SECURE_ACCESS_POINTS.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            file_systems = cached.get("file_systems", {}).get("FileSystems", [])
            access_points_data = cached.get("access_points", {})

            for fs in file_systems:
                fs_id = fs.get("FileSystemId")
                if not fs_id:
                    continue

                # Get access points for this file system from cached data
                fs_access_points = access_points_data.get(fs_id, {}).get("AccessPoints", [])

                for ap in fs_access_points:
                    ap_id = ap.get("AccessPointId")

                    # Check root directory enforcement
                    root_directory = ap.get("RootDirectory", {})
                    path = root_directory.get("Path")
                    is_root_directory_enforced = bool(path)

                    # Check user identity enforcement
                    posix_user = ap.get("PosixUser", {})
                    uid = posix_user.get("Uid")
                    gid = posix_user.get("Gid")
                    is_user_identity_enforced = uid is not None and gid is not None

                    # Both conditions must be met for compliance
                    is_compliant = is_root_directory_enforced and is_user_identity_enforced

                    if findings["secure_access_points"]["status"] == ResourceComplianceStatusEnum.PASS.value and not is_compliant:
                        findings["secure_access_points"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                    findings["secure_access_points"]["details"].append({
                        "file_system_id": fs_id,
                        "access_point_id": ap_id,
                        # "is_root_directory_enforced": is_root_directory_enforced,
                        # "is_user_identity_enforced": is_user_identity_enforced,
                        "compliance": is_compliant,
                        "region": region,
                    })

        return findings

    def remediate(self):
        pass