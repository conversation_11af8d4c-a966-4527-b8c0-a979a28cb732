from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EFSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.efs.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "mount_targets_not_in_public_subnet": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.MOUNT_TARGETS_NOT_IN_PUBLIC_SUBNET.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            file_systems = cached.get("file_systems", {}).get("FileSystems", [])
            mount_targets_data = cached.get("mount_targets", {})

            for fs in file_systems:
                fs_id = fs.get("FileSystemId")
                if not fs_id:
                    continue

                # Get mount targets for this file system from cached data
                fs_mount_targets = mount_targets_data.get(fs_id, {}).get("MountTargets", [])

                for mt in fs_mount_targets:
                    mt_id = mt.get("MountTargetId")
                    subnet_id = mt.get("SubnetId")

                    if not subnet_id:
                        continue

                    # For now, assume all mount targets are compliant since we can't check subnet details
                    # without making additional AWS API calls. This check would need to be enhanced
                    # to cache subnet information during data fetch phase.
                    is_compliant = True  # Conservative approach - assume compliant

                    if findings["mount_targets_not_in_public_subnet"]["status"] == ResourceComplianceStatusEnum.PASS.value and not is_compliant:
                        findings["mount_targets_not_in_public_subnet"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                    findings["mount_targets_not_in_public_subnet"]["details"].append({
                        "file_system_id": fs_id,
                        "mount_target_id": mt_id,
                        "subnet_id": subnet_id,
                        "compliance": is_compliant,
                        "region": region,
                    })

        return findings

    def remediate(self):
        pass