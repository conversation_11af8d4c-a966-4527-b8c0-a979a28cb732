from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EFSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.efs.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "automatic_backups_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.AUTOMATIC_BACKUPS_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            file_systems = cached.get("file_systems", {}).get("FileSystems", [])
            backup_policies_data = cached.get("backup_policies", {})

            for fs in file_systems:
                fs_id = fs.get("FileSystemId")
                if not fs_id:
                    continue

                # Get backup policy for this file system from cached data
                backup_policy = backup_policies_data.get(fs_id, {}).get("BackupPolicy", {})
                is_backup_enabled = backup_policy.get("Status") == "ENABLED"

                if findings["automatic_backups_enabled"]["status"] == ResourceComplianceStatusEnum.PASS.value and not is_backup_enabled:
                    findings["automatic_backups_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["automatic_backups_enabled"]["details"].append({
                    "file_system_id": fs_id,
                    "compliance": is_backup_enabled,
                    "region": region,
                })

        return findings

    def remediate(self):
        pass