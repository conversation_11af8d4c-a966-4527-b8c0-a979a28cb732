from typing import Dict, Any, List
import json

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EFSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.efs.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "vpc_access_policy_restriction": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.VPC_ACCESS_POLICY_RESTRICTION.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            file_systems = cached.get("file_systems", {}).get("FileSystems", [])
            file_system_policies_data = cached.get("file_system_policies", {})

            for fs in file_systems:
                fs_id = fs.get("FileSystemId")
                if not fs_id:
                    continue

                # Get file system policy from cached data
                policy_data = file_system_policies_data.get(fs_id, {})
                policy_document = policy_data.get("Policy")

                # Check if policy exists and restricts access
                has_restrictive_policy = self._check_policy_restriction(policy_document)

                if findings["vpc_access_policy_restriction"]["status"] == ResourceComplianceStatusEnum.PASS.value and not has_restrictive_policy:
                    findings["vpc_access_policy_restriction"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["vpc_access_policy_restriction"]["details"].append({
                    "file_system_id": fs_id,
                    # "has_policy": policy_document is not None,
                    # "has_restrictive_policy": has_restrictive_policy,
                    "compliance": has_restrictive_policy,
                    "region": region,
                })

        return findings

    def _check_policy_restriction(self, policy_document: str) -> bool:
        """
        Check if the EFS file system policy has restrictive access controls.
        
        Args:
            policy_document: JSON string of the file system policy
            
        Returns:
            bool: True if policy has restrictive controls, False otherwise
        """
        if not policy_document:
            return False

        try:
            policy = json.loads(policy_document)
            statements = policy.get("Statement", [])

            # Check for restrictive conditions
            for statement in statements:
                effect = statement.get("Effect", "")
                conditions = statement.get("Condition", {})

                # Look for VPC-based restrictions
                if effect == "Allow" and conditions:
                    # Check for VPC endpoint restrictions
                    if "StringEquals" in conditions:
                        string_equals = conditions["StringEquals"]
                        if "aws:sourceVpce" in string_equals or "aws:sourceVpc" in string_equals:
                            return True

                    # Check for IP address restrictions
                    if "IpAddress" in conditions:
                        ip_address = conditions["IpAddress"]
                        if "aws:sourceIp" in ip_address:
                            return True

                    # Check for secure transport requirement
                    if "Bool" in conditions:
                        bool_conditions = conditions["Bool"]
                        if "aws:SecureTransport" in bool_conditions:
                            return True

                # Check for explicit deny statements
                if effect == "Deny":
                    return True

            return False

        except (json.JSONDecodeError, KeyError, TypeError):
            return False

    def remediate(self):
        pass