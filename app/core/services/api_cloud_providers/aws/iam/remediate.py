import json
from celery.utils.log import get_task_logger
from ..config import BaseRemediationProcessor
from app.common import AWSServiceNameEnum, ResourceComplianceStatusEnum

__all__ = ['RemediationProcessor']

logger = get_task_logger(__name__)


class RemediationProcessor(BaseRemediationProcessor):
    """
    Processor for remediating IAM compliance issues.
    Inherits from BaseChecksProcessor to reuse AWS session management.
    """

    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.remediation_results = {
            "no_full_admin_privileges": {
                "status": False,
                "message": "",
                "field_updates": {"has_full_admin_privileges": False}
            },
            "no_iam_policies_for_users": {
                "status": False,
                "message": "",
                "field_updates": {"has_attached_policies": False}
            },
            "no_wild_card_actions_in_custom_policies": {
                "status": False,
                "message": "",
                "field_updates": {"has_wildcard_actions": False}
            },
            "access_key_rotation": {
                "status": False,
                "message": "",
                "field_updates": {"is_key_old": False}
            },
            "no_root_user_access_key": {
                "status": False,
                "message": "",
                "field_updates": {"has_root_access_key": False}
            },
            "mfa_enabled_for_console_users": {
                "status": False,
                "message": "",
                "field_updates": {"has_mfa_enabled": True}
            },
            "hardware_mfa_for_root_user": {
                "status": False,
                "message": "",
                "field_updates": {}
            },
            "strong_password_policies": {
                "status": False,
                "message": "",
                "field_updates": {"is_strong_policy": True}
            },
            "remove_unused_user_credentials": {
                "status": False,
                "message": "",
                "field_updates": {}
            }
        }

    async def remediate_no_full_admin_privileges(self, details):
        """
        Remediate IAM policies that allow full '*' administrative privileges.
        """
        try:
            policy_arn = details.get("policy_arn", "")
            if not policy_arn:
                return False, "No policy ARN provided"
                
            # Get the policy details
            policy = await self.client.get_policy(PolicyArn=policy_arn)
            policy_version = await self.client.get_policy_version(
                PolicyArn=policy_arn,
                VersionId=policy["Policy"]["DefaultVersionId"]
            )
            
            # Get the policy document
            policy_doc = policy_version["PolicyVersion"]["Document"]
            statements = policy_doc.get("Statement", [])
            
            # Convert to list if it's not already
            if not isinstance(statements, list):
                statements = [statements]
            
            # Modify statements to remove full admin privileges
            modified = False
            for i, statement in enumerate(statements):
                actions = statement.get("Action", [])
                resources = statement.get("Resource", [])
                effect = statement.get("Effect", "")
                
                if not isinstance(actions, list):
                    actions = [actions]
                if not isinstance(resources, list):
                    resources = [resources]
                
                # Check if this statement grants full admin privileges
                if effect == "Allow" and "*" in actions and "*" in resources:
                    # Replace wildcard actions with more specific ones
                    if "*" in actions:
                        # Replace with read-only actions as a safer alternative
                        statements[i]["Action"] = [
                            "iam:Get*", 
                            "iam:List*", 
                            "iam:Generate*"
                        ]
                    
                    # Replace wildcard resources with more specific ones if possible
                    if "*" in resources:
                        # Use the policy's own ARN as a more specific resource
                        statements[i]["Resource"] = [policy_arn]
                    
                    modified = True
            
            if modified:
                # Create a new policy version
                await self.client.create_policy_version(
                    PolicyArn=policy_arn,
                    PolicyDocument=json.dumps(policy_doc),
                    SetAsDefault=True
                )
                
                # Delete the oldest policy version if we have 5 versions (AWS limit)
                policy_versions = await self.client.list_policy_versions(PolicyArn=policy_arn)
                if len(policy_versions["Versions"]) >= 5:
                    # Find the oldest non-default version
                    versions_to_delete = [v for v in policy_versions["Versions"] 
                                         if not v["IsDefaultVersion"]]
                    versions_to_delete.sort(key=lambda x: x["CreateDate"])
                    
                    if versions_to_delete:
                        oldest_version = versions_to_delete[0]
                        await self.client.delete_policy_version(
                            PolicyArn=policy_arn,
                            VersionId=oldest_version["VersionId"]
                        )
                
                logger.info(f"Successfully removed full admin privileges from policy {policy_arn}")
                return True, f"Successfully removed full admin privileges from policy {policy_arn}"
            else:
                return False, f"No full admin privileges found in policy {policy_arn}"
                
        except Exception as e:
            error_msg = f"Failed to remediate full admin privileges for policy {policy_arn}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_no_iam_policies_for_users(self, details):
        """
        Remediate IAM users that have IAM policies attached directly.
        """
        try:
            user_name = details.get("user_name", "")
            if not user_name:
                return False, "No user name provided"
                
            # List attached policies
            attached_policies = await self.client.list_attached_user_policies(UserName=user_name)
            
            # Detach each policy
            for policy in attached_policies.get("AttachedPolicies", []):
                policy_arn = policy["PolicyArn"]
                await self.client.detach_user_policy(
                    UserName=user_name,
                    PolicyArn=policy_arn
                )
                logger.info(f"Detached policy {policy_arn} from user {user_name}")
            
            # Check if we detached any policies
            if attached_policies.get("AttachedPolicies", []):
                logger.info(f"Successfully detached all policies from user {user_name}")
                return True, f"Successfully detached all policies from user {user_name}"
            else:
                return False, f"No policies to detach from user {user_name}"
                
        except Exception as e:
            error_msg = f"Failed to detach policies from user {user_name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_no_wild_card_actions_in_custom_policies(self, details):
        """
        Remediate IAM customer managed policies that allow wildcard actions for services.
        """
        try:
            policy_arn = details.get("policy_arn", "")
            if not policy_arn:
                return False, "No policy ARN provided"
                
            # Get the policy details
            policy = await self.client.get_policy(PolicyArn=policy_arn)
            policy_version = await self.client.get_policy_version(
                PolicyArn=policy_arn,
                VersionId=policy["Policy"]["DefaultVersionId"]
            )
            
            # Get the policy document
            policy_doc = policy_version["PolicyVersion"]["Document"]
            statements = policy_doc.get("Statement", [])
            
            # Convert to list if it's not already
            if not isinstance(statements, list):
                statements = [statements]
            
            # Modify statements to remove wildcard actions
            modified = False
            for i, statement in enumerate(statements):
                actions = statement.get("Action", [])
                effect = statement.get("Effect", "")
                
                if not isinstance(actions, list):
                    actions = [actions]
                
                # Check if this statement has wildcard actions
                if effect == "Allow":
                    wildcard_actions = [action for action in actions if "*" in action]
                    if wildcard_actions:
                        # Replace wildcard actions with more specific ones
                        new_actions = []
                        for action in actions:
                            if "*" in action:
                                # Extract service name
                                service = action.split(":")[0] if ":" in action else ""
                                if service:
                                    # Replace with read-only actions for that service
                                    new_actions.extend([
                                        f"{service}:Get*", 
                                        f"{service}:List*", 
                                        f"{service}:Describe*"
                                    ])
                            else:
                                new_actions.append(action)
                        
                        statements[i]["Action"] = new_actions
                        modified = True
            
            if modified:
                # Create a new policy version
                await self.client.create_policy_version(
                    PolicyArn=policy_arn,
                    PolicyDocument=json.dumps(policy_doc),
                    SetAsDefault=True
                )
                
                # Delete the oldest policy version if we have 5 versions (AWS limit)
                policy_versions = await self.client.list_policy_versions(PolicyArn=policy_arn)
                if len(policy_versions["Versions"]) >= 5:
                    # Find the oldest non-default version
                    versions_to_delete = [v for v in policy_versions["Versions"] 
                                         if not v["IsDefaultVersion"]]
                    versions_to_delete.sort(key=lambda x: x["CreateDate"])
                    
                    if versions_to_delete:
                        oldest_version = versions_to_delete[0]
                        await self.client.delete_policy_version(
                            PolicyArn=policy_arn,
                            VersionId=oldest_version["VersionId"]
                        )
                
                logger.info(f"Successfully removed wildcard actions from policy {policy_arn}")
                return True, f"Successfully removed wildcard actions from policy {policy_arn}"
            else:
                return False, f"No wildcard actions found in policy {policy_arn}"
                
        except Exception as e:
            error_msg = f"Failed to remediate wildcard actions for policy {policy_arn}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_access_key_rotation(self, details):
        """
        Remediate IAM users' access keys that haven't been rotated in 90 days or more.
        Note: This will deactivate the old key but not delete it to prevent immediate disruption.
        """
        try:
            user_name = details.get("user_name", "")
            access_key_id = details.get("access_key_id", "")
            
            if not user_name or not access_key_id:
                return False, "Missing user name or access key ID"
                
            # Deactivate the old key
            await self.client.update_access_key(
                UserName=user_name,
                AccessKeyId=access_key_id,
                Status='Inactive'
            )
            
            logger.info(f"Successfully deactivated old access key {access_key_id} for user {user_name}")
            return True, f"Successfully deactivated old access key {access_key_id} for user {user_name}. Please create a new key and update applications before deleting the old key."
                
        except Exception as e:
            error_msg = f"Failed to deactivate old access key: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_no_root_user_access_key(self, details):
        """
        Remediate by removing IAM root user access keys.
        """
        try:
            # List access keys for the root user
            account_summary = await self.client.get_account_summary()
            root_access_key_count = account_summary.get("SummaryMap", {}).get("AccountAccessKeysPresent", 0)
            
            if root_access_key_count > 0:
                # We can't directly delete root access keys via the API
                # Instead, provide instructions
                message = "Root user access keys detected. For security reasons, these must be manually deleted through the AWS Console. Please log in as the root user and delete all access keys from Security Credentials."
                logger.warning(message)
                return False, message
            else:
                return False, "No root user access keys found."
                
        except Exception as e:
            error_msg = f"Failed to check root user access keys: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_mfa_enabled_for_console_users(self, details):
        """
        Remediate by enforcing MFA for IAM users with console access.
        Note: We can't directly enable MFA, but we can create a policy that requires MFA.
        """
        try:
            user_name = details.get("user_name", "")
            if not user_name:
                return False, "No user name provided"
                
            # Create an inline policy that requires MFA
            mfa_policy = {
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Sid": "AllowViewAccountInfo",
                        "Effect": "Allow",
                        "Action": [
                            "iam:GetAccountPasswordPolicy",
                            "iam:GetAccountSummary",
                            "iam:ListVirtualMFADevices"
                        ],
                        "Resource": "*"
                    },
                    {
                        "Sid": "AllowManageOwnVirtualMFADevice",
                        "Effect": "Allow",
                        "Action": [
                            "iam:CreateVirtualMFADevice",
                            "iam:DeleteVirtualMFADevice"
                        ],
                        "Resource": f"arn:aws:iam::*:mfa/{user_name}"
                    },
                    {
                        "Sid": "AllowManageOwnUserMFA",
                        "Effect": "Allow",
                        "Action": [
                            "iam:DeactivateMFADevice",
                            "iam:EnableMFADevice",
                            "iam:ListMFADevices",
                            "iam:ResyncMFADevice"
                        ],
                        "Resource": f"arn:aws:iam::*:user/{user_name}"
                    },
                    {
                        "Sid": "DenyAllExceptListedIfNoMFA",
                        "Effect": "Deny",
                        "NotAction": [
                            "iam:CreateVirtualMFADevice",
                            "iam:EnableMFADevice",
                            "iam:GetUser",
                            "iam:ListMFADevices",
                            "iam:ListVirtualMFADevices",
                            "iam:ResyncMFADevice",
                            "sts:GetSessionToken"
                        ],
                        "Resource": "*",
                        "Condition": {
                            "BoolIfExists": {
                                "aws:MultiFactorAuthPresent": "false"
                            }
                        }
                    }
                ]
            }
            
            # Apply the policy to the user
            await self.client.put_user_policy(
                UserName=user_name,
                PolicyName="RequireMFA",
                PolicyDocument=json.dumps(mfa_policy)
            )
            
            logger.info(f"Successfully applied MFA enforcement policy to user {user_name}")
            return True, f"Successfully applied MFA enforcement policy to user {user_name}. The user will be required to set up MFA before accessing most AWS services."
                
        except Exception as e:
            error_msg = f"Failed to apply MFA enforcement policy to user {user_name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_hardware_mfa_for_root_user(self, details):
        """
        Remediate by providing instructions for enabling hardware MFA for the root user.
        Note: Hardware MFA for root user must be set up manually through the AWS Console.
        """
        try:
            message = "Hardware MFA for the root user must be set up manually. Please log in to the AWS Console as the root user, go to Security Credentials, and add a hardware MFA device."
            logger.info(message)
            return False, message

        except Exception as e:
            error_msg = f"Failed to provide instructions for hardware MFA: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_strong_password_policies(self, details):
        """
        Remediate by enforcing strong password policies for IAM users.
        """
        try:
            # Set a strong password policy
            await self.client.update_account_password_policy(
                MinimumPasswordLength=12,
                RequireSymbols=True,
                RequireNumbers=True,
                RequireUppercaseCharacters=True,
                RequireLowercaseCharacters=True,
                AllowUsersToChangePassword=True,
                MaxPasswordAge=90,  # Require password rotation every 90 days
                PasswordReusePrevention=24  # Prevent reuse of the previous 24 passwords
            )
            
            logger.info("Successfully enforced strong password policy")
            return True, "Successfully enforced strong password policy for all IAM users"
                
        except Exception as e:
            error_msg = f"Failed to enforce strong password policy: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_remove_unused_user_credentials(self, details):
        """
        Remediate by removing unused IAM user credentials (access keys or passwords).
        """
        try:
            user_name = details.get("user_name", "")
            access_key_id = details.get("access_key_id", "")
            
            if not user_name:
                return False, "No user name provided"
                
            # Check if we're dealing with an access key or password
            if access_key_id:
                # Delete the unused access key
                await self.client.delete_access_key(
                    UserName=user_name,
                    AccessKeyId=access_key_id
                )
                
                logger.info(f"Successfully deleted unused access key {access_key_id} for user {user_name}")
                return True, f"Successfully deleted unused access key {access_key_id} for user {user_name}"
            else:
                # Delete the unused login profile (password)
                await self.client.delete_login_profile(
                    UserName=user_name
                )
                
                logger.info(f"Successfully deleted unused password for user {user_name}")
                return True, f"Successfully deleted unused password for user {user_name}"
                
        except Exception as e:
            error_msg = f"Failed to remove unused credential: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def get_remediation_functions(self):
        """
        Return a mapping of policy checks to remediation functions.
        """
        return {
            "no_full_admin_privileges": self.remediate_no_full_admin_privileges,
            "no_iam_policies_for_users": self.remediate_no_iam_policies_for_users,
            "no_wild_card_actions_in_custom_policies": self.remediate_no_wild_card_actions_in_custom_policies,
            "access_key_rotation": self.remediate_access_key_rotation,
            "no_root_user_access_key": self.remediate_no_root_user_access_key,
            "mfa_enabled_for_console_users": self.remediate_mfa_enabled_for_console_users,
            "hardware_mfa_for_root_user": self.remediate_hardware_mfa_for_root_user,
            "strong_password_policies": self.remediate_strong_password_policies,
            "remove_unused_user_credentials": self.remediate_remove_unused_user_credentials
        }

    async def remediate(self, policy_check, details):
        """
        Run the appropriate remediation function based on the policy check.
        Updates the details object with remediation results and returns it.
        """
        logger.info(f"Starting remediation for policy check: {policy_check}")
        logger.info(f"Details: {details}")

        # Create a copy of the details to update
        updated_details = details.copy()

        # Initialize AWS client
        session = self.get_session()
        async with session.client(AWSServiceNameEnum.IAM.value) as client:
            self.client = client

            # Get the appropriate remediation function
            remediation_functions = self.get_remediation_functions()
            if policy_check not in remediation_functions:
                error_msg = f"No remediation function found for policy check: {policy_check}"
                logger.warning(error_msg)
                
                # Update remediation results
                if policy_check in self.remediation_results:
                    self.remediation_results[policy_check]["status"] = False
                    self.remediation_results[policy_check]["message"] = error_msg
                
                # Add remediation failure information
                updated_details["remediate"] = {
                    "status": "fail",
                    "message": f"Error: {error_msg}"
                }
                
                return False, error_msg, updated_details

            remediation_func = remediation_functions[policy_check]

            # Call the remediation function with the appropriate parameters
            try:
                # All remediation functions now take details as their parameter
                success, message = await remediation_func(details)
                
            except Exception as e:
                error_msg = f"Failed to remediate {policy_check}: {str(e)}"
                logger.error(error_msg)
                
                # Update remediation results
                if policy_check in self.remediation_results:
                    self.remediation_results[policy_check]["status"] = False
                    self.remediation_results[policy_check]["message"] = error_msg
                
                # Add remediation failure information
                updated_details["remediate"] = {
                    "status": "fail",
                    "message": f"Error: {error_msg}"
                }
                
                return False, error_msg, updated_details

            # Update remediation results
            if policy_check in self.remediation_results:
                self.remediation_results[policy_check]["status"] = success
                self.remediation_results[policy_check]["message"] = message

            # Add remediation information
            updated_details["remediate"] = {
                "status": ResourceComplianceStatusEnum.PASS.value if success else ResourceComplianceStatusEnum.FAIL.value,
                "message": message
            }
            
            # Update compliance and field values if remediation was successful
            if success:
                updated_details["compliance"] = True
                
                # Apply field updates from remediation_results if available
                if policy_check in self.remediation_results and "field_updates" in self.remediation_results[policy_check]:
                    for field, value in self.remediation_results[policy_check]["field_updates"].items():
                        updated_details[field] = value

            return success, message, updated_details
