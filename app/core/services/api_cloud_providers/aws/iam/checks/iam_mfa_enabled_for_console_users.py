from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, IAMChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.iam.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "mfa_enabled_for_console_users": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.MFA_ENABLED_FOR_CONSOLE_USERS.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        # Since IAM is global, we only need to check the first region
        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            users = (cached.get("users") or {}).get("Users", [])
            user_details = cached.get("user_details", {})

            for user in users:
                user_name = user.get("UserName")
                user_arn = user.get("Arn")

                # Get user details for console password and MFA
                user_detail = user_details.get(user_name, {})
                login_profile = user_detail.get("login_profile")
                mfa_devices = user_detail.get("mfa_devices", {}).get("MFADevices", [])

                # If user details are not available, assume no console password (conservative approach)
                if not user_detail:
                    has_console_password = False
                    has_mfa_enabled = False
                else:
                    has_console_password = login_profile is not None
                    has_mfa_enabled = len(mfa_devices) > 0
                
                # Compliance: either no console password OR has MFA enabled
                compliance = not has_console_password or has_mfa_enabled
                
                # Update overall status if we found a non-compliant user
                if (findings["mfa_enabled_for_console_users"]["status"] == ResourceComplianceStatusEnum.PASS.value 
                    and has_console_password and not has_mfa_enabled):
                    findings["mfa_enabled_for_console_users"]["status"] = ResourceComplianceStatusEnum.FAIL.value
                
                # Add user details
                findings["mfa_enabled_for_console_users"]["details"].append({
                    "user_name": user_name,
                    # "user_arn": user_arn,
                    # "region": region,
                    # "has_console_password": has_console_password,
                    # "has_mfa_enabled": has_mfa_enabled,
                    "compliance": compliance,
                })
            
            # Since IAM is global, we only need to process the first region
            break

        return findings

    def remediate(self):
        pass