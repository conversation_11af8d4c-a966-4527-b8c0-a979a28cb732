from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, IAMChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.iam.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "strong_password_policies": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.STRONG_PASSWORD_POLICIES.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        # Since IAM is global, we only need to check the first region
        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            password_policy = cached.get("password_policy")
            
            # Default values if no password policy exists
            has_password_policy = password_policy is not None
            
            if has_password_policy:
                policy_data = password_policy.get("PasswordPolicy", {})
                
                # Check password policy requirements
                min_password_length = policy_data.get("MinimumPasswordLength", 0)
                require_symbols = policy_data.get("RequireSymbols", False)
                require_numbers = policy_data.get("RequireNumbers", False)
                require_uppercase = policy_data.get("RequireUppercaseCharacters", False)
                require_lowercase = policy_data.get("RequireLowercaseCharacters", False)
                max_password_age = policy_data.get("MaxPasswordAge", 0)
                password_reuse_prevention = policy_data.get("PasswordReusePrevention", 0)
                
                # Define strong password policy criteria
                is_strong_policy = (
                    min_password_length >= 8 and
                    require_symbols and
                    require_numbers and
                    require_uppercase and
                    require_lowercase and
                    max_password_age > 0 and
                    password_reuse_prevention >= 3
                )
            else:
                # No password policy exists
                min_password_length = 0
                require_symbols = False
                require_numbers = False
                require_uppercase = False
                require_lowercase = False
                max_password_age = 0
                password_reuse_prevention = 0
                is_strong_policy = False
            
            # Determine compliance - compliant if strong password policy exists
            compliance = has_password_policy and is_strong_policy
            
            # Update overall status if we found weak or no password policy
            if (findings["strong_password_policies"]["status"] == ResourceComplianceStatusEnum.PASS.value 
                and not compliance):
                findings["strong_password_policies"]["status"] = ResourceComplianceStatusEnum.FAIL.value
            
            # Add password policy details
            findings["strong_password_policies"]["details"].append({
                # "region": region,
                # "user_name": user_name,
                # "has_password_policy": has_password_policy,
                "is_strong_policy": is_strong_policy,
                # "min_password_length": min_password_length,
                # "require_symbols": require_symbols,
                # "require_numbers": require_numbers,
                # "require_uppercase": require_uppercase,
                # "require_lowercase": require_lowercase,
                # "max_password_age": max_password_age,
                # "password_reuse_prevention": password_reuse_prevention,
                "compliance": compliance,
            })
            
            # Since IAM is global, we only need to process the first region
            break

        return findings

    def remediate(self):
        pass