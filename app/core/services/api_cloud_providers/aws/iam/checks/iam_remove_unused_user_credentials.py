from typing import Dict, Any, List
from datetime import datetime, timezone, timedelta
from app.common import SeverityEnum, ResourceComplianceStatusEnum, IAMChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.iam.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "remove_unused_user_credentials": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.REMOVE_UNUSED_USER_CREDENTIALS.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        # Since IAM is global, we only need to check the first region
        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            users = (cached.get("users") or {}).get("Users", [])

            # Define threshold for unused credentials (e.g., 90 days)
            unused_threshold_days = 90
            threshold_date = datetime.now(timezone.utc) - timedelta(days=unused_threshold_days)

            for user in users:
                user_name = user.get("UserName")
                user_arn = user.get("Arn")
                
                # For this check, we need to determine:
                # 1. Last password used date
                # 2. Last access key used date
                # 3. Last console login date
                # This information would require additional API calls or credential report
                # Since this data is not in the current cached data, we'll use placeholder logic
                
                # Placeholder logic - in real implementation, we'd check:
                # - PasswordLastUsed from user data
                # - AccessKeyLastUsed from get_access_key_last_used
                # - Last console login from credential report
                
                password_last_used = user.get("PasswordLastUsed")
                has_unused_credentials = False
                days_since_last_use = 0
                
                if password_last_used:
                    # Parse the date if it exists
                    try:
                        if isinstance(password_last_used, str):
                            last_used_date = datetime.fromisoformat(password_last_used.replace('Z', '+00:00'))
                        else:
                            last_used_date = password_last_used
                        
                        days_since_last_use = (datetime.now(timezone.utc) - last_used_date).days
                        has_unused_credentials = days_since_last_use > unused_threshold_days
                    except (ValueError, TypeError):
                        # If we can't parse the date, assume it's unused
                        has_unused_credentials = True
                        days_since_last_use = unused_threshold_days + 1
                else:
                    # No password last used date means never used or no password
                    has_unused_credentials = False  # We'll be conservative here
                
                # Determine compliance - compliant if credentials are being used
                compliance = not has_unused_credentials
                
                # Update overall status if we found unused credentials
                if (findings["remove_unused_user_credentials"]["status"] == ResourceComplianceStatusEnum.PASS.value 
                    and has_unused_credentials):
                    findings["remove_unused_user_credentials"]["status"] = ResourceComplianceStatusEnum.FAIL.value
                
                # Add user details
                findings["remove_unused_user_credentials"]["details"].append({
                    "user_name": user_name,
                    # "user_arn": user_arn,
                    # "region": region,
                    # "has_unused_credentials": has_unused_credentials,
                    # "days_since_last_use": days_since_last_use,
                    # "unused_threshold_days": unused_threshold_days,
                    "compliance": compliance,
                })
            
            # Since IAM is global, we only need to process the first region
            break

        return findings

    def remediate(self):
        pass