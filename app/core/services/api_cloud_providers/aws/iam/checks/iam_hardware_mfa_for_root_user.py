from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, IAMChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.iam.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "hardware_mfa_for_root_user": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.HARDWARE_MFA_FOR_ROOT_USER.value,
                "severity": SeverityEnum.CRITICAL.value,
            }
        }

        # Since IAM is global, we only need to check the first region
        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            account_summary = cached.get("account_summary", {})
            
            # Check if root user has hardware MFA enabled
            # This information would typically come from account summary
            summary_map = account_summary.get("SummaryMap", {}) if account_summary else {}
            
            # Look for MFA devices in account summary
            account_mfa_enabled = summary_map.get("AccountMFAEnabled", 0)
            
            # For hardware MFA check, we need to determine if it's specifically hardware MFA
            # This would require additional API calls to get virtual vs hardware MFA details
            # For now, we'll check if any MFA is enabled for root user
            has_hardware_mfa = account_mfa_enabled > 0
            
            # Determine compliance - compliant if hardware MFA is enabled
            compliance = has_hardware_mfa
            
            # Update overall status if we found no hardware MFA
            if (findings["hardware_mfa_for_root_user"]["status"] == ResourceComplianceStatusEnum.PASS.value 
                and not has_hardware_mfa):
                findings["hardware_mfa_for_root_user"]["status"] = ResourceComplianceStatusEnum.FAIL.value
            
            # Add account details
            findings["hardware_mfa_for_root_user"]["details"].append({
                # "region": region,
                "has_hardware_mfa": has_hardware_mfa,
                # "account_mfa_enabled": account_mfa_enabled,
                "compliance": compliance,
            })
            
            # Since IAM is global, we only need to process the first region
            break

        return findings

    def remediate(self):
        pass