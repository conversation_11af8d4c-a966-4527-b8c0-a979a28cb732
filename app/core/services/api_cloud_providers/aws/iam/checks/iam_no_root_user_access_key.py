from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, IAMChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.iam.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "no_root_user_access_key": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.NO_ROOT_USER_ACCESS_KEY.value,
                "severity": SeverityEnum.CRITICAL.value,
            }
        }

        # Since IAM is global, we only need to check the first region
        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            account_summary = cached.get("account_summary", {})
            
            # Check if root user has access keys
            # This information would typically come from account summary or credential report
            # For now, we'll use a placeholder approach
            
            # In the account summary, look for AccountAccessKeysPresent
            summary_map = account_summary.get("SummaryMap", {}) if account_summary else {}
            account_access_keys_present = summary_map.get("AccountAccessKeysPresent", 0)
            
            # If AccountAccessKeysPresent > 0, it means root user has access keys
            has_root_access_key = account_access_keys_present > 0
            
            # Determine compliance - compliant if no root access keys
            compliance = not has_root_access_key
            
            # Update overall status if we found root access keys
            if (findings["no_root_user_access_key"]["status"] == ResourceComplianceStatusEnum.PASS.value 
                and has_root_access_key):
                findings["no_root_user_access_key"]["status"] = ResourceComplianceStatusEnum.FAIL.value
            
            # Add account details
            findings["no_root_user_access_key"]["details"].append({
                # "region": region,
                "has_root_access_key": has_root_access_key,
                # "account_access_keys_present": account_access_keys_present,
                "compliance": compliance,
            })
            
            # Since IAM is global, we only need to process the first region
            break

        return findings

    def remediate(self):
        pass