from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, IAMChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.iam.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "no_iam_policies_for_users": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.NO_IAM_POLICIES_FOR_USERS.value,
                "severity": SeverityEnum.LOW.value,
            }
        }

        # Since IAM is global, we only need to check the first region
        # But we'll iterate through all regions for consistency with the pattern
        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            users = (cached.get("users") or {}).get("Users", [])
            user_details = cached.get("user_details", {})

            for user in users:
                user_name = user.get("UserName")
                user_arn = user.get("Arn")

                # Get attached policies from user details
                user_detail = user_details.get(user_name, {})
                attached_policies = user_detail.get("attached_policies", {}).get("AttachedPolicies", [])
                has_attached_policies = len(attached_policies) > 0

                # If user details are not available, assume compliant (conservative approach)
                if not user_detail:
                    has_attached_policies = False

                compliance = not has_attached_policies
                
                # Update overall status if we found a non-compliant user
                if (findings["no_iam_policies_for_users"]["status"] == ResourceComplianceStatusEnum.PASS.value 
                    and has_attached_policies):
                    findings["no_iam_policies_for_users"]["status"] = ResourceComplianceStatusEnum.FAIL.value
                
                # Add user details
                findings["no_iam_policies_for_users"]["details"].append({
                    "user_name": user_name,
                    # "user_arn": user_arn,
                    # "region": region,
                    # "has_attached_policies": has_attached_policies,
                    "compliance": compliance,
                })
            
            # Since IAM is global, we only need to process the first region
            break

        return findings

    def remediate(self):
        pass
