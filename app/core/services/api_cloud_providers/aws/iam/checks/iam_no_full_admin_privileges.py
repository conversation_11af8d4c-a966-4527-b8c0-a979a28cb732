from typing import Dict, Any, List, Set
from app.common import SeverityEnum, ResourceComplianceStatusEnum, IAMChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.iam.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "no_full_admin_privileges": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.NO_FULL_ADMIN_PRIVILEGES.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        # Track processed policies to avoid duplicates
        processed_policies: Set[str] = set()
        
        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            policies = (cached.get("policies") or {}).get("Policies", [])

            for policy in policies:
                policy_arn = policy.get("Arn")
                policy_name = policy.get("PolicyName")
                
                # Skip if we've already processed this policy
                if policy_arn in processed_policies:
                    continue
                
                # Mark this policy as processed
                processed_policies.add(policy_arn)
                
                # Get the default version document
                default_version_id = policy.get("DefaultVersionId")
                policy_versions = cached.get("policy_versions", {})
                policy_version_key = f"{policy_arn}#{default_version_id}"
                
                has_full_admin_privileges = False
                
                if policy_version_key in policy_versions:
                    policy_document = policy_versions[policy_version_key].get("PolicyVersion", {}).get("Document", {})
                    statements = policy_document.get("Statement", [])
                    
                    # Ensure statements is a list
                    if not isinstance(statements, list):
                        statements = [statements]
                    
                    # Check each statement for full administrative privileges
                    for statement in statements:
                        actions = statement.get("Action", [])
                        resources = statement.get("Resource", [])
                        effect = statement.get("Effect", "")
                        
                        # Ensure actions and resources are lists
                        if not isinstance(actions, list):
                            actions = [actions]
                        if not isinstance(resources, list):
                            resources = [resources]
                        
                        # Check for full admin privileges: Allow effect with * action and * resource
                        if effect == "Allow" and "*" in actions and "*" in resources:
                            has_full_admin_privileges = True
                            break
                
                # Determine compliance - compliant if no full admin privileges
                compliance = not has_full_admin_privileges
                
                # Update overall status if we found a non-compliant policy
                if (findings["no_full_admin_privileges"]["status"] == ResourceComplianceStatusEnum.PASS.value 
                    and has_full_admin_privileges):
                    findings["no_full_admin_privileges"]["status"] = ResourceComplianceStatusEnum.FAIL.value
                
                # Add policy details
                findings["no_full_admin_privileges"]["details"].append({
                    "policy_name": policy_name,
                    "policy_arn": policy_arn,
                    # "region": region,
                    "has_full_admin_privileges": has_full_admin_privileges,
                    "compliance": compliance,
                })

        return findings

    def remediate(self):
        pass