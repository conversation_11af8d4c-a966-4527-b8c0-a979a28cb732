from typing import Dict, Any, List
from datetime import datetime, timezone
from app.common import SeverityEnum, ResourceComplianceStatusEnum, IAMChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.iam.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "access_key_rotation": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.ACCESS_KEY_ROTATION.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        # Since IAM is global, we only need to check the first region
        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            users = (cached.get("users") or {}).get("Users", [])
            user_details = cached.get("user_details", {})

            for user in users:
                user_name = user.get("UserName")
                user_arn = user.get("Arn")

                # Get access key information from user details
                user_detail = user_details.get(user_name, {})
                access_keys = user_detail.get("access_keys", {}).get("AccessKeyMetadata", [])

                access_keys_need_rotation = False
                max_days_since_creation = 0

                # If user details are not available, assume compliant (conservative approach)
                if not user_detail:
                    access_keys = []

                # Check each access key for rotation needs (90 days threshold)
                for access_key in access_keys:
                    create_date = access_key.get("CreateDate")
                    if create_date:
                        if isinstance(create_date, str):
                            create_date = datetime.fromisoformat(create_date.replace('Z', '+00:00'))
                        days_since_creation = (datetime.now(timezone.utc) - create_date).days
                        max_days_since_creation = max(max_days_since_creation, days_since_creation)

                        if days_since_creation > 90:  # 90 days threshold
                            access_keys_need_rotation = True

                compliance = not access_keys_need_rotation

                # Update overall status if we found a non-compliant user
                if (findings["access_key_rotation"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and access_keys_need_rotation):
                    findings["access_key_rotation"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                # Add user details
                findings["access_key_rotation"]["details"].append({
                    "user_name": user_name,
                    # "user_arn": user_arn,
                    # "region": region,
                    # "access_keys_need_rotation": access_keys_need_rotation,
                    # "max_days_since_creation": max_days_since_creation,
                    "compliance": compliance,
                })
            
            # Since IAM is global, we only need to process the first region
            break

        return findings

    def remediate(self):
        pass