from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, IAMChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.iam.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "no_wild_card_actions_in_custom_policies": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.NO_WILDCARD_ACTIONS_IN_CUSTOM_POLICIES.value,
                "severity": SeverityEnum.LOW.value,
            }
        }

        # Since IAM is global, we only need to check the first region
        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            policies = (cached.get("policies") or {}).get("Policies", [])

            for policy in policies:
                policy_arn = policy.get("Arn")
                policy_name = policy.get("PolicyName")
                
                # Get the default version document
                default_version_id = policy.get("DefaultVersionId")
                policy_versions = cached.get("policy_versions", {})
                policy_version_key = f"{policy_arn}#{default_version_id}"
                
                has_wildcard_actions = False
                
                if policy_version_key in policy_versions:
                    policy_document = policy_versions[policy_version_key].get("PolicyVersion", {}).get("Document", {})
                    statements = policy_document.get("Statement", [])
                    
                    # Ensure statements is a list
                    if not isinstance(statements, list):
                        statements = [statements]
                    
                    # Check each statement for wildcard actions
                    for statement in statements:
                        actions = statement.get("Action", [])
                        effect = statement.get("Effect", "")
                        
                        # Ensure actions is a list
                        if not isinstance(actions, list):
                            actions = [actions]
                        
                        # Check for wildcard actions in Allow statements
                        if effect == "Allow":
                            for action in actions:
                                if "*" in str(action):
                                    has_wildcard_actions = True
                                    break
                        
                        if has_wildcard_actions:
                            break
                
                # Determine compliance - compliant if no wildcard actions
                compliance = not has_wildcard_actions
                
                # Update overall status if we found a non-compliant policy
                if (findings["no_wild_card_actions_in_custom_policies"]["status"] == ResourceComplianceStatusEnum.PASS.value 
                    and has_wildcard_actions):
                    findings["no_wild_card_actions_in_custom_policies"]["status"] = ResourceComplianceStatusEnum.FAIL.value
                
                # Add policy details
                findings["no_wild_card_actions_in_custom_policies"]["details"].append({
                    "policy_name": policy_name,
                    "policy_arn": policy_arn,
                    # "region": region,
                    "has_wildcard_actions": has_wildcard_actions,
                    "compliance": compliance,
                })
            
            # Since IAM is global, we only need to process the first region
            break

        return findings

    def remediate(self):
        pass