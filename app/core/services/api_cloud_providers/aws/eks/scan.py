import asyncio
from celery.utils.log import get_task_logger
from ..config import BaseChecksProcessor
from app.common import (AWSServiceNameEnum, SeverityEnum, ResourceComplianceStatusEnum,
                        EKSChecksDescriptionEnum)

__all__ = ['ChecksProcessor']

logger = get_task_logger(__name__)


class ChecksProcessor(BaseChecksProcessor):
    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.clusters = None
        self.replication_groups = None
        self.findings = {
            "private_cluster_endpoint": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EKSChecksDescriptionEnum.PRIVATE_CLUSTER_ENDPOINT.value,
                "severity": SeverityEnum.HIGH.value
            },
            "supported_kubernetes_version": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EKSChecksDescriptionEnum.SUPPORTED_KUBERNETES_VERSION.value,
                "severity": SeverityEnum.HIGH.value
            },
            "encrypted_kubernetes_secrets": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EKSChecksDescriptionEnum.ENCRYPTED_KUBERNETES_SECRETS.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "audit_logging_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EKSChecksDescriptionEnum.AUDIT_LOGGING_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "private_node_groups": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EKSChecksDescriptionEnum.PRIVATE_NODE_GROUPS.value,
                "severity": SeverityEnum.HIGH.value
            },
            "network_policy_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EKSChecksDescriptionEnum.NETWORK_POLICY_ENABLED.value,
                "severity": SeverityEnum.HIGH.value
            }
        }

    async def check_private_cluster_endpoint(self):
        """
        EKS cluster endpoints should not be publicly accessible.
        """
        all_compliant = True  # Track overall compliance status

        for cluster_arn in self.clusters.get("clusters", []):
            cluster_name = cluster_arn.split("/")[-1]
            cluster_details = await self.client.describe_cluster(name=cluster_name)
            endpoint_public_access = cluster_details.get("cluster", {}).get("resourcesVpcConfig", {}).get(
                "endpointPublicAccess", True)

            private_endpoint = not endpoint_public_access

            resource_status = ResourceComplianceStatusEnum.PASS.value if private_endpoint else ResourceComplianceStatusEnum.FAIL.value

            self.findings["private_cluster_endpoint"]["details"].append({
                "cluster_name": cluster_name,
                "region": self.client.meta.region_name,
                "endpoint_public_access": endpoint_public_access,
                "private_endpoint": private_endpoint,
                "status": resource_status,
                "compliance": private_endpoint
            })

            if not private_endpoint:
                all_compliant = False

        # Set the main status for the check
        if self.findings["private_cluster_endpoint"]["status"] and not all_compliant:
            self.findings["private_cluster_endpoint"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_supported_kubernetes_version(self):
        """
        EKS clusters should run on a supported Kubernetes version.
        """
        all_compliant = True  # Track overall compliance status
        #TODO: Fetch supported versions from AWS API or configuration
        supported_versions = ["1.30", "1.31", "1.32"]  # Define supported Kubernetes versions

        for cluster_arn in self.clusters.get("clusters", []):
            cluster_name = cluster_arn.split("/")[-1]
            cluster_details = await self.client.describe_cluster(name=cluster_name)
            kubernetes_version = cluster_details.get("cluster", {}).get("version", "")

            is_supported_version = kubernetes_version in supported_versions

            resource_status = ResourceComplianceStatusEnum.PASS.value if is_supported_version else ResourceComplianceStatusEnum.FAIL.value

            self.findings["supported_kubernetes_version"]["details"].append({
                "cluster_name": cluster_name,
                "region": self.client.meta.region_name,
                "kubernetes_version": kubernetes_version,
                "is_supported_version": is_supported_version,
                "status": resource_status,
                "compliance": is_supported_version
            })

            if not is_supported_version:
                all_compliant = False

        # Set the main status for the check
        if self.findings["supported_kubernetes_version"]["status"] and not all_compliant:
            self.findings["supported_kubernetes_version"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_encrypted_kubernetes_secrets(self):
        """
        EKS clusters should use encrypted Kubernetes secrets.
        """
        all_compliant = True  # Track overall compliance status

        for cluster_arn in self.clusters.get("clusters", []):
            cluster_name = cluster_arn.split("/")[-1]
            cluster_details = await self.client.describe_cluster(name=cluster_name)
            encryption_config = cluster_details.get("cluster", {}).get("encryptionConfig", [])

            has_secrets_encryption = any(
                "secrets" in provider.get("resources", [])
                for config in encryption_config
                for provider in config.get("provider", {}).values()
            )

            resource_status = ResourceComplianceStatusEnum.PASS.value if has_secrets_encryption else ResourceComplianceStatusEnum.FAIL.value

            self.findings["encrypted_kubernetes_secrets"]["details"].append({
                "cluster_name": cluster_name,
                "region": self.client.meta.region_name,
                "encryption_config": encryption_config,
                "has_secrets_encryption": has_secrets_encryption,
                "status": resource_status,
                "compliance": has_secrets_encryption
            })

            if not has_secrets_encryption:
                all_compliant = False

        # Set the main status for the check
        if self.findings["encrypted_kubernetes_secrets"]["status"] and not all_compliant:
            self.findings["encrypted_kubernetes_secrets"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_audit_logging_enabled(self):
        """
        EKS clusters should have audit logging enabled.
        """
        all_compliant = True  # Track overall compliance status

        for cluster_arn in self.clusters.get("clusters", []):
            cluster_name = cluster_arn.split("/")[-1]
            cluster_details = await self.client.describe_cluster(name=cluster_name)
            logging_config = cluster_details.get("cluster", {}).get("logging", {}).get("clusterLogging", [])

            audit_logging_enabled = any(
                log.get("types", []) == ["audit"] and log.get("enabled", False)
                for log in logging_config
            )

            resource_status = ResourceComplianceStatusEnum.PASS.value if audit_logging_enabled else ResourceComplianceStatusEnum.FAIL.value

            self.findings["audit_logging_enabled"]["details"].append({
                "cluster_name": cluster_name,
                "region": self.client.meta.region_name,
                "audit_logging_enabled": audit_logging_enabled,
                "status": resource_status,
                "compliance": audit_logging_enabled
            })

            if not audit_logging_enabled:
                all_compliant = False

            logger.info(f"all_compliant: {all_compliant}")

        logger.info(f"all_compliant-out: {all_compliant}")
        # Set the main status for the check
        if self.findings["audit_logging_enabled"]["status"] and not all_compliant:
            self.findings["audit_logging_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_private_node_groups(self):
        """
        EKS clusters should use private node groups without public IP addresses.
        Ensures node groups are deployed in private subnets and have no public IP addresses.
        """
        all_compliant = True  # Track overall compliance status

        for cluster_arn in self.clusters.get("clusters", []):
            cluster_name = cluster_arn.split("/")[-1]

            try:
                # Get all node groups for this cluster
                node_groups_response = await self.client.list_nodegroups(clusterName=cluster_name)
                node_groups = node_groups_response.get("nodegroups", [])

                if not node_groups:
                    # No node groups found - this might be a Fargate-only cluster
                    self.findings["private_node_groups"]["details"].append({
                        "cluster_name": cluster_name,
                        "region": self.client.meta.region_name,
                        "node_group_name": "N/A",
                        "has_public_ip": False,
                        "remote_access_configured": True,
                        "private_subnets_only": True,
                        "status": ResourceComplianceStatusEnum.PASS.value,
                        "compliance": True,
                        "note": "No node groups found - possibly Fargate-only cluster"
                    })
                    continue

                for node_group_name in node_groups:
                    try:
                        # Get detailed information about each node group
                        node_group_details = await self.client.describe_nodegroup(
                            clusterName=cluster_name,
                            nodegroupName=node_group_name
                        )

                        node_group = node_group_details.get("nodegroup", {})

                        # Check if node group has public IP addresses
                        remote_access = node_group.get("remoteAccess", {})
                        has_public_ip = remote_access.get("ec2SshKey") is not None and not remote_access.get("sourceSecurityGroups")

                        # Check subnet configuration (private vs public)
                        subnets = node_group.get("subnets", [])
                        private_subnets_only = True  # Assume private unless proven otherwise

                        # Check if remote access is properly configured
                        remote_access_configured = True
                        if remote_access.get("ec2SshKey"):
                            # If SSH key is configured, ensure source security groups are specified
                            source_security_groups = remote_access.get("sourceSecurityGroups", [])
                            remote_access_configured = len(source_security_groups) > 0

                        # Overall compliance check
                        is_compliant = not has_public_ip and remote_access_configured and private_subnets_only

                        resource_status = ResourceComplianceStatusEnum.PASS.value if is_compliant else ResourceComplianceStatusEnum.FAIL.value

                        self.findings["private_node_groups"]["details"].append({
                            "cluster_name": cluster_name,
                            "region": self.client.meta.region_name,
                            "node_group_name": node_group_name,
                            # "has_public_ip": has_public_ip,
                            "remote_access_configured": remote_access_configured,
                            "private_subnets_only": private_subnets_only,
                            "subnet_count": len(subnets),
                            "ami_type": node_group.get("amiType", "Unknown"),
                            "instance_types": node_group.get("instanceTypes", []),
                            # "status": resource_status,
                            "compliance": is_compliant
                        })

                        if not is_compliant:
                            all_compliant = False

                    except Exception as e:
                        logger.error(f"Error checking node group {node_group_name} in cluster {cluster_name}: {str(e)}")
                        self.findings["private_node_groups"]["details"].append({
                            "cluster_name": cluster_name,
                            "region": self.client.meta.region_name,
                            "node_group_name": node_group_name,
                            "has_public_ip": True,  # Assume non-compliant on error
                            "remote_access_configured": False,
                            "private_subnets_only": False,
                            "status": ResourceComplianceStatusEnum.FAIL.value,
                            "compliance": False,
                            "error": str(e)
                        })
                        all_compliant = False

            except Exception as e:
                logger.error(f"Error listing node groups for cluster {cluster_name}: {str(e)}")
                self.findings["private_node_groups"]["details"].append({
                    "cluster_name": cluster_name,
                    "region": self.client.meta.region_name,
                    "node_group_name": "Error",
                    "has_public_ip": True,  # Assume non-compliant on error
                    "remote_access_configured": False,
                    "private_subnets_only": False,
                    "status": ResourceComplianceStatusEnum.FAIL.value,
                    "compliance": False,
                    "error": str(e)
                })
                all_compliant = False

        # Set the main status for the check
        if self.findings["private_node_groups"]["status"] and not all_compliant:
            self.findings["private_node_groups"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_network_policy_enabled(self):
        """
        EKS clusters should have network policy enabled for pod-level network security.
        Checks if Calico or other CNI network policy add-ons are installed and configured.
        """
        all_compliant = True  # Track overall compliance status

        for cluster_arn in self.clusters.get("clusters", []):
            cluster_name = cluster_arn.split("/")[-1]

            try:
                # Get all add-ons for this cluster
                addons_response = await self.client.list_addons(clusterName=cluster_name)
                addons = addons_response.get("addons", [])

                # Check for network policy related add-ons
                network_policy_addons = []
                network_policy_enabled = False

                # Common network policy add-ons
                network_policy_addon_names = [
                    "vpc-cni",  # AWS VPC CNI can support network policies
                    "calico",   # Calico network policy
                    "cilium",   # Cilium network policy
                    "weave-net" # Weave Net network policy
                ]

                for addon_name in addons:
                    try:
                        # Get detailed information about each add-on
                        addon_details = await self.client.describe_addon(
                            clusterName=cluster_name,
                            addonName=addon_name
                        )

                        addon = addon_details.get("addon", {})
                        addon_status = addon.get("status", "")

                        # Check if this is a network policy related add-on
                        if any(np_addon in addon_name.lower() for np_addon in network_policy_addon_names):
                            network_policy_addons.append({
                                "name": addon_name,
                                "status": addon_status,
                                "version": addon.get("addonVersion", "Unknown")
                            })

                            # Consider network policy enabled if any relevant add-on is active
                            if addon_status.lower() == "active":
                                network_policy_enabled = True

                    except Exception as e:
                        logger.error(f"Error describing add-on {addon_name} in cluster {cluster_name}: {str(e)}")

                # If no network policy add-ons found, check for vpc-cni specifically
                if not network_policy_enabled and "vpc-cni" in addons:
                    try:
                        vpc_cni_details = await self.client.describe_addon(
                            clusterName=cluster_name,
                            addonName="vpc-cni"
                        )
                        vpc_cni = vpc_cni_details.get("addon", {})
                        if vpc_cni.get("status", "").lower() == "active":
                            # VPC CNI is active, but we need to check if network policies are actually configured
                            # This would require additional Kubernetes API calls which are beyond EKS API scope
                            # For now, we'll mark as partially compliant if VPC CNI is present
                            network_policy_enabled = True
                            network_policy_addons.append({
                                "name": "vpc-cni",
                                "status": vpc_cni.get("status", "Unknown"),
                                "version": vpc_cni.get("addonVersion", "Unknown"),
                                "note": "VPC CNI present but network policy configuration not verified"
                            })
                    except Exception as e:
                        logger.error(f"Error checking VPC CNI add-on in cluster {cluster_name}: {str(e)}")

                resource_status = ResourceComplianceStatusEnum.PASS.value if network_policy_enabled else ResourceComplianceStatusEnum.FAIL.value

                self.findings["network_policy_enabled"]["details"].append({
                    "cluster_name": cluster_name,
                    "region": self.client.meta.region_name,
                    "network_policy_enabled": network_policy_enabled,
                    # "network_policy_addons": network_policy_addons,
                    # "total_addons": len(addons),
                    "status": resource_status,
                    "compliance": network_policy_enabled
                })

                if not network_policy_enabled:
                    all_compliant = False

            except Exception as e:
                logger.error(f"Error checking network policy for cluster {cluster_name}: {str(e)}")
                self.findings["network_policy_enabled"]["details"].append({
                    "cluster_name": cluster_name,
                    "region": self.client.meta.region_name,
                    "network_policy_enabled": False,
                    # "network_policy_addons": [],
                    # "total_addons": 0,
                    "status": ResourceComplianceStatusEnum.FAIL.value,
                    "compliance": False,
                    "error": str(e)
                })
                all_compliant = False

        # Set the main status for the check
        if self.findings["network_policy_enabled"]["status"] and not all_compliant:
            self.findings["network_policy_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    def get_check_functions(self):
        return [
            self.check_private_cluster_endpoint,
            self.check_supported_kubernetes_version,
            self.check_encrypted_kubernetes_secrets,
            self.check_audit_logging_enabled,
            self.check_private_node_groups,
            self.check_network_policy_enabled
        ]

    async def run_checks(self):
        for region in self.regions:
            logger.info(f"Scanning region: {region}")
            session = self.get_session(region)
            if not await self.is_region_accessible():
                logger.warning(f"Region {region} is not accessible. Skipping checks.")
                continue
            async with session.client(AWSServiceNameEnum.EKS.value, region_name=region) as client:
                self.client = client

                self.clusters = await self.client.list_clusters()

                for check_function in self.get_check_functions():
                    await check_function()
            await asyncio.sleep(5)

        return self.findings
