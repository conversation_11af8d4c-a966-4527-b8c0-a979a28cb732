import asyncio
from celery.utils.log import get_task_logger
from ..config import BaseRemediationProcessor
from app.common import AWSServiceNameEnum

__all__ = ['RemediationProcessor']

logger = get_task_logger(__name__)


class RemediationProcessor(BaseRemediationProcessor):
    """
    Processor for remediating EKS compliance issues.
    Inherits from BaseRemediationProcessor to reuse AWS session management.
    """

    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.remediation_results = {
            "private_cluster_endpoint": {
                "status": False,
                "message": "",
                "field_updates": {"private_endpoint": True, "endpoint_public_access": False}
            },
            "supported_kubernetes_version": {
                "status": False,
                "message": "",
                "field_updates": {"is_supported_version": True}
            },
            "encrypted_kubernetes_secrets": {
                "status": False,
                "message": "",
                "field_updates": {"has_secrets_encryption": True}
            },
            "audit_logging_enabled": {
                "status": False,
                "message": "",
                "field_updates": {"audit_logging_enabled": True}
            }
        }

    async def remediate_private_cluster_endpoint(self, details):
        """
        Remediate EKS by disabling public endpoint access.
        """
        try:
            cluster_name = details.get("cluster_name", "")
            region = details.get("region", "")

            if not cluster_name or not region:
                return False, "Missing cluster name or region"

            # Disable public endpoint access
            await self.client.update_cluster_config(
                name=cluster_name,
                resourcesVpcConfig={
                    'endpointPublicAccess': False,
                    'endpointPrivateAccess': True
                }
            )

            logger.info(f"Successfully disabled public endpoint access for EKS cluster {cluster_name}")
            return True, f"Successfully disabled public endpoint access for EKS cluster {cluster_name}"

        except Exception as e:
            error_msg = f"Failed to disable public endpoint access: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_supported_kubernetes_version(self, details):
        """
        Remediate EKS by updating the cluster to a supported Kubernetes version.
        """
        try:
            cluster_name = details.get("cluster_name", "")
            region = details.get("region", "")

            if not cluster_name or not region:
                return False, "Missing cluster name or region"

            # Get available Kubernetes versions
            versions_response = await self.client.describe_addon_versions()
            available_versions = []

            for addon in versions_response.get('addons', []):
                for version_info in addon.get('addonVersions', []):
                    for k8s_version in version_info.get('compatibilities', []):
                        if 'clusterVersion' in k8s_version:
                            available_versions.append(k8s_version['clusterVersion'])

            # Remove duplicates and sort in descending order
            available_versions = sorted(list(set(available_versions)), reverse=True)

            if not available_versions:
                return False, "Could not determine supported Kubernetes versions"

            # Get the supported versions (assuming the top 3 versions are supported)
            supported_versions = available_versions[:3]

            # Get current version
            cluster_details = await self.client.describe_cluster(name=cluster_name)
            current_version = cluster_details.get("cluster", {}).get("version", "")

            if current_version in supported_versions:
                return True, f"EKS cluster {cluster_name} is already on a supported version ({current_version})"

            # Update to the latest supported version
            latest_supported_version = supported_versions[0]

            await self.client.update_cluster_version(
                name=cluster_name,
                version=latest_supported_version
            )

            logger.info(
                f"Successfully initiated update of EKS cluster {cluster_name} to version {latest_supported_version}")
            return True, f"Successfully initiated update of EKS cluster {cluster_name} to version {latest_supported_version}"

        except Exception as e:
            error_msg = f"Failed to update cluster version: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_encrypted_kubernetes_secrets(self, details):
        """
        Remediate EKS by enabling secrets encryption using KMS.
        """
        try:
            cluster_name = details.get("cluster_name", "")
            region = details.get("region", "")

            if not cluster_name or not region:
                return False, "Missing cluster name or region"

            # Create a KMS key for EKS secrets encryption if one doesn't exist
            session = self.get_session()
            async with session.client('kms', region_name=region) as kms_client:
                # Create a new KMS key for EKS
                key_response = await kms_client.create_key(
                    Description=f'KMS key for EKS cluster {cluster_name} secrets encryption',
                    KeyUsage='ENCRYPT_DECRYPT',
                    Origin='AWS_KMS',
                    Tags=[
                        {
                            'TagKey': 'Name',
                            'TagValue': f'eks-{cluster_name}-secrets-encryption'
                        },
                        {
                            'TagKey': 'Purpose',
                            'TagValue': 'EKS Secrets Encryption'
                        }
                    ]
                )

                key_id = key_response['KeyMetadata']['KeyId']

                # Create an alias for the key
                await kms_client.create_alias(
                    AliasName=f'alias/eks-{cluster_name}-secrets',
                    TargetKeyId=key_id
                )

            # Enable secrets encryption using the KMS key
            await self.client.associate_encryption_config(
                clusterName=cluster_name,
                encryptionConfig=[
                    {
                        'resources': ['secrets'],
                        'provider': {
                            'keyArn': f'arn:aws:kms:{region}:{self.credentials["aws_account_id"]}:key/{key_id}'
                        }
                    }
                ]
            )

            logger.info(f"Successfully enabled secrets encryption for EKS cluster {cluster_name}")
            return True, f"Successfully enabled secrets encryption for EKS cluster {cluster_name}"

        except Exception as e:
            error_msg = f"Failed to enable secrets encryption: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_audit_logging_enabled(self, details):
        """
        Remediate EKS by enabling control plane audit logging.
        """
        try:
            cluster_name = details.get("cluster_name", "")
            region = details.get("region", "")

            if not cluster_name or not region:
                return False, "Missing cluster name or region"

            # Enable audit logging
            await self.client.update_cluster_config(
                name=cluster_name,
                logging={
                    'clusterLogging': [
                        {
                            'types': ['audit'],
                            'enabled': True
                        }
                    ]
                }
            )

            logger.info(f"Successfully enabled audit logging for EKS cluster {cluster_name}")
            return True, f"Successfully enabled audit logging for EKS cluster {cluster_name}"

        except Exception as e:
            error_msg = f"Failed to enable audit logging: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def get_remediation_functions(self):
        """
        Return a mapping of policy checks to remediation functions.
        """
        return {
            "private_cluster_endpoint": self.remediate_private_cluster_endpoint,
            "supported_kubernetes_version": self.remediate_supported_kubernetes_version,
            "encrypted_kubernetes_secrets": self.remediate_encrypted_kubernetes_secrets,
            "audit_logging_enabled": self.remediate_audit_logging_enabled
        }

    async def remediate(self, policy_check, details):
        """
        Main remediation method that delegates to specific remediation functions.
        """
        logger.info(f"Starting remediation for EKS policy check: {policy_check}")

        # Initialize updated details with the original details
        updated_details = details.copy()

        # Initialize AWS client
        session = self.get_session()
        async with session.client(AWSServiceNameEnum.EKS.value,
                                  region_name=details.get("region", "us-east-1")) as client:
            self.client = client

            # Get the appropriate remediation function
            remediation_functions = self.get_remediation_functions()
            if policy_check not in remediation_functions:
                error_msg = f"No remediation function found for policy check: {policy_check}"
                logger.warning(error_msg)

                # Update remediation results
                if policy_check in self.remediation_results:
                    self.remediation_results[policy_check]["status"] = False
                    self.remediation_results[policy_check]["message"] = error_msg

                # Add remediation failure information
                updated_details["remediate"] = {
                    "status": "fail",
                    "message": f"Error: {error_msg}"
                }

                return False, error_msg, updated_details

            # Call the appropriate remediation function
            success, message = await remediation_functions[policy_check](details)

            # Update remediation results
            if policy_check in self.remediation_results:
                self.remediation_results[policy_check]["status"] = success
                self.remediation_results[policy_check]["message"] = message

            # Update the details with remediation information
            updated_details["remediate"] = {
                "status": "pass" if success else "fail",
                "message": message
            }

            # If successful, update the field values
            if success and policy_check in self.remediation_results:
                for field, value in self.remediation_results[policy_check]["field_updates"].items():
                    updated_details[field] = value

                # Update compliance status
                updated_details["compliance"] = True

            return success, message, updated_details