import asyncio
import json
from celery.utils.log import get_task_logger
from ..config import BaseChecksProcessor
from app.common import (AWSServiceNameEnum, SeverityEnum, ResourceComplianceStatusEnum,
                        LambdaChecksDescriptionEnum, LAMBDA_SUPPORTED_RUNTIMES)

__all__ = ['ChecksProcessor']

logger = get_task_logger(__name__)


class ChecksProcessor(BaseChecksProcessor):
    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.functions = None
        self.findings = {
            "prohibit_public_access": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": LambdaChecksDescriptionEnum.PROHIBIT_PUBLIC_ACCESS.value,
                "severity": SeverityEnum.CRITICAL.value
            },
            "supported_runtimes": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": LambdaChecksDescriptionEnum.SUPPORTED_RUNTIMES.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "multi_az_vpc_operation": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": LambdaChecksDescriptionEnum.MULTI_AZ_VPC_OPERATION.value,
                "severity": SeverityEnum.MEDIUM.value
            }
        }

    async def check_lambda_prohibit_public_access(self):
        """
        Lambda function policies should prohibit public access.
        """
        all_compliant = True

        for function in self.functions.get("Functions", []):
            function_name = function["FunctionName"]

            # Retrieve the function's resource-based policy
            try:
                policy_response = await self.client.get_policy(FunctionName=function_name)
                policy = json.loads(policy_response.get("Policy", "{}"))
                statements = policy.get("Statement", [])

                # Check if any statement allows public access
                public_access = any(
                    statement.get("Effect") == "Allow" and
                    (statement.get("Principal") == "*" or statement.get("Principal", {}).get("AWS") == "*")
                    for statement in statements
                )
            except self.client.exceptions.ResourceNotFoundException:
                public_access = False

            self.findings["prohibit_public_access"]["details"].append({
                "function_name": function_name,
                "compliance": not public_access,
                "region": self.client.meta.region_name
            })

            if public_access:
                all_compliant = False

        if not all_compliant:
            self.findings["prohibit_public_access"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_lambda_supported_runtimes(self):
        """
        Lambda functions should use supported runtimes.
        """
        all_compliant = True

        # Updated supported runtimes list as per the latest AWS documentation
        for function in self.functions.get("Functions", []):
            function_name = function["FunctionName"]
            runtime = function.get("Runtime", "")

            # Check if the runtime is supported
            is_supported = runtime in LAMBDA_SUPPORTED_RUNTIMES

            self.findings["supported_runtimes"]["details"].append({
                "function_name": function_name,
                "runtime": runtime,
                "compliance": is_supported,
                "region": self.client.meta.region_name
            })

            if not is_supported:
                all_compliant = False

        if not all_compliant:
            self.findings["supported_runtimes"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_lambda_multi_az_vpc_operation(self):
        """
        VPC Lambda functions should operate in multiple Availability Zones.
        """
        all_compliant = True

        for function in self.functions.get("Functions", []):
            function_name = function["FunctionName"]

            # Retrieve the function's configuration
            function_config = await self.client.get_function_configuration(FunctionName=function_name)
            vpc_config = function_config.get("VpcConfig", {})

            # Skip functions not in a VPC
            if not vpc_config or not vpc_config.get("SubnetIds"):
                continue

            # Check if the function is associated with subnets in multiple Availability Zones
            subnets = vpc_config.get("SubnetIds", [])
            azs = set()

            if subnets:
                for subnet_id in subnets:
                    try:
                        subnet_response = await self.ec2_client.describe_subnets(SubnetIds=[subnet_id])
                        for subnet in subnet_response.get("Subnets", []):
                            azs.add(subnet.get("AvailabilityZone"))
                    except Exception as e:
                        logger.error(f"Error retrieving subnet {subnet_id} details: {str(e)}")

            multi_az = len(azs) > 1

            self.findings["multi_az_vpc_operation"]["details"].append({
                "function_name": function_name,
                "compliance": multi_az,
                "region": self.client.meta.region_name
            })

            if not multi_az and subnets:  # Only mark non-compliant if it's in a VPC
                all_compliant = False

        if not all_compliant:
            self.findings["multi_az_vpc_operation"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    def get_check_functions(self):
        return [
            self.check_lambda_prohibit_public_access,
            self.check_lambda_supported_runtimes,
            self.check_lambda_multi_az_vpc_operation
        ]

    async def run_checks(self):
        for region in self.regions:
            logger.info(f"Scanning region: {region}")
            session = self.get_session(region)
            if not await self.is_region_accessible():
                logger.warning(f"Region {region} is not accessible. Skipping checks.")
                continue
            async with session.client(AWSServiceNameEnum.Lambda.value, region_name=region) as client, \
                     session.client(AWSServiceNameEnum.EC2.value, region_name=region) as ec2_client:
                self.client = client
                self.ec2_client = ec2_client
                self.functions = await self.client.list_functions()

                for check_function in self.get_check_functions():
                    await check_function()
            await asyncio.sleep(5)

        return self.findings
