import json
from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, LambdaChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from ..data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "prohibit_public_access": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": LambdaChecksDescriptionEnum.PROHIBIT_PUBLIC_ACCESS.value,
                "severity": SeverityEnum.CRITICAL.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            functions = (cached.get("functions") or {}).get("Functions", [])
            function_details = cached.get("function_details", {})

            for function in functions:
                function_name = function.get("FunctionName")
                public_access = False

                # Check if function has a resource-based policy
                function_detail = function_details.get(function_name, {})
                policy_data = function_detail.get("policy")
                
                if policy_data and policy_data.get("Policy"):
                    try:
                        policy = json.loads(policy_data.get("Policy", "{}"))
                        statements = policy.get("Statement", [])

                        # Check if any statement allows public access
                        public_access = any(
                            statement.get("Effect") == "Allow" and
                            (statement.get("Principal") == "*" or 
                             statement.get("Principal", {}).get("AWS") == "*")
                            for statement in statements
                        )
                    except (json.JSONDecodeError, TypeError):
                        # If we can't parse the policy, assume no public access
                        public_access = False

                # Update overall status if any function has public access
                if findings["prohibit_public_access"]["status"] == ResourceComplianceStatusEnum.PASS.value and public_access:
                    findings["prohibit_public_access"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["prohibit_public_access"]["details"].append({
                    "function_name": function_name,
                    "compliance": not public_access,
                    "region": region
                })

        return findings

    def remediate(self):
        """
        Remediation for Lambda public access is complex and requires careful consideration
        of the specific use case. This is typically handled manually.
        """
        pass
