from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, LambdaChecksDescriptionEnum, LAMBDA_SUPPORTED_RUNTIMES
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from ..data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "supported_runtimes": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": LambdaChecksDescriptionEnum.SUPPORTED_RUNTIMES.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            functions = (cached.get("functions") or {}).get("Functions", [])

            for function in functions:
                function_name = function.get("FunctionName")
                runtime = function.get("Runtime", "")

                # Check if the runtime is supported
                is_supported = runtime in LAMBDA_SUPPORTED_RUNTIMES

                # Update overall status if any function has unsupported runtime
                if findings["supported_runtimes"]["status"] == ResourceComplianceStatusEnum.PASS.value and not is_supported:
                    findings["supported_runtimes"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["supported_runtimes"]["details"].append({
                    "function_name": function_name,
                    "runtime": runtime,
                    "compliance": is_supported,
                    "region": region
                })

        return findings

    def remediate(self):
        """
        Remediation for unsupported runtimes requires updating the function code
        and runtime configuration. This is typically handled manually.
        """
        pass
