from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, LambdaChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from ..data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "multi_az_vpc_operation": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": LambdaChecksDescriptionEnum.MULTI_AZ_VPC_OPERATION.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            functions = (cached.get("functions") or {}).get("Functions", [])
            function_details = cached.get("function_details", {})
            subnet_details = cached.get("subnet_details", {})

            for function in functions:
                function_name = function.get("FunctionName")
                
                # Get function configuration from cached data
                function_detail = function_details.get(function_name, {})
                config = function_detail.get("configuration", {})
                vpc_config = config.get("VpcConfig", {}) if config else {}

                # Skip functions not in a VPC
                if not vpc_config or not vpc_config.get("SubnetIds"):
                    # Functions not in VPC are not evaluated for this check
                    continue

                # Check if the function is associated with subnets in multiple Availability Zones
                subnet_ids = vpc_config.get("SubnetIds", [])
                azs = set()

                for subnet_id in subnet_ids:
                    subnets = subnet_details.get(subnet_id, [])
                    for subnet in subnets:
                        az = subnet.get("AvailabilityZone")
                        if az:
                            azs.add(az)

                multi_az = len(azs) > 1

                # Update overall status if any VPC function is not multi-AZ
                if findings["multi_az_vpc_operation"]["status"] == ResourceComplianceStatusEnum.PASS.value and not multi_az:
                    findings["multi_az_vpc_operation"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["multi_az_vpc_operation"]["details"].append({
                    "function_name": function_name,
                    "compliance": multi_az,
                    "region": region,
                    "subnet_ids": subnet_ids,
                    "availability_zones": list(azs),
                    "multi_az": multi_az
                })

        return findings

    def remediate(self):
        """
        Remediation for multi-AZ VPC operation requires updating the function's
        VPC configuration to include subnets from multiple availability zones.
        This is typically handled manually.
        """
        pass
