from app import app
from ..models.mysql import get_cloud_provider_by_id
from app.common.enums import CloudProviderNameEnum, AWSRegionNameEnum


__all__ = ['CloudProviderRegionsService']


class CloudProviderRegionsService:
    def __init__(self, cloud_provider_id):
        self.cloud_provider_id = cloud_provider_id
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Get cloud provider details
        cloud_provider = await get_cloud_provider_by_id(self.conn_pool, self.cloud_provider_id)

        print(cloud_provider)
        
        if not cloud_provider:
            return {"regions": []}
            
        # Get regions based on cloud provider
        regions = []
        if cloud_provider['name'].lower() == CloudProviderNameEnum.AWS.value:
            regions = self._get_aws_regions()
        # elif cloud_provider['name'] == CloudProviderNameEnum.AZURE.value:
        #     regions = self._get_azure_regions()
        # Add more cloud providers as needed
            
        return {"regions": regions}
        
    def _get_aws_regions(self):
        """Get list of AWS regions formatted similar to AWS Console.
        - name: short city label (e.g., "N. Virginia", "Tokyo")
        - id: region code (e.g., "us-east-1")
        - description: geographic group header (e.g., "United States", "Asia Pacific")
        """
        region_group_and_city = {
            "us-east-1": ("United States", "N. Virginia"),
            "us-east-2": ("United States", "Ohio"),
            "us-west-1": ("United States", "N. California"),
            "us-west-2": ("United States", "Oregon"),
            "ca-central-1": ("Canada", "Central"),
            "eu-west-1": ("Europe", "Ireland"),
            "eu-west-2": ("Europe", "London"),
            "eu-west-3": ("Europe", "Paris"),
            "eu-central-1": ("Europe", "Frankfurt"),
            "eu-north-1": ("Europe", "Stockholm"),
            "ap-southeast-1": ("Asia Pacific", "Singapore"),
            "ap-southeast-2": ("Asia Pacific", "Sydney"),
            "ap-northeast-1": ("Asia Pacific", "Tokyo"),
            "ap-northeast-2": ("Asia Pacific", "Seoul"),
            "ap-northeast-3": ("Asia Pacific", "Osaka"),
            "ap-south-1": ("Asia Pacific", "Mumbai"),
            "sa-east-1": ("South America", "São Paulo"),
            "me-south-1": ("Middle East", "Bahrain"),
            "af-south-1": ("Africa", "Cape Town"),
        }

        regions = []
        for region in AWSRegionNameEnum:
            region_id = region.value
            group, city = region_group_and_city.get(region_id, (None, region.description))

            display_name = f"{group} ({city})" if group else city

            regions.append({
                "id": region_id,
                "name": display_name,
                "description": group
            })

        return regions
        
    # def _get_azure_regions(self):
    #     """Get list of Azure regions"""
    #     regions = []
    #     for region in AzureRegionNameEnum:
    #         region_id = region.value
    #         # Format region name for display
    #         region_name = region_id.replace('-', ' ').title()
    #
    #         # Add descriptive names for well-known regions
    #         descriptions = {
    #             # Add Azure region descriptions
    #         }
    #
    #         description = descriptions.get(region_id, None)
    #
    #         regions.append({
    #             "id": region_id,
    #             "name": region_name,
    #             "description": description
    #         })
    #
    #     return regions