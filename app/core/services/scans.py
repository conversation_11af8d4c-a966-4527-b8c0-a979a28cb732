from app import app
from ..models.mysql import (
    check_active_running_scans,
    create_scan,
    check_scan_from_account,
    get_services,
    update_scan,
    get_user_scans,
    get_user_scans_count,
    get_scan_detail,
    get_scan_services,
    sync_scan_services,
    get_user_by_id,
    get_account_detail,
    get_check_detail,
)
from app.common.exception import (
    MaxScanAccountsException,
    ScanAlreadyRunningException,
    ServiceRequiredException,
    ServiceNotValidException,
    ScanNotFoundException
)
from app.common.enums import ScanStatusEnum, ScanServiceStatusEnum
from app.core.services.celery_conf import scan_service_task
from app.core.services.resource_explorer import ResourceExplorerService
from app.core.services.api_cloud_providers.aws.region_intelligence import RegionIntelligenceManager
from app.common.slack import send_slack_message


__all__ = ["CreateScanService", "GetScansService", "GetScanDetailService"]


class CreateScanService:
    def __init__(self, message, user):
        self.message = message
        self.user_id = user["user_id"]
        self.user = user
        self.conn_pool = app.state.connection_pool

    async def validate_scan_status_for_account(self):
        # Check if the scan is already running for the given account
        active_running_accounts, given_account_running_scans = await check_active_running_scans(
            self.conn_pool, self.message, self.user_id
        )
        if active_running_accounts >= app.config.MAX_SCAN_ACCOUNTS:
            raise MaxScanAccountsException
        if given_account_running_scans:
            raise ScanAlreadyRunningException

    async def validate_services(self):
        """Fetch enabled services for the given cloud provider from DB.

        We no longer take services from payload; instead, we use `services` table entries
        where `is_enable = 1` for the given `cloud_provider_id`.
        """
        cloud_provider_services = await get_services(self.conn_pool, self.message.cloud_provider_id)
        if not cloud_provider_services:
            app.logger.warning("No enabled services found for cloud_provider_id=%s", self.message.cloud_provider_id)
        # Ignore any services passed in payload; use all enabled services
        return cloud_provider_services

    async def discover_regions_for_services(self, services):
        """
        Discover regions containing resources for the enabled services using Resource Explorer.
        This replaces the need to specify regions in the scan payload and initializes
        the Region Intelligence Manager for service-level optimization.
        """
        try:
            # Get parsed account credentials (includes workspace_id, aws_account_id)
            from app.core.models.mysql import get_account_credentials
            credentials = await get_account_credentials(self.conn_pool, self.message.account_id)
            if not credentials:
                app.logger.warning("Account credentials not found, using provided regions as fallback")
                return self.message.regions or []
            if not credentials.get('access_key') or not credentials.get('secret_key'):
                app.logger.warning("AWS credentials missing keys, using provided regions as fallback")
                return self.message.regions or []

            # Initialize Region Intelligence Manager for service-level optimization
            region_intelligence = RegionIntelligenceManager(credentials)
            initialization_success = await region_intelligence.initialize(services)

            if initialization_success:
                # Store region intelligence manager in message for later use by service tasks
                self.message.region_intelligence_mapping = region_intelligence.get_service_region_mapping()
                app.logger.info("🧠 Region Intelligence Manager initialized successfully")

                # Get optimized regions for overall scan
                resource_explorer = ResourceExplorerService(credentials)
                discovered_regions = await resource_explorer.get_regions_for_scan(services)

                if discovered_regions:
                    app.logger.info(f"🌍 Optimized scan will cover {len(discovered_regions)} regions: {discovered_regions}")
                    return discovered_regions
                else:
                    app.logger.warning("No regions discovered, using provided regions as fallback")
                    return self.message.regions or []
            else:
                app.logger.warning("Region Intelligence Manager initialization failed, using basic region discovery")
                # Fallback to basic Resource Explorer
                resource_explorer = ResourceExplorerService(credentials)
                discovered_regions = await resource_explorer.get_regions_for_scan(services)

                if discovered_regions:
                    app.logger.info(f"🌍 Basic region discovery found {len(discovered_regions)} regions: {discovered_regions}")
                    return discovered_regions
                else:
                    app.logger.warning("No regions discovered, using provided regions as fallback")
                    return self.message.regions or []

        except Exception as e:
            app.logger.error(f"Region discovery failed: {e}, using provided regions as fallback")
            return self.message.regions or []

    async def get_account_scan_id(self):
        # Check if the scan is already exists for the given account
        scan_id = await check_scan_from_account(self.conn_pool, self.message)
        if scan_id:
            # set scan status to running
            await update_scan(self.conn_pool, scan_id, ScanStatusEnum.RUNNING.value, update_start=True)
        else:
            # Create a new scan if it doesn't exist
            scan_id = await create_scan(self.conn_pool, self.message)
        return scan_id

    async def get_account_checks_according_to_service(self,service_id):
        # return checks according to service for script path

        checks_path = await get_check_detail(self.conn_pool,service_id)

        if checks_path:
            return checks_path
        else:
            return 0


    async def process(self):
        services = await self.validate_services()
        await self.validate_scan_status_for_account()

        # Discover regions containing resources for the enabled services
        discovered_regions = await self.discover_regions_for_services(services)
        
        # Update the message with discovered regions
        self.message.regions = discovered_regions

        # Check if this is the user's first scan before creating the scan
        user_scan_count = await get_user_scans_count(self.conn_pool, self.user_id)
        is_first_scan = user_scan_count == 0

        scan_id = await self.get_account_scan_id()

        checks_path = {}

        # sync scan_services for all the service (either add or update the scan_service status to pending)
        for service in services:

            await sync_scan_services(self.conn_pool, scan_id, service["id"], ScanServiceStatusEnum.PENDING.value)

            # # Get all checks for this service and store in checks_path
            # service_checks=await self.get_account_checks_according_to_service(service["id"])
            # checks_path[service_id] = [check["script_path"] for check in service_checks]

        # Start the scan for each service
        for service in services:
            scan_service_task.apply_async(args=(scan_id, service, self.message.__dict__))

        # Send Slack notification for first scan
        if is_first_scan:
            await self._send_first_scan_notification()

    async def _send_first_scan_notification(self):
        """Send Slack notification for user's first scan"""
        try:
            # Get user details
            user_details = await get_user_by_id(self.conn_pool, self.user_id)

            # Format username from first_name and last_name
            username = ""
            if user_details:
                first_name = user_details.get("first_name", "")
                last_name = user_details.get("last_name", "")
                if first_name or last_name:
                    username = f"{first_name} {last_name}".strip()
                else:
                    username = user_details.get("email", "Unknown")
            else:
                username = "Unknown"

            # Get workspace name from user details
            workspace_name = "Unknown Workspace"
            if user_details and user_details.get("workspace_id"):
                from ..models.mysql import get_workspace_name
                workspace_name = await get_workspace_name(self.conn_pool, user_details["workspace_id"])

            # Send notification
            message = f"First scan initiated in `{workspace_name}` by `{username}`."
            await send_slack_message(message)

        except Exception as slack_error:
            # Log the error but don't fail the scan process
            app.logger.warning(f"Failed to send Slack notification for first scan: {slack_error}")


class GetScansService:
    def __init__(self, user, page=1, page_size=10):
        self.user_id = user["user_id"]
        self.conn_pool = app.state.connection_pool
        self.page = page
        self.page_size = page_size

    async def process(self):
        total_scans = await get_user_scans_count(self.conn_pool, self.user_id)
        scans = await get_user_scans(self.conn_pool, self.user_id, self.page, self.page_size)

        total_pages = (total_scans + self.page_size - 1) // self.page_size  # Ceiling division

        return {
            "data": scans,
            "pagination": {"total": total_scans, "page": self.page, "page_size": self.page_size, "total_pages": total_pages},
        }


class GetScanDetailService:
    def __init__(self, user, scan_id):
        self.user_id = user["user_id"]
        self.scan_id = scan_id
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Get basic scan information
        scan = await get_scan_detail(self.conn_pool, self.scan_id, self.user_id)
        if not scan:
            raise ScanNotFoundException

        # Get services for this scan
        scan_services = await get_scan_services(self.conn_pool, self.scan_id)

        # Normalize service names to AWS official casing for response
        aws_display_name_map = {
            "lambda": "Lambda",
            "ecs": "ECS",
            "eks": "EKS",
            "elasticache": "ElastiCache",
            "elb": "ELB",
            "efs": "EFS",
            "rds": "Aurora and RDS",
            "ec2": "EC2",
            "s3": "S3",
            "iam": "IAM",
        }
        for svc in scan_services:
            name = svc.get("service_name")
            if isinstance(name, str):
                svc["service_name"] = aws_display_name_map.get(name.lower(), name)

        # Combine data
        scan["services"] = scan_services

        return scan
