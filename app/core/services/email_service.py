import aioboto3
import logging
from typing import Dict, Any
from app import app
from app.common.exception import EmailSendException

__all__ = ['EmailService']

logger = logging.getLogger(__name__)


class EmailService:
    """AWS SES Email Service for sending emails"""
    
    def __init__(self):
        self.region = app.config.AWS_SES_REGION
        self.access_key_id = app.config.AWS_SES_ACCESS_KEY_ID
        self.secret_access_key = app.config.AWS_SES_SECRET_ACCESS_KEY
        self.sender_email = app.config.AWS_SES_SENDER_EMAIL
        self.sender_name = app.config.AWS_SES_SENDER_NAME
        
    async def send_email(self, recipient_email: str, subject: str, html_body: str, text_body: str = None) -> bool:
        """
        Send email using AWS SES
        
        Args:
            recipient_email: Email address of the recipient
            subject: Email subject
            html_body: HTML content of the email
            text_body: Plain text content (optional)
            
        Returns:
            bool: True if email sent successfully, False otherwise
            
        Raises:
            EmailSendException: If email sending fails
        """
        try:
            session = aioboto3.Session(
                aws_access_key_id=self.access_key_id,
                aws_secret_access_key=self.secret_access_key,
                region_name=self.region
            )
            
            async with session.client('ses') as ses_client:
                # Prepare email content
                body_content = {'Html': {'Data': html_body, 'Charset': 'UTF-8'}}
                if text_body:
                    body_content['Text'] = {'Data': text_body, 'Charset': 'UTF-8'}
                
                # Send email
                response = await ses_client.send_email(
                    Source=f"{self.sender_name} <{self.sender_email}>",
                    Destination={'ToAddresses': [recipient_email]},
                    Message={
                        'Subject': {'Data': subject, 'Charset': 'UTF-8'},
                        'Body': body_content
                    }
                )
                
                message_id = response.get('MessageId')
                logger.info(f"Email sent successfully to {recipient_email}. MessageId: {message_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to send email to {recipient_email}: {str(e)}")
            raise EmailSendException(f"Failed to send email: {str(e)}")
    
    async def send_otp_email(self, recipient_email: str, otp_code: str, first_name: str = None) -> bool:
        """
        Send OTP verification email
        
        Args:
            recipient_email: Email address of the recipient
            otp_code: 6-digit OTP code
            first_name: First name of the user (optional)
            
        Returns:
            bool: True if email sent successfully
        """
        subject = "Verify Your Email - CloudAudit"
        
        # Generate HTML email content
        html_body = self._generate_otp_html_template(otp_code, first_name)
        
        # Generate plain text fallback
        text_body = self._generate_otp_text_template(otp_code, first_name)
        
        return await self.send_email(recipient_email, subject, html_body, text_body)
    
    def _generate_otp_html_template(self, otp_code: str, first_name: str = None) -> str:
        """Generate HTML template for OTP email"""
        greeting = f"Hi {first_name}," if first_name else "Hi there,"
        
        html_template = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Email Verification - CloudAudit</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f4f4f4;
                }}
                .container {{
                    background-color: #ffffff;
                    padding: 40px;
                    border-radius: 10px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                }}
                .logo {{
                    font-size: 28px;
                    font-weight: bold;
                    color: #2563eb;
                    margin-bottom: 10px;
                }}
                .otp-code {{
                    background-color: #f8fafc;
                    border: 2px dashed #2563eb;
                    padding: 20px;
                    text-align: center;
                    margin: 30px 0;
                    border-radius: 8px;
                }}
                .otp-digits {{
                    font-size: 36px;
                    font-weight: bold;
                    color: #2563eb;
                    letter-spacing: 8px;
                    font-family: 'Courier New', monospace;
                }}
                .warning {{
                    background-color: #fef3c7;
                    border-left: 4px solid #f59e0b;
                    padding: 15px;
                    margin: 20px 0;
                    border-radius: 4px;
                }}
                .footer {{
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #e5e7eb;
                    font-size: 14px;
                    color: #6b7280;
                    text-align: center;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">CloudAudit</div>
                    <h1 style="color: #1f2937; margin: 0;">Email Verification</h1>
                </div>
                
                <p>{greeting}</p>
                
                <p>Thank you for signing up for CloudAudit! To complete your registration, please verify your email address using the verification code below:</p>
                
                <div class="otp-code">
                    <p style="margin: 0 0 10px 0; font-size: 16px; color: #6b7280;">Your verification code is:</p>
                    <div class="otp-digits">{otp_code}</div>
                </div>
                
                <p>Enter this code in the verification form to activate your account.</p>
                
                <div class="warning">
                    <strong>Important:</strong> This code will expire in 5 minutes for security reasons. If you didn't request this verification, please ignore this email.
                </div>
                
                <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
                
                <div class="footer">
                    <p>Best regards,<br>The CloudAudit Team</p>
                    <p style="margin-top: 20px;">
                        This is an automated message. Please do not reply to this email.
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
        return html_template
    
    def _generate_otp_text_template(self, otp_code: str, first_name: str = None) -> str:
        """Generate plain text template for OTP email"""
        greeting = f"Hi {first_name}," if first_name else "Hi there,"
        
        text_template = f"""
{greeting}

Thank you for signing up for CloudAudit! To complete your registration, please verify your email address using the verification code below:

Your verification code is: {otp_code}

Enter this code in the verification form to activate your account.

IMPORTANT: This code will expire in 5 minutes for security reasons. If you didn't request this verification, please ignore this email.

If you have any questions or need assistance, please don't hesitate to contact our support team.

Best regards,
The CloudAudit Team

This is an automated message. Please do not reply to this email.
        """
        return text_template.strip()
