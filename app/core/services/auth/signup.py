from app import app
from app.core.models.mysql import get_user_data
from app.common import (UserExistsException, InternalServerException, WorkspaceNameRequiredException, EmailSendException)
from app.core.services.otp_service import OTPService
from app.core.services.celery_conf.tasks import send_otp_email_task
import logging

__all__ = ['SignUpService']

logger = logging.getLogger(__name__)


class SignUpService:
    """
    Modified SignUpService that sends OTP for email verification instead of immediately creating user
    """
    def __init__(self, message):
        self.message = message
        self.conn_pool = app.state.connection_pool
        self.otp_service = OTPService()

    async def process(self):
        """
        Process signup request by sending OTP email for verification
        """
        # Check if user already exists
        user = await get_user_data(self.conn_pool, self.message)
        if user and user.get("email") and user["email"].lower() == self.message.email.lower():
            raise UserExistsException("User with this email already exists")

        # Validate required fields
        if not self.message.workspace_name:
            raise WorkspaceNameRequiredException("Workspace name is required")

        try:
            # Prepare signup data to store with OTP
            signup_data = {
                "email": self.message.email,
                "password": self.message.password,  # Will be encrypted in OTP service
                "workspace_name": self.message.workspace_name,
                "first_name": self.message.first_name,
                "last_name": self.message.last_name
            }

            # Create and store OTP
            otp_code = await self.otp_service.create_and_store_otp(
                email=self.message.email,
                signup_data=signup_data
            )

            # Send OTP email asynchronously using Celery
            send_otp_email_task.delay(
                recipient_email=self.message.email,
                otp_code=otp_code,
                first_name=self.message.first_name
            )

            logger.info(f"OTP sent for signup verification: {self.message.email}")

            return {
                "message": "Verification code sent to your email",
                "email": self.message.email,
                "expires_in_minutes": app.config.OTP_EXPIRY_MINUTES
            }

        except EmailSendException as e:
            logger.error(f"Failed to send OTP email for signup: {str(e)}")
            raise InternalServerException("Failed to send verification email. Please try again.")

        except Exception as e:
            logger.error(f"Signup process failed: {str(e)}")
            raise InternalServerException("Signup process failed. Please try again.")
