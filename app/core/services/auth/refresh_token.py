import jwt
from datetime import datetime, timedelta
from app import app
from app.core.models.mysql import get_user_refresh_token, update_refresh_token
from app.common import (InvalidCredentialsException, InternalServerException, create_access_token, create_refresh_token)


__all__ = ['RefreshTokenService']


class RefreshTokenService:
    def __init__(self, message):
        self.message = message
        self.conn_pool = app.state.connection_pool

    async def process(self) -> dict:
        try:
            payload: dict = jwt.decode(self.message.refresh_token, app.config.JWT_SECRET_KEY+"REFRESH_TOKEN",
                                       algorithms=[app.config.ENCRYPTION_ALGORITHM])
        except Exception as error:
            raise InvalidCredentialsException

        if not payload.get("sub"):
            raise InvalidCredentialsException

        result: dict = await get_user_refresh_token(self.conn_pool, self.message.refresh_token)
        email, user_id, workspace_id = (result.get(key) for key in
                                        ("email", "user_id", "workspace_id")) if result else (None, None, None)

        if email != payload.get("sub") or not user_id:
            raise InvalidCredentialsException

        access_token: str = create_access_token(
            data={"sub": email, "user_id": user_id, "workspace_id": workspace_id},
            expires_delta=timedelta(minutes=app.config.ACCESS_TOKEN_EXPIRE_MINUTES)
        )

        refresh_token: str = create_refresh_token(
            data={"sub": email, "user_id": user_id, "workspace_id": workspace_id},
            expires_delta=timedelta(days=app.config.REFRESH_TOKEN_EXPIRE_DAYS)
        )

        try:
            await update_refresh_token(
                conn_pool=self.conn_pool,
                user_id=result["user_id"],
                refresh_token=refresh_token,
                expires_at=datetime.utcnow() + timedelta(days=app.config.REFRESH_TOKEN_EXPIRE_DAYS))

            return {
                "access_token": access_token,
                "refresh_token": refresh_token
            }
        except Exception as error:
            raise InternalServerException
