from app import app
from app.core.models.mysql import (get_user_data, add_new_user, add_workspace, update_workspace_user, update_user_admin,
                                   add_user_role)
from app.common import (UserExistsException, InternalServerException, get_utc_timestamp,
                        PredefinedUser<PERSON><PERSON>Enum, OTPExpiredException, OTPNotFoundException, OTPAlreadyVerifiedException)
from app.common.slack import send_slack_message
from app.core.services.otp_service import OTPService
import logging

__all__ = ['OTPVerificationService', 'ResendOTPService']

logger = logging.getLogger(__name__)


class OTPVerificationService:
    """
    Service to verify OTP and complete user registration
    """
    def __init__(self, message):
        self.message = message
        self.conn_pool = app.state.connection_pool
        self.otp_service = OTPService()

    async def process(self):
        """
        Verify OTP and create user account if valid
        """
        try:
            # Verify OTP and get signup data
            signup_data = await self.otp_service.verify_otp(
                email=self.message.email,
                otp_code=self.message.otp_code
            )

            # Check if user was created in the meantime (race condition protection)
            user = await get_user_data(self.conn_pool, self.message)
            if user and user.get("email") and user["email"].lower() == self.message.email.lower():
                raise UserExistsException("User with this email already exists")

            # Create user account with verified email
            await self._create_user_account(signup_data)

            logger.info(f"User account created successfully after OTP verification: {self.message.email}")

            return {
                "message": "Email verified and account created successfully",
                "email": self.message.email
            }

        except (OTPExpiredException, OTPNotFoundException, OTPAlreadyVerifiedException) as e:
            logger.warning(f"OTP verification failed for {self.message.email}: {str(e)}")
            raise e

        except UserExistsException as e:
            logger.warning(f"User already exists during OTP verification: {self.message.email}")
            raise e

        except Exception as e:
            logger.error(f"OTP verification process failed: {str(e)}")
            raise InternalServerException("Account creation failed. Please try again.")

    async def _create_user_account(self, signup_data):
        """
        Create user account with the provided signup data
        """
        try:
            # Create workspace
            workspace_id = await add_workspace(
                self.conn_pool,
                signup_data['workspace_name'],
                get_utc_timestamp()
            )

            # Create user
            user_id = await add_new_user(
                self.conn_pool,
                signup_data['email'],
                signup_data['password_hash'],  # Already encrypted in OTP service
                workspace_id,
                get_utc_timestamp(),
                None,  # created_by is None for initial signup
                signup_data.get('first_name'),
                signup_data.get('last_name')
            )

            # Set up workspace ownership
            await update_workspace_user(self.conn_pool, user_id, workspace_id)
            await update_user_admin(self.conn_pool, user_id)
            await add_user_role(self.conn_pool, user_id, PredefinedUserRoleEnum.OWNER.value)

            # Send Slack notification (non-blocking)
            try:
                username = ""
                if signup_data.get('first_name') or signup_data.get('last_name'):
                    first_name = signup_data.get('first_name') or ""
                    last_name = signup_data.get('last_name') or ""
                    username = f"{first_name} {last_name}".strip()
                else:
                    username = signup_data['email']

                await send_slack_message(
                    f"`{signup_data['workspace_name']}` workspace created by `{username}`."
                )
            except Exception as slack_error:
                logger.warning(f"Failed to send Slack notification for user signup: {slack_error}")

        except Exception as e:
            logger.error(f"Failed to create user account: {str(e)}")
            raise InternalServerException("Failed to create user account")


class ResendOTPService:
    """
    Service to resend OTP for email verification
    """
    def __init__(self, message):
        self.message = message
        self.conn_pool = app.state.connection_pool
        self.otp_service = OTPService()

    async def process(self):
        """
        Resend OTP for the given email
        """
        try:
            from app.core.services.celery_conf.tasks import send_otp_email_task
            
            # Resend OTP
            new_otp_code = await self.otp_service.resend_otp(self.message.email)

            # Get OTP status to extract first name for email
            otp_status = await self.otp_service.get_otp_status(self.message.email)
            first_name = None
            
            if otp_status:
                # Try to get first name from stored signup data
                from app.core.models.mysql import get_otp_by_email
                otp_record = await get_otp_by_email(self.conn_pool, self.message.email)
                if otp_record and otp_record.get('signup_data'):
                    import json
                    signup_data = json.loads(otp_record['signup_data'])
                    first_name = signup_data.get('first_name')

            # Send new OTP email asynchronously
            send_otp_email_task.delay(
                recipient_email=self.message.email,
                otp_code=new_otp_code,
                first_name=first_name
            )

            logger.info(f"OTP resent for email: {self.message.email}")
            
            return {
                "message": "New verification code sent to your email",
                "email": self.message.email,
                "expires_in_minutes": app.config.OTP_EXPIRY_MINUTES
            }

        except Exception as e:
            logger.error(f"Resend OTP failed for {self.message.email}: {str(e)}")
            raise e
