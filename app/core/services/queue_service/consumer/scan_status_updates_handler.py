from ..common import (channel_basic_consume, handle_consumer_callback, publish_failed_message)
from .....common import QueueEnum, PREFETCH_COUNT


__all_ = ['ConsumerScanStatusUpdatesHandler']


class ConsumerScanStatusUpdatesHandler:

    def __init__(self, queue_name):
        self.queue_name = queue_name

    @staticmethod
    async def success_callback(payload):
        from ....services import WebSocketManager
        await WebSocketManager().send_message(payload)

    @staticmethod
    async def failure_callback(params):
        await publish_failed_message(params, QueueEnum.SCAN_STATUS_UPDATES_FAILED.value)

    async def callback(self, message):
        await handle_consumer_callback(self,
                                       message,
                                       self.success_callback,
                                       self.failure_callback,
                                       handle_redelivered=self.failure_callback)

    async def run(self):
        await channel_basic_consume(self.callback, self.queue_name,
                                    prefetch_count=QueueEnum.SCAN_STATUS_UPDATES.value[PREFETCH_COUNT])
