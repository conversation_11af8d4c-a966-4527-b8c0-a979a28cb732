import json
import aio_pika
from celery.utils.log import get_task_logger

from app import app


logger = get_task_logger(__name__)


class ConsumerScanDLQHandler:
    def __init__(self, queue_name: str):
        self.queue_name = queue_name

    async def _handle_dlq_message(self, message: aio_pika.IncomingMessage, channel: aio_pika.Channel):
        async with message.process(requeue=False):
            headers = message.headers or {}
            routing_key = message.routing_key

            # Extract retry metadata
            retry_count = int(headers.get("retry_count", 0))
            max_retries = int(headers.get("max_retries", 3))

            # Optional identifiers for tracking
            scan_id = headers.get("scan_id")
            service_id = headers.get("service_id")
            check_id = headers.get("check_id")

            logger.warning(
                "DLQ message received: rk=%s retry=%s/%s scan=%s service=%s check=%s",
                routing_key,
                retry_count,
                max_retries,
                scan_id,
                service_id,
                check_id,
            )

            if retry_count < max_retries:
                # Re-publish to primary exchange with incremented retry count
                exchange = await channel.get_exchange(app.config.RABBIT_MQ_DIRECT_EXCHANGE, ensure=True)
                new_headers = dict(headers)
                new_headers["retry_count"] = retry_count + 1

                await exchange.publish(
                    aio_pika.Message(
                        body=message.body,
                        headers=new_headers,
                        delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
                    ),
                    routing_key=routing_key,
                )
                logger.info("Requeued DLQ message to %s (retry %s)", routing_key, new_headers["retry_count"])
            else:
                # Exhausted retries: keep acknowledged in DLQ (for manual ops) and log
                logger.error(
                    "Max retries exceeded for rk=%s scan=%s service=%s check=%s; leaving in DLQ",
                    routing_key,
                    scan_id,
                    service_id,
                    check_id,
                )

    async def run(self):
        async with app.rmq_channel_pool.acquire() as channel:
            queue = await channel.declare_queue(self.queue_name, durable=True)
            async with queue.iterator() as queue_iter:
                async for message in queue_iter:
                    try:
                        await self._handle_dlq_message(message, channel)
                    except Exception as e:
                        logger.exception("Failed to process DLQ message: %s", e)

