import logging
import boto3
from collections import defaultdict
from typing import Dict, List

# Get logger instance
logger = logging.getLogger(__name__)

class ResourceExplorerService:
    """Service to discover AWS regions containing resources for enabled services using service-based queries only."""
    
    # Services that can be searched directly using service: filter
    SUPPORTED_SERVICES = [
        'ec2', 'rds', 'lambda', 'ecs', 'eks', 's3', 'iam', 
        'elb', 'efs', 'elasticache', 'cloudformation', 'sns', 'sqs'
    ]
    
    def __init__(self, credentials: dict, region: str = 'ap-south-1'):
        """
        Lightweight initializer; no session initialization here.
        """
        self.credentials = credentials
        self.region = region
        self.client = None
    
    def _get_resource_explorer_client(self):
        """Initialize Resource Explorer client lazily"""
        if self.client is None:
            try:
                boto_session = boto3.Session(
                    aws_access_key_id=self.credentials.get('access_key'),
                    aws_secret_access_key=self.credentials.get('secret_key'),
                    aws_session_token=self.credentials.get('session_token'),
                    region_name=self.region,
                )
                self.client = boto_session.client('resource-explorer-2', region_name=self.region)
                
                # Test the client with a simple search to verify it works
                test_response = self.client.search(QueryString='service:ec2', MaxResults=1)
                logger.info("✅ Resource Explorer client initialized successfully")
                
            except Exception as e:
                logger.error(f"❌ Failed to initialize Resource Explorer client: {e}")
                raise
        
        return self.client
    
    async def discover_regions_for_services(self, enabled_services: List[Dict]) -> Dict[str, List[str]]:
        """
        Discover regions containing resources for enabled services using service-based queries only.
        """
        service_region_mapping = {}
        logger.info(f"🔍 Starting region discovery for {len(enabled_services)} enabled services")
        
        try:
            client = self._get_resource_explorer_client()
        except Exception as e:
            logger.error(f"❌ Resource Explorer unavailable: {e}")
            return {service['name']: [] for service in enabled_services}
        
        for service in enabled_services:
            service_name = service['name']
            
            # Check if service is supported
            if service_name not in self.SUPPORTED_SERVICES:
                logger.warning(f"⚠️ Service '{service_name}' not supported for Resource Explorer queries")
                service_region_mapping[service_name] = []
                continue
            
            logger.info(f"🔍 Searching for {service_name} resources...")
            
            try:
                # Use service-based search (the working approach from your code)
                response = client.search(
                    QueryString=f'service:{service_name}', 
                    MaxResults=1000  # Increased to capture more resources
                )
                resources = response.get('Resources', [])
                
                # Extract unique regions from found resources
                regions = set()
                for resource in resources:
                    region = resource.get('Region')
                    if region and region != 'global':  # Exclude global region for regional services
                        regions.add(region)
                
                service_region_mapping[service_name] = sorted(list(regions))
                
                if regions:
                    region_list = sorted(list(regions))
                    logger.info(f"  ✅ {service_name}: Found {len(resources)} resources across {len(region_list)} regions")
                    logger.info(f"      📍 Regions with {service_name} resources: {region_list}")
                else:
                    logger.info(f"  ℹ️ {service_name}: No regional resources found")
                
            except Exception as e:
                logger.error(f"  ❌ {service_name}: Search failed - {e}")
                service_region_mapping[service_name] = []
        
        # Log final mapping with detailed region breakdown
        logger.info("📊 Final Service-Region Mapping Summary:")
        total_services_with_resources = 0
        total_regions_discovered = set()
        
        for service_name, regions in service_region_mapping.items():
            if regions:
                total_services_with_resources += 1
                total_regions_discovered.update(regions)
                logger.info(f"  🔹 '{service_name}' → Available in: {regions}")
            else:
                logger.warning(f"  🔸 '{service_name}' → No regions found (will scan all regions)")
        
        logger.info(f"🎯 Discovery Summary: {total_services_with_resources}/{len(enabled_services)} services have resources")
        logger.info(f"🌍 Total unique regions discovered: {sorted(list(total_regions_discovered))}")
        
        return service_region_mapping
    
    async def get_regions_for_scan(self, enabled_services: List[Dict]) -> List[str]:
        """
        Get all unique regions that contain resources for any enabled service.
        """
        service_region_mapping = await self.discover_regions_for_services(enabled_services)
        
        # Collect all unique regions
        all_regions = set()
        for service_name, regions in service_region_mapping.items():
            if regions:  # Only add regions if service has resources
                all_regions.update(regions)
        
        unique_regions = sorted(list(all_regions))
        
        if unique_regions:
            logger.info(f"🌍 Optimized scan will cover {len(unique_regions)} regions: {unique_regions}")
            logger.info(f"💡 Region optimization: Skipping {len(self._get_all_aws_regions()) - len(unique_regions)} empty regions")
        else:
            logger.warning("⚠️ No regions found with resources - fallback to all regions scan required")
        
        return unique_regions
    
    async def get_service_region_mapping(self, enabled_services: List[Dict]) -> Dict[str, List[str]]:
        """
        Get detailed mapping of services to their regions.
        """
        return await self.discover_regions_for_services(enabled_services)
    
    def get_regions_for_service(self, service_name: str) -> List[str]:
        """
        Synchronous method to get regions for a single service.
        Useful for individual service processors.
        """
        if service_name not in self.SUPPORTED_SERVICES:
            logger.warning(f"⚠️ Service '{service_name}' not supported for Resource Explorer queries")
            return []
        
        try:
            client = self._get_resource_explorer_client()
            
            response = client.search(
                QueryString=f'service:{service_name}', 
                MaxResults=1000
            )
            resources = response.get('Resources', [])
            
            # Extract unique regions
            regions = set()
            for resource in resources:
                region = resource.get('Region')
                if region and region != 'global':
                    regions.add(region)
            
            region_list = sorted(list(regions))
            logger.info(f"🔍 {service_name}: Found {len(resources)} resources across {len(region_list)} regions")
            logger.info(f"    📍 {service_name} available in: {region_list}")
            
            return region_list
            
        except Exception as e:
            logger.error(f"❌ Failed to get regions for {service_name}: {e}")
            return []
    
    def is_service_supported(self, service_name: str) -> bool:
        """
        Check if a service is supported for Resource Explorer queries.
        """
        return service_name in self.SUPPORTED_SERVICES
    
    def get_supported_services(self) -> List[str]:
        """
        Get list of all supported services.
        """
        return self.SUPPORTED_SERVICES.copy()
    
    def _get_all_aws_regions(self) -> List[str]:
        """
        Get all available AWS regions for optimization calculation.
        """
        try:
            ec2_client = boto3.client('ec2', region_name='us-east-1')
            response = ec2_client.describe_regions()
            return [region['RegionName'] for region in response['Regions']]
        except:
            # Fallback to common regions if API call fails
            return [
                'us-east-1', 'us-east-2', 'us-west-1', 'us-west-2',
                'eu-west-1', 'eu-west-2', 'eu-central-1', 'ap-south-1',
                'ap-southeast-1', 'ap-southeast-2', 'ap-northeast-1',
                'ca-central-1', 'sa-east-1'
            ]


# Standalone function version matching your working pattern
def get_service_regions_by_service(credentials: dict = None, region: str = 'ap-south-1'):
    """
    Standalone function using service: filter approach.
    Compatible with your existing working code pattern.
    """
    try:
        if credentials:
            session = boto3.Session(
                aws_access_key_id=credentials.get('access_key'),
                aws_secret_access_key=credentials.get('secret_key'),
                aws_session_token=credentials.get('session_token'),
                region_name=region,
            )
        else:
            # Use default profile like your original code
            session = boto3.Session(profile_name='default')
        
        client = session.client('resource-explorer-2', region_name=region)
        
        # Services that can be searched directly
        services = ['ec2', 'rds', 'lambda', 'ecs', 'eks', 's3', 'iam', 'elb', 'efs', 'elasticache']
        
        service_region_mapping = {}
        
        for service in services:
            try:
                response = client.search(QueryString=f'service:{service}', MaxResults=1000)
                resources = response.get('Resources', [])
                
                regions = set()
                for resource in resources:
                    region = resource.get('Region')
                    if region and region != 'global':  # Filter out global region
                        regions.add(region)
                
                service_region_mapping[service] = sorted(list(regions))
                if regions:
                    print(f"✅ {service}: Found {len(resources)} resources across {len(regions)} regions")
                    print(f"   📍 {service} available in: {sorted(list(regions))}")
                else:
                    print(f"ℹ️  {service}: No regional resources found")
                
            except Exception as e:
                print(f"❌ {service}: Error - {e}")
                service_region_mapping[service] = []
        
        return service_region_mapping
        
    except Exception as e:
        print(f"❌ Failed to initialize Resource Explorer: {e}")
        return {}


if __name__ == "__main__":
    print("=== SERVICE-BASED RESOURCE EXPLORER ===")
    
    # Test the standalone function
    service_mapping = get_service_regions_by_service()
    
    print("\n📊 Service-Region Availability Summary:")
    services_with_resources = 0
    all_discovered_regions = set()
    
    for service, regions in service_mapping.items():
        if regions:
            services_with_resources += 1
            all_discovered_regions.update(regions)
            print(f"  🔹 {service.upper()} → Available in: {regions}")
        else:
            print(f"  🔸 {service.upper()} → No regions found")
    
    print(f"\n🎯 Summary: {services_with_resources}/{len(service_mapping)} services have discoverable resources")
    print(f"🌍 Total regions with resources: {sorted(list(all_discovered_regions))}")
    print(f"💡 Potential optimization: Skip ~{13 - len(all_discovered_regions)} empty regions per service")