import random
import string
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from app import app
from app.common import (
    get_utc_timestamp, get_encrypted_password,
    OTPExpiredException, OTPNotFoundException, OTPAlreadyVerifiedException, OTPResendCooldownException
)

__all__ = ['OTPService']

logger = logging.getLogger(__name__)


class OTPService:
    """Service for handling OTP generation, validation, and management"""
    
    def __init__(self):
        self.conn_pool = app.state.connection_pool
        self.otp_length = app.config.OTP_LENGTH
        self.expiry_minutes = app.config.OTP_EXPIRY_MINUTES
        self.resend_cooldown_minutes = app.config.OTP_RESEND_COOLDOWN_MINUTES
    
    def generate_otp(self) -> str:
        """Generate a random OTP code"""
        return ''.join(random.choices(string.digits, k=self.otp_length))
    
    async def create_and_store_otp(self, email: str, signup_data: Dict[str, Any]) -> str:
        """
        Create and store OTP for email verification
        
        Args:
            email: User's email address
            signup_data: Dictionary containing signup information (first_name, last_name, password, workspace_name)
            
        Returns:
            str: Generated OTP code
        """
        # Check if there's a recent OTP request (cooldown period)
        await self._check_resend_cooldown(email)
        
        # Generate OTP
        otp_code = self.generate_otp()
        
        # Calculate expiry time
        expires_at = datetime.utcnow() + timedelta(minutes=self.expiry_minutes)
        
        # Prepare signup data with encrypted password
        processed_signup_data = signup_data.copy()
        if 'password' in processed_signup_data:
            processed_signup_data['password_hash'] = get_encrypted_password(processed_signup_data['password'])
            del processed_signup_data['password']  # Remove plain password
        
        # Store OTP in database
        from app.core.models.mysql import store_otp
        await store_otp(
            self.conn_pool,
            email=email,
            otp_code=otp_code,
            expires_at=expires_at,
            signup_data=json.dumps(processed_signup_data)
        )
        
        logger.info(f"OTP created for email: {email}")
        return otp_code
    
    async def verify_otp(self, email: str, otp_code: str) -> Dict[str, Any]:
        """
        Verify OTP code and return signup data if valid
        
        Args:
            email: User's email address
            otp_code: OTP code to verify
            
        Returns:
            Dict containing signup data if OTP is valid
            
        Raises:
            OTPNotFoundException: If OTP is not found
            OTPExpiredException: If OTP has expired
            OTPAlreadyVerifiedException: If OTP is already verified
        """
        from app.core.models.mysql import get_otp, verify_otp
        
        # Get OTP record
        otp_record = await get_otp(self.conn_pool, email=email, otp_code=otp_code)
        
        if not otp_record:
            logger.warning(f"OTP verification failed - not found: {email}")
            raise OTPNotFoundException("Invalid or expired OTP code")
        
        if otp_record['is_verified']:
            logger.warning(f"OTP already verified: {email}")
            raise OTPAlreadyVerifiedException("OTP code has already been used")
        
        # Check if OTP has expired
        if otp_record['expires_at'] < datetime.utcnow():
            logger.warning(f"OTP expired: {email}")
            raise OTPExpiredException("OTP code has expired")
        
        # Mark OTP as verified
        await verify_otp(self.conn_pool, email=email, otp_code=otp_code)
        
        # Parse and return signup data
        signup_data = json.loads(otp_record['signup_data']) if otp_record['signup_data'] else {}
        
        logger.info(f"OTP verified successfully for email: {email}")
        return signup_data
    
    async def resend_otp(self, email: str) -> str:
        """
        Resend OTP for the given email
        
        Args:
            email: User's email address
            
        Returns:
            str: New OTP code
            
        Raises:
            OTPResendCooldownException: If trying to resend too quickly
            OTPNotFoundException: If no pending OTP found
        """
        from app.core.models.mysql import get_otp_by_email
        
        # Check cooldown period
        await self._check_resend_cooldown(email)
        
        # Get existing OTP record to retrieve signup data
        existing_otp = await get_otp_by_email(self.conn_pool, email=email)
        
        if not existing_otp:
            logger.warning(f"No pending OTP found for resend: {email}")
            raise OTPNotFoundException("No pending verification found for this email")
        
        # Parse existing signup data
        signup_data = json.loads(existing_otp['signup_data']) if existing_otp['signup_data'] else {}
        
        # Create new OTP (this will replace the existing one)
        new_otp_code = await self.create_and_store_otp(email, signup_data)
        
        logger.info(f"OTP resent for email: {email}")
        return new_otp_code
    
    async def cleanup_expired_otps(self) -> int:
        """
        Clean up expired and verified OTP records
        
        Returns:
            int: Number of records cleaned up
        """
        from app.core.models.mysql import cleanup_expired_otps
        
        try:
            result = await cleanup_expired_otps(self.conn_pool)
            count = result.get('affected_rows', 0) if result else 0
            logger.info(f"Cleaned up {count} expired OTP records")
            return count
        except Exception as e:
            logger.error(f"Failed to cleanup expired OTPs: {str(e)}")
            return 0
    
    async def _check_resend_cooldown(self, email: str) -> None:
        """
        Check if the user is trying to resend OTP too quickly
        
        Args:
            email: User's email address
            
        Raises:
            OTPResendCooldownException: If trying to resend too quickly
        """
        from app.core.models.mysql import get_otp_by_email
        
        # Get the most recent OTP for this email
        recent_otp = await get_otp_by_email(self.conn_pool, email=email)
        
        if recent_otp:
            # Check if the OTP was created within the cooldown period
            cooldown_threshold = datetime.utcnow() - timedelta(minutes=self.resend_cooldown_minutes)
            
            if recent_otp['created_at'] > cooldown_threshold:
                remaining_seconds = int((recent_otp['created_at'] + timedelta(minutes=self.resend_cooldown_minutes) - datetime.utcnow()).total_seconds())
                logger.warning(f"OTP resend attempted too quickly for email: {email}")
                raise OTPResendCooldownException(f"Please wait {remaining_seconds} seconds before requesting a new code")
    
    async def get_otp_status(self, email: str) -> Optional[Dict[str, Any]]:
        """
        Get OTP status for debugging/monitoring purposes
        
        Args:
            email: User's email address
            
        Returns:
            Dict with OTP status information or None if no OTP found
        """
        from app.core.models.mysql import get_otp_by_email
        
        otp_record = await get_otp_by_email(self.conn_pool, email=email)
        
        if not otp_record:
            return None
        
        return {
            'email': otp_record['email'],
            'created_at': otp_record['created_at'],
            'expires_at': otp_record['expires_at'],
            'is_verified': otp_record['is_verified'],
            'is_expired': otp_record['expires_at'] < datetime.utcnow(),
            'time_remaining': max(0, int((otp_record['expires_at'] - datetime.utcnow()).total_seconds()))
        }
