from fastapi import WebSocket


__all__ = ['WebSocketManager']


class WebSocketManager:
    def __init__(self):
        self.active_connections = {}

    async def connect(self, websocket: WebSocket, account_id: int):
        await websocket.accept()
        self.active_connections[account_id] = websocket

    async def disconnect(self, account_id: int):
        self.active_connections.pop(account_id, None)

    async def send_message(self, message: dict):
        if message['account_id'] in self.active_connections:
            await self.active_connections[message['account_id']].send_json(message)
