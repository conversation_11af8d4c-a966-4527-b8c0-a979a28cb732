from app import app
from ..models.mysql import (get_user_data, add_new_user, fetch_permissions, delete_user, add_user_role,
                            get_workspace_name, get_team_members, get_accounts_by_workspace, add_user_account,
                            get_user_by_id, get_roles_of_user, remove_user_role, get_custom_roles_of_user,
                            update_user_password, update_user_info, get_workspace_owner, transfer_workspace_ownership,
                            update_new_owner_admin_status)
from app.common import (UserExistsException, get_encrypted_password, get_utc_timestamp, BadRequestException,
                        ResourceNotFoundException, AdminUserNotDeletedException, CurrentPasswordNotValidException,
                        AdminUserPasswordNotChangedException, AdminUserInfoNotUpdatedException, CannotUpdateYourselfException,
                        NotWorkspaceOwnerException, CannotTransferToSelfException, TargetUserNotInWorkspaceException,
                        TargetUserNotAdminException, PredefinedUserRoleEnum)
from .roles import GetRolesService

__all__ = ["CreateUserService", "DeleteUserService", "GetPermissionsService", "GetUserInfoService",
           "ListTeamMembersService", "UpdateTeamMemberService", "ChangePasswordService", "UpdateUserInfoService",
           "UpdatePasswordService", "TransferWorkspaceOwnershipService"]


class CreateUserService:
    def __init__(self, message, user):
        self.message = message
        self.creator = user
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # add user to users table
        user = await get_user_data(self.conn_pool, self.message)
        if user and user.get("email") and user["email"].lower() == self.message.email.lower():
            raise UserExistsException

        password_hash = get_encrypted_password(self.message.password)
        user_id = await add_new_user(
            self.conn_pool, 
            self.message.email, 
            password_hash, 
            self.creator["workspace_id"],
            get_utc_timestamp(), 
            self.creator["user_id"],
            self.message.first_name,
            self.message.last_name
        )

        # Validate role assignment - ONLY users with OWNER role can assign Owner role
        if (not self.message.is_custom_role and
            self.message.role_id == PredefinedUserRoleEnum.OWNER.value):
            # Check if current user has OWNER role
            user_roles = await get_roles_of_user(self.conn_pool, self.creator["user_id"])
            has_owner_role = any(role["role_id"] == PredefinedUserRoleEnum.OWNER.value for role in user_roles) if user_roles else False
            
            if not has_owner_role:
                raise NotWorkspaceOwnerException

        # map user with role
        await add_user_role(self.conn_pool, user_id, self.message.role_id, self.message.is_custom_role)
        
        # Sync user with all accounts in the workspace
        await self._sync_user_accounts(user_id)
        
    async def _sync_user_accounts(self, user_id):
        """
        Sync the newly created user with all accounts in the workspace.
        This ensures the user has access to all accounts in their workspace.
        """
        # Get all accounts in the workspace
        workspace_accounts = await get_accounts_by_workspace(self.conn_pool, self.creator["workspace_id"])
        
        # Add user to each account
        for account in workspace_accounts:
            await add_user_account(self.conn_pool, user_id, account["id"])


class DeleteUserService:
    def __init__(self, user_to_delete, user):
        self.user = user
        self.user_to_delete = user_to_delete
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Prevent users from deleting themselves
        if self.user["user_id"] == self.user_to_delete:
            raise BadRequestException

        # Get user info to verify workspace
        user_info = await get_user_by_id(self.conn_pool, self.user_to_delete)

        # Check if user exists and belongs to the same workspace
        if not user_info or user_info['workspace_id'] != self.user['workspace_id']:
            raise ResourceNotFoundException
        elif user_info['created_by'] == self.user_to_delete:
            raise AdminUserNotDeletedException

        # Delete the user
        await delete_user(self.conn_pool, self.user_to_delete)


class GetPermissionsService:
    def __init__(self):
        self.conn_pool = app.state.connection_pool

    async def process(self):
        return await fetch_permissions(self.conn_pool)


class GetUserInfoService:
    def __init__(self, user):
        self.user = user
        self.conn_pool = app.state.connection_pool

    async def process(self):
        workspace_name = await get_workspace_name(self.conn_pool, self.user["workspace_id"])
        data = await GetRolesService(self.user, self.user["user_id"]).process()
        user_info = await get_user_by_id(self.conn_pool, self.user["user_id"])

        # Check if user has OWNER role (same logic as team members API)
        user_roles = data.get("roles", [])
        is_owner = any(role["id"] == PredefinedUserRoleEnum.OWNER.value
                      for role in user_roles) if user_roles else False

        return {
            "user_id": self.user["user_id"],
            "email": self.user["email"],
            "first_name": user_info.get("first_name"),
            "last_name": user_info.get("last_name"),
            "workspace_id": self.user["workspace_id"],
            "workspace_name": workspace_name,
            "is_owner": is_owner,
            "roles": data["roles"],
            "custom_roles": data["custom_roles"]
        }


class ListTeamMembersService:
    def __init__(self, user):
        self.user = user
        self.workspace_id = user["workspace_id"]
        self.conn_pool = app.state.connection_pool

    async def process(self):
        team_members = await get_team_members(self.conn_pool, self.workspace_id)

        # Enhanced team members with roles information
        enhanced_members = []
        for member in team_members:
            if self.user["user_id"] == member["id"]:
                continue

            # Get user's roles
            user_roles = await get_roles_of_user(self.conn_pool, member["id"])
            user_custom_roles = await get_custom_roles_of_user(self.conn_pool, member["id"])

            # Check if user has OWNER role
            is_owner = any(role["role_id"] == PredefinedUserRoleEnum.OWNER.value
                          for role in user_roles) if user_roles else False

            # Determine role information - prioritize OWNER role
            role_name = None
            is_custom_role = False

            # Check if user has OWNER role first
            owner_role = None
            if user_roles:
                owner_role = next((role for role in user_roles
                                 if role["role_id"] == PredefinedUserRoleEnum.OWNER.value), None)

            if owner_role:
                role_name = owner_role["role_name"]
                is_custom_role = False
            elif user_custom_roles and len(user_custom_roles) > 0:
                role_name = user_custom_roles[0]["role_name"]
                is_custom_role = True
            elif user_roles and len(user_roles) > 0:
                role_name = user_roles[0]["role_name"]
                is_custom_role = False

            member_info = {
                "id": member["id"],
                "email": member["email"],
                "first_name": member.get("first_name"),
                "last_name": member.get("last_name"),
                "is_owner": is_owner,
                "role_name": role_name,
                "is_custom_role": is_custom_role,
                "last_login": member.get("last_login"),
                "created_at": member["created_at"],
            }
            enhanced_members.append(member_info)

        return enhanced_members


class UpdateTeamMemberService:
    def __init__(self, message, user):
        self.message = message
        self.updater = user
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Get user info to verify workspace
        user_to_update = await get_user_by_id(self.conn_pool, self.message.user_id)

        # Check if user exists and belongs to the same workspace
        if not user_to_update or user_to_update['workspace_id'] != self.updater['workspace_id']:
            raise ResourceNotFoundException

        # Prevent users from updating themselves
        elif self.updater["user_id"] == self.message.user_id:
            raise CannotUpdateYourselfException

        # Only block updates if someone ELSE is trying to change the current workspace owner's role
        # Current owner should be able to change their own role (for ownership transfer)
        # But allow ownership transfers (changing someone else to OWNER)
        if self.message.role_id is not None:
            # If we're assigning OWNER role, allow it (ownership transfer)
            if (not self.message.is_custom_role and 
                self.message.role_id == PredefinedUserRoleEnum.OWNER.value):
                # Allow ownership transfer - don't block
                pass
            else:
                # For non-ownership transfers, block if someone ELSE is trying to change the current workspace owner's role
                workspace_owner = await get_workspace_owner(self.conn_pool, self.updater["workspace_id"])
                if (workspace_owner and 
                    workspace_owner["id"] == self.message.user_id and 
                    self.updater["user_id"] != self.message.user_id):
                    raise AdminUserInfoNotUpdatedException

        # Update user info if provided
        updates = {}
        if self.message.first_name is not None:
            updates["first_name"] = self.message.first_name
            
        if self.message.last_name is not None:
            updates["last_name"] = self.message.last_name
            
        if updates:
            await update_user_info(self.conn_pool, self.message.user_id, updates)

        # Update role if provided
        if self.message.role_id is not None:
            # Handle ownership transfer when assigning OWNER role
            if (not self.message.is_custom_role and
                self.message.role_id == PredefinedUserRoleEnum.OWNER.value):
                await self._handle_ownership_transfer()
            else:
                # For non-ownership transfers, just update the role normally
                # First remove existing roles
                existing_roles = await get_roles_of_user(self.conn_pool, self.message.user_id)
                for role in existing_roles:
                    await remove_user_role(self.conn_pool, self.message.user_id, role["role_id"], False)

                # Also remove any existing custom roles
                existing_custom_roles = await get_custom_roles_of_user(self.conn_pool, self.message.user_id)
                for role in existing_custom_roles:
                    await remove_user_role(self.conn_pool, self.message.user_id, role["role_id"], True)

                # Add new role
                await add_user_role(self.conn_pool, self.message.user_id, self.message.role_id, self.message.is_custom_role)

        return {
            "user_id": self.message.user_id,
            "updated_by": self.updater["user_id"]
        }

    async def _handle_ownership_transfer(self):
        """Handle workspace ownership transfer when assigning OWNER role"""
        # Validate that current user has the OWNER role
        user_roles = await get_roles_of_user(self.conn_pool, self.updater["user_id"])
        has_owner_role = any(role["role_id"] == PredefinedUserRoleEnum.OWNER.value for role in user_roles) if user_roles else False
        
        if not has_owner_role:
            raise NotWorkspaceOwnerException

        # Cannot transfer to yourself
        if self.message.user_id == self.updater["user_id"]:
            raise CannotTransferToSelfException

        # Target user must have Admin role (role_id = 1)
        user_roles = await get_roles_of_user(self.conn_pool, self.message.user_id)
        has_admin_role = any(role["role_id"] == 1 for role in user_roles) if user_roles else False

        if not has_admin_role:
            raise TargetUserNotAdminException

        # Remove OWNER role from ANY user who currently has it (ensure only one owner)
        # First, find all users with OWNER role in this workspace
        all_users_with_owner_role = await self._get_all_users_with_owner_role()
        
        # Remove OWNER role from all of them
        for user_id in all_users_with_owner_role:
            await remove_user_role(self.conn_pool, user_id,
                                 PredefinedUserRoleEnum.OWNER.value, False)

        # Add Admin role back to previous owner (since they lose Owner role)
        await add_user_role(self.conn_pool, self.updater["user_id"],
                           PredefinedUserRoleEnum.ADMIN.value, False)

        # Remove Admin role from new owner (if they have it) to avoid duplication
        new_owner_roles = await get_roles_of_user(self.conn_pool, self.message.user_id)
        for role in new_owner_roles:
            if role["role_id"] == PredefinedUserRoleEnum.ADMIN.value:
                await remove_user_role(self.conn_pool, self.message.user_id,
                                     PredefinedUserRoleEnum.ADMIN.value, False)
                break

        # Assign OWNER role to new owner
        await add_user_role(self.conn_pool, self.message.user_id,
                           PredefinedUserRoleEnum.OWNER.value, False)

        # Transfer workspace ownership in database
        await transfer_workspace_ownership(
            self.conn_pool,
            self.updater["workspace_id"],
            self.updater["user_id"],
            self.message.user_id
        )

        # Update new owner's admin status (self-reference)
        await update_new_owner_admin_status(self.conn_pool, self.message.user_id)

    async def _get_all_users_with_owner_role(self):
        """Get all user IDs who currently have the OWNER role in this workspace"""
        # This is a simplified approach - in a real implementation, you'd want to query the database
        # to find all users in this workspace who have the OWNER role
        
        # For now, we'll remove OWNER role from the current user making the request
        # and also check if the target user already has it
        users_to_clean = []
        
        # Check if current user has OWNER role
        current_user_roles = await get_roles_of_user(self.conn_pool, self.updater["user_id"])
        if any(role["role_id"] == PredefinedUserRoleEnum.OWNER.value for role in current_user_roles):
            users_to_clean.append(self.updater["user_id"])
        
        # Check if target user already has OWNER role
        target_user_roles = await get_roles_of_user(self.conn_pool, self.message.user_id)
        if any(role["role_id"] == PredefinedUserRoleEnum.OWNER.value for role in target_user_roles):
            users_to_clean.append(self.message.user_id)
        
        return users_to_clean


class ChangePasswordService:
    def __init__(self, message, user):
        self.message = message
        self.updater = user
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Get user info to verify workspace
        user_to_update = await get_user_by_id(self.conn_pool, self.message.user_id)

        # Check if user exists and belongs to the same workspace
        if not user_to_update or user_to_update['workspace_id'] != self.updater['workspace_id']:
            raise ResourceNotFoundException

        # Prevent admin users from being updated by others
        if user_to_update['created_by'] == self.message.user_id and self.updater["user_id"] != self.message.user_id:
            raise AdminUserPasswordNotChangedException

        # Update the password
        password_hash = get_encrypted_password(self.message.password)
        await update_user_password(self.conn_pool, self.message.user_id, password_hash)

        return {
            "user_id": self.message.user_id,
            "updated_by": self.updater["user_id"]
        }


class UpdateUserInfoService:
    def __init__(self, message, user):
        self.message = message
        self.user = user
        self.conn_pool = app.state.connection_pool

    async def process(self):
        updates = {}

        # Check which fields need to be updated
        if self.message.first_name is not None:
            updates["first_name"] = self.message.first_name

        if self.message.last_name is not None:
            updates["last_name"] = self.message.last_name

        if self.message.email is not None and self.message.email != self.user["email"]:
            # Check if email already exists for a different user
            existing_user = await get_user_data(self.conn_pool, self.message)
            if existing_user and existing_user.get('id') != self.user["user_id"]:
                raise UserExistsException
            updates["email"] = self.message.email

        # If there are updates to make
        if updates:
            await update_user_info(self.conn_pool, self.user["user_id"], updates)

        return {
            "user_id": self.user["user_id"],
            "updated": bool(updates)
        }


class UpdatePasswordService:
    def __init__(self, message, user):
        self.message = message
        self.user = user
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Get the user's current password hash
        user_info = await get_user_by_id(self.conn_pool, self.user["user_id"])
        
        if not user_info:
            raise ResourceNotFoundException
            
        # Verify the current password
        current_password_hash = get_encrypted_password(self.message.current_password)
        if current_password_hash != user_info.get("password_hash"):
            raise CurrentPasswordNotValidException
            
        # Update to the new password
        new_password_hash = get_encrypted_password(self.message.new_password)
        await update_user_password(self.conn_pool, self.user["user_id"], new_password_hash)
            
        return {
            "user_id": self.user["user_id"],
            "updated": True
        }


class TransferWorkspaceOwnershipService:
    def __init__(self, message, user):
        self.message = message
        self.current_user = user
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Validate that current user is the workspace owner
        await self._validate_current_user_is_owner()

        # Validate target user
        await self._validate_target_user()

        # Perform the ownership transfer
        await self._transfer_ownership()

        return {
            "workspace_id": self.current_user["workspace_id"],
            "previous_owner_id": self.current_user["user_id"],
            "new_owner_id": self.message.new_owner_user_id,
            "transferred_at": get_utc_timestamp()
        }

    async def _validate_current_user_is_owner(self):
        """Validate that the current user is the workspace owner"""
        workspace_owner = await get_workspace_owner(self.conn_pool, self.current_user["workspace_id"])

        if not workspace_owner or workspace_owner["id"] != self.current_user["user_id"]:
            raise NotWorkspaceOwnerException

    async def _validate_target_user(self):
        """Validate the target user for ownership transfer"""
        # Cannot transfer to yourself
        if self.message.new_owner_user_id == self.current_user["user_id"]:
            raise CannotTransferToSelfException

        # Get target user info
        target_user = await get_user_by_id(self.conn_pool, self.message.new_owner_user_id)

        # Target user must exist and be in the same workspace
        if not target_user or target_user["workspace_id"] != self.current_user["workspace_id"]:
            raise TargetUserNotInWorkspaceException

        # Target user must have Admin role (role_id = 1)
        user_roles = await get_roles_of_user(self.conn_pool, self.message.new_owner_user_id)
        has_admin_role = any(role["role_id"] == 1 for role in user_roles) if user_roles else False

        if not has_admin_role:
            raise TargetUserNotAdminException

    async def _transfer_ownership(self):
        """Perform the actual ownership transfer"""
        # Remove OWNER role from current owner (if they have it)
        current_owner_roles = await get_roles_of_user(self.conn_pool, self.current_user["user_id"])
        for role in current_owner_roles:
            if role["role_id"] == PredefinedUserRoleEnum.OWNER.value:
                await remove_user_role(self.conn_pool, self.current_user["user_id"],
                                     PredefinedUserRoleEnum.OWNER.value, False)
                break

        # Add Admin role back to previous owner (since they lose Owner role)
        await add_user_role(self.conn_pool, self.current_user["user_id"],
                           PredefinedUserRoleEnum.ADMIN.value, False)

        # Remove Admin role from new owner (if they have it) to avoid duplication
        new_owner_roles = await get_roles_of_user(self.conn_pool, self.message.new_owner_user_id)
        for role in new_owner_roles:
            if role["role_id"] == PredefinedUserRoleEnum.ADMIN.value:
                await remove_user_role(self.conn_pool, self.message.new_owner_user_id,
                                     PredefinedUserRoleEnum.ADMIN.value, False)
                break

        # Assign OWNER role to new owner
        await add_user_role(self.conn_pool, self.message.new_owner_user_id,
                           PredefinedUserRoleEnum.OWNER.value, False)

        # Transfer workspace ownership
        await transfer_workspace_ownership(
            self.conn_pool,
            self.current_user["workspace_id"],
            self.current_user["user_id"],
            self.message.new_owner_user_id
        )

        # Update new owner's admin status (self-reference)
        await update_new_owner_admin_status(self.conn_pool, self.message.new_owner_user_id)
