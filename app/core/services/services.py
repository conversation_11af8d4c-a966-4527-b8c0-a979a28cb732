from app import app
from ..models.mysql import get_services


__all__ = ['CloudProviderServiceProcessor']


class CloudProviderServiceProcessor:
    def __init__(self, cloud_provider_id):
        self.cloud_provider_id = cloud_provider_id
        self.conn_pool = app.state.connection_pool

    async def process(self):
        services = await get_services(self.conn_pool, self.cloud_provider_id)

        # Normalize service display names to AWS official naming conventions
        aws_display_name_map = {
            "lambda": "Lambda",
            "ecs": "ECS",
            "eks": "EKS",
            "elasticache": "ElastiCache",
            "elb": "ELB",
            "efs": "EFS",
            "rds": "Aurora and RDS",
            "ec2": "EC2",
            "s3": "S3",
            "iam": "IAM",
        }

        normalized = []
        for service in services:
            name = service.get("name")
            display = aws_display_name_map.get(name.lower(), name)
            normalized.append({
                "id": service.get("id"),
                "name": display,
            })

        return normalized
