import os
from dotenv import load_dotenv
from celery import Celery
from app.config import Configurations
from app import app


__all__ = ["celery_obj"]

# Load .env file if it exists (for local development)
dotenv_path = os.path.join(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))), ".env"
)
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path)

app.config = Configurations()


def get_celery():
    celery_app = Celery("cloud-audit", broker=app.config.CELERY_BROKER_URL, backend=app.config.CELERY_RESULT_BACKEND)
    
    # Include beat schedule for periodic tasks
    from app.core.services.celery_conf.beat_schedule import beat_schedule, timezone
    celery_app.conf.beat_schedule = beat_schedule
    celery_app.conf.timezone = timezone
    
    return celery_app

celery_obj = get_celery()
