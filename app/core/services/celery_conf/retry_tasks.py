"""
Retry Tasks for DLQ Processing
Handles retry logic for failed scan operations
"""

import asyncio
import importlib
from celery.utils.log import get_task_logger

from app.core.models.mysql import mysql_connection_pool_factory
from app.core.models.mysql.scans import (
    get_failed_scan_tasks, 
    tracking_set_status,
    get_scan_details,
    get_service_details,
    get_account_credentials,
)
from app.common.enums import ScanServiceStatusEnum, ScanStatusEnum
from app.core.services.celery_conf.celery import celery_obj

logger = get_task_logger(__name__)


@celery_obj.task
def retry_failed_data_fetch(scan_id, service_id, max_retries=3):
    """
    Retry failed data fetch operations for a specific service
    """
    import asyncio
    loop = asyncio.get_event_loop()
    
    async def run_retry():
        mysql_pool = await mysql_connection_pool_factory()
        
        try:
            # Get scan details
            scan_details = await get_scan_details(mysql_pool, scan_id)
            if not scan_details:
                logger.error(f"Scan {scan_id} not found")
                return
            
            # Get service details
            service_details = await get_service_details(mysql_pool, service_id)
            if not service_details:
                logger.error(f"Service {service_id} not found")
                return
            
            # Get account credentials
            credentials = await get_account_credentials(mysql_pool, scan_details["account_id"])
            
            logger.info(f"Retrying data fetch for scan {scan_id}, service {service_id}")
            
            # Try data fetch again
            try:
                fetch_mod = importlib.import_module(
                    f"app.core.services.api_cloud_providers.{scan_details['cloud_provider_name']}.{service_details['name']}.data_fetch"
                )
                # Pull regions from any failed task row (they all carry same regions per service)
                failed_tasks_snapshot = await get_failed_scan_tasks(mysql_pool, scan_id, service_id)
                regions = failed_tasks_snapshot[0]["regions"] if (failed_tasks_snapshot and failed_tasks_snapshot[0].get("regions")) else []
                session_factory = fetch_mod.prepare_session_factory(credentials, regions)
                # Clean existing cache for this workspace/account/service before retrying data fetch
                try:
                    if credentials.get("workspace_id") and credentials.get("aws_account_id"):
                        fetch_mod.clear_cache(credentials.get("workspace_id"), credentials.get("aws_account_id"))
                except Exception as clear_err:
                    logger.warning("Cache clear failed during retry for service %s: %s", service_id, clear_err)
                await fetch_mod.fetch_and_cache_all_regions(
                    regions=regions,
                    session_factory=session_factory,
                    account_id=str(scan_details["account_id"]),
                    credentials=credentials
                )
                
                logger.info(f"Data fetch retry successful for scan {scan_id}, service {service_id}")
                
                # Mark all failed checks as pending for retry
                from app.core.models.mysql.scans import get_check_detail
                checks = await get_check_detail(mysql_pool, service_id)
                
                for check in checks:
                    await tracking_set_status(
                        mysql_pool, 
                        scan_id, 
                        service_id, 
                        check["id"], 
                        "pending", 
                        error_message=None,
                        inc_retry=False,
                        mark_dlq=False
                    )
                
                # Update service status to running
                from app.core.models.mysql.scans import update_scan_service
                await update_scan_service(
                    mysql_pool, 
                    scan_details["scan_service_id"], 
                    status=ScanServiceStatusEnum.RUNNING.value
                )
                
                # Trigger check execution
                from app.core.services.celery_conf.tasks import execute_check_task
                for check in checks:
                    module_path = (
                        f"app.core.services.api_cloud_providers.{scan_details['cloud_provider_name']}."
                        f"{service_details['name']}.checks.{check['script_path']}"
                    )
                    execute_check_task.delay(
                        scan_id,
                        service_id,
                        check["id"],
                        module_path,
                        {
                            "account_id": scan_details["account_id"],
                            "regions": regions,
                            "cloud_provider_name": scan_details["cloud_provider_name"]
                        },
                        credentials
                    )
                
            except Exception as fetch_err:
                logger.exception(f"Data fetch retry failed for scan {scan_id}, service {service_id}: {fetch_err}")
                
                # Get current retry count
                failed_tasks = await get_failed_scan_tasks(mysql_pool, scan_id, service_id)
                if failed_tasks:
                    retry_count = failed_tasks[0]["retry_count"]
                    
                    if retry_count < max_retries:
                        # Increment retry count and schedule another retry
                        for task in failed_tasks:
                            await tracking_set_status(
                                mysql_pool,
                                scan_id,
                                service_id,
                                task["check_id"],
                                "failed",
                                error_message=f"Data fetch retry {retry_count + 1} failed: {str(fetch_err)}",
                                inc_retry=True,
                                mark_dlq=False
                            )
                        
                        # Schedule next retry with exponential backoff
                        retry_delay = 2 ** retry_count * 60  # 1min, 2min, 4min, 8min
                        retry_failed_data_fetch.apply_async(
                            args=[scan_id, service_id, max_retries],
                            countdown=retry_delay
                        )
                        logger.info(f"Scheduled retry {retry_count + 1} for scan {scan_id}, service {service_id} in {retry_delay} seconds")
                    else:
                        # Max retries exceeded, move to DLQ
                        logger.error(f"Max retries exceeded for scan {scan_id}, service {service_id}, moving to DLQ")
                        for task in failed_tasks:
                            await tracking_set_status(
                                mysql_pool,
                                scan_id,
                                service_id,
                                task["check_id"],
                                "dlq",
                                error_message=f"Max retries exceeded: {str(fetch_err)}",
                                inc_retry=False,
                                mark_dlq=True
                            )
                        
                        # Update service status to completed (since failed might not be in ENUM)
                        from app.core.models.mysql.scans import update_scan_service
                        await update_scan_service(
                            mysql_pool, 
                            scan_details["scan_service_id"], 
                            status=ScanServiceStatusEnum.COMPLETED.value
                        )
        
        except Exception as e:
            logger.exception(f"Error in retry_failed_data_fetch: {e}")
    
    return loop.run_until_complete(run_retry())


@celery_obj.task
def process_dlq_entries():
    """
    Process entries in DLQ status and attempt retry
    """
    import asyncio
    loop = asyncio.get_event_loop()
    
    async def run_dlq_processing():
        mysql_pool = await mysql_connection_pool_factory()
        
        try:
            # Get all DLQ entries
            dlq_entries = await get_failed_scan_tasks(mysql_pool, status="dlq")
            
            if not dlq_entries:
                logger.info("No DLQ entries found")
                return
            
            logger.info(f"Processing {len(dlq_entries)} DLQ entries")
            
            # Group by scan_id and service_id for batch processing
            scan_service_groups = {}
            for entry in dlq_entries:
                key = (entry["scan_id"], entry["service_id"])
                if key not in scan_service_groups:
                    scan_service_groups[key] = []
                scan_service_groups[key].append(entry)
            
            # Process each group
            for (scan_id, service_id), entries in scan_service_groups.items():
                logger.info(f"Processing DLQ group: scan {scan_id}, service {service_id}")
                
                # Check if we should retry this group
                retry_count = entries[0]["retry_count"]
                max_retries = entries[0]["max_retries"]
                
                if retry_count < max_retries:
                    # Move back to failed status and retry
                    for entry in entries:
                        await tracking_set_status(
                            mysql_pool,
                            scan_id,
                            service_id,
                            entry["check_id"],
                            "failed",
                            error_message=entry["error_message"],
                            inc_retry=False,
                            mark_dlq=False
                        )
                    
                    # Schedule retry
                    retry_failed_data_fetch.apply_async(
                        args=[scan_id, service_id, max_retries],
                        countdown=60  # 1 minute delay
                    )
                    logger.info(f"Scheduled DLQ retry for scan {scan_id}, service {service_id}")
                else:
                    logger.warning(f"DLQ entry max retries exceeded for scan {scan_id}, service {service_id}")
        
        except Exception as e:
            logger.exception(f"Error in process_dlq_entries: {e}")
    
    return loop.run_until_complete(run_dlq_processing())


@celery_obj.task
def periodic_retry_scheduler():
    """
    Periodic task to check for failed entries and schedule retries
    """
    import asyncio
    loop = asyncio.get_event_loop()
    
    async def run_scheduler():
        mysql_pool = await mysql_connection_pool_factory()
        
        try:
            # Get failed entries that haven't been retried recently
            failed_entries = await get_failed_scan_tasks(mysql_pool, status="failed")
            
            if not failed_entries:
                logger.info("No failed entries found for retry")
                return
            
            logger.info(f"Found {len(failed_entries)} failed entries for retry")
            
            # Group by scan_id and service_id
            scan_service_groups = {}
            for entry in failed_entries:
                key = (entry["scan_id"], entry["service_id"])
                if key not in scan_service_groups:
                    scan_service_groups[key] = []
                scan_service_groups[key].append(entry)
            
            # Schedule retries for each group
            for (scan_id, service_id), entries in scan_service_groups.items():
                retry_count = entries[0]["retry_count"]
                max_retries = entries[0]["max_retries"]
                
                if retry_count < max_retries:
                    # Schedule retry with exponential backoff
                    retry_delay = 2 ** retry_count * 60  # 1min, 2min, 4min, 8min
                    retry_failed_data_fetch.apply_async(
                        args=[scan_id, service_id, max_retries],
                        countdown=retry_delay
                    )
                    logger.info(f"Scheduled retry for scan {scan_id}, service {service_id} in {retry_delay} seconds")
        
        except Exception as e:
            logger.exception(f"Error in periodic_retry_scheduler: {e}")
    
    return loop.run_until_complete(run_scheduler())
