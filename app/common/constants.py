""" APIs """

SIGNUP_API = "/signup"
LOGIN_API = "/login"
LOGOUT_API = "/logout"
REFRESH_TOKEN_API = "/refresh-token"
VERIFY_OTP_API = "/verify-otp"
RESEND_OTP_API = "/resend-otp"
GET_CLOUD_PROVIDERS_API = "/cloud-providers"
ACCOUNTS_API = "/accounts"
DELETE_ACCOUNT_API = "/accounts/{account_id}"
GET_ACCOUNT_DETAIL_API = "/accounts/{account_id}"
USERS_API = "/admin/users"
GET_USER_INFO_API = "/user-info"
UPDATE_USER_INFO_API = "/update-user-info"
LIST_TEAM_MEMBERS_API = "/team-members"
UPDATE_TEAM_MEMBER_API='/team-member'
GET_SERVICES_API = "/services"
CREATE_SCAN_API = "/scans"
GET_PERMISSIONS_API = "/user-permissions"
CUSTOM_ROLES_API = "/custom-roles"
ROLES_API = "/roles"
ASSIGN_ROLE_API = "/assign-role"
REVOKE_ROLE_API = "/revoke-role"
GET_SCANS_API = "/scans"
GET_SCAN_DETAIL_API = "/scans/{scan_id}"
GET_REGIONS_API = "/regions"
GET_FINDINGS_API = "/findings"
GET_FINDING_DETAIL_API = "/findings/{finding_id}"
REMEDIATE_FINDING_API = "/findings/{finding_id}/remediate"
CHANGE_PASSWORD_API = "/change-password"
UPDATE_PASSWORD_API = "/update-password"
TRANSFER_WORKSPACE_OWNERSHIP_API = "/admin/transfer-workspace-ownership"


""" Third Party API List """
APP_READINESS_API = "/readiness"
APP_LIVENESS_API = "/liveness"
RABBITMQ_CONSUMER_HEALTH_TEST_API = "/consumer-health-test"
RABBITMQ_QUEUES_DETAILS_API = "{rabbitmq_host}/api/queues/{virtual_host}/{queue}"


""" Date Format """
DT_FMT_dmy = "%d/%m/%y"  # 31/12/17
DT_FMT_bdYIMp = "%b %d %Y %I:%M %p"  # Jul 16 2017 08:46 PM
DT_FMT_ymdHMSf = "%Y-%m-%d %H:%M:%S.%f"  # 2017-07-19 06:58:20.370
DT_FMT_ymdHMSfz = "%Y-%m-%d %H:%M:%S.%f%z"  # 2017-07-19 06:58:20.370+00:00
DT_FMT_ymdHMS = "%Y-%m-%d %H:%M:%S"  # 2017-07-19 06:58:20
DT_FMT_Ymd = "%Y-%m-%d"  # 2017-09-11
DT_FMT_YMD = "%Y/%m/%d"
DT_FMT_ymdHM = "%Y-%m-%d %H:%M"
DT_FMT_dbYHMS = "%d-%b-%Y %H:%M:%S"
DT_FMT_dbYHMSf = "%d-%b-%Y %H:%M:%S.%f"
DT_FMT_HM = "%H:%M"
DT_FMT_ymdTHMSf = "%Y-%m-%dT%H:%M:%S.%f"
YYYY_MM_DD_HH_MM_SS = "%Y-%m-%d %H:%M:%S"


""" Request Headers """
CORRELATION_ID = "CORRELATION_ID"


""" Ignore Liveness and Readiness APIs """
IGNORE_PATH_LOG = ["/readiness", "/liveness"]


""" Utility Constants """
MIN_PASSWORD_LENGTH = 8
EMAIL_REGEX = r"(^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$)"
AUTH_TOKEN_TYPE = "Bearer"
PREFETCH_COUNT = "prefetch_count"
STOPPED_EC2_INSTANCE_CLEANUP_TIME = 30  # in days
DEFAULT_RDS_ADMIN_USERNAMES = {"admin", "root", "postgres", "sqladmin", "master", "rdsadmin", "sa"}


# Dictionary of finding detail keys by service
FINDING_DETAIL_KEYS = {
    # EC2 Service
    "ec2": {
        # EBS Encryption
        "volume_id": "Volume ID",
        "encrypted": "Encrypted",
        "status": "Status",
        "region": "Region",

        # IMDSv2
        "instance_id": "Instance ID",
        "imdsv2_required": "IMDSv2 Required",
        "imdsv2_enabled": "IMDSv2 Enabled",

        # Public Access
        "public_ip": "Public IP",
        "public_dns_name": "Public DNS Name",
        "has_public_access": "Has Public Access",

        # VPC Flow Logs
        "vpc_id": "VPC ID",
        "flow_logs_enabled": "Flow Logs Enabled",
        "endpoint_type": "Endpoint Type",
        "vpc_endpoint_id":"VPC Endpoint Id",

        # Security Groups
        "security_group_id": "Security Group ID",
        "security_group_name": "Security Group Name",
        "rule_id": "Rule ID",
        "protocol": "Protocol",
        "port_range": "Port Range",
        "has_violations": "Has Violations",
        "inbound_rules": "Inbound Rules",
        "outbound_rules": "Outbound Rules",

        # Network ACLs
        "nacl_id": "Network ACL ID",
        "rule_number": "Rule Number",
        "port_from": "Port From",
        "port_to": "Port To",
        "associated_subnets_count": "Associated Subnets Count",

        # Launch Templates
        "launch_template_id": "Launch Template ID",
        "launch_template_name": "Launch Template Name",
        "template_version": "Template Version",
        "imdsv2_enforced": "IMDSv2 Enforced",

        # Snapshots
        "snapshot_id": "Snapshot ID",
        "publicly_restorable": "Publicly Restorable",

        # VPC
        "internet_gateway_id": "Internet Gateway ID",
        "exposes_to_internet": "Exposes to Internet",

        # Subnets
        "subnet_id": "Subnet ID",
        "map_public_ip_on_launch": "Map Public IP on Launch",
        "assigns_public_ip": "Assigns Public IP",
        "is_public_subnet": "Is Public Subnet",

        # VPN
        "vpn_connection_id": "VPN Connection ID",
        "vpn_gateway_id": "VPN Gateway ID",
        "customer_gateway_id": "Customer Gateway ID",
        "vpn_id": "VPN ID",
        "logging_enabled": "Logging Enabled",
        "tunnel_status": "Tunnel Status",
        "has_down_tunnel": "Has Down Tunnel",

        # Transit Gateway
        "transit_gateway_id": "Transit Gateway ID",
        "auto_accept_shared_attachments": "Auto Accept Shared Attachments",
        "auto_accept": "Auto Accept",

        # Paravirtual
        "virtualization_type": "Virtualization Type",
        "is_paravirtual": "Is Paravirtual",

        # Stopped Instances
        "stopped_since": "Stopped Since",
        "exceeds_cleanup_time": "Exceeds Cleanup Time",
        "stopped_days": "Stopped Days",

        # Termination Protection
        "termination_protection": "Termination Protection",

        # VPC Endpoints
        "endpoint_exists": "Endpoint Exists",

        # Default Encryption
        "encryption_enabled": "Encryption Enabled",

        # Multiple ENIs
        "network_interfaces_count": "Network Interfaces Count",
        "eni_count": "ENI Count",

        # other
        "service_name":"Service Name",
    },

    # S3 Service
    "s3": {
        "bucket_name": "Bucket Name",
        "access_point_name": "Access Point Name",
        "multi_region_access_point_name": "Multi-Region Access Point Name",
        "public_access_blocked": "Public Access Blocked",
        "has_acls": "Has ACLs",
        "lifecycle_configuration": "Lifecycle Configuration",
        "versioning_enabled": "Versioning Enabled",
        "encrypted": "Encrypted",
        "kms_key_id": "KMS Key ID",
        "target_bucket": "Target Bucket",
        "target_prefix": "Target Prefix",
        "has_secure_transport_policy": "Has Secure Transport Policy",
        "has_public_access_blocks": "Has Public Access Blocks",
        "acls_used": "ACLs Used",
        "public_read_access": "Public Read Access",
        "public_write_access": "Public Write Access"
    },

    # IAM Service
    "iam": {
        "user_name": "User Name",
        "user_id": "User ID",
        "user_arn": "User ARN",
        "has_admin_privileges": "Has Admin Privileges",
        "policy_name": "Policy Name",
        "policy_arn": "Policy ARN",
        "has_wildcard_actions": "Has Wildcard Actions",
        "wildcard_actions": "Wildcard Actions",
        "access_key_id": "Access Key ID",
        "access_key_last_used": "Access Key Last Used",
        "access_key_created": "Access Key Created",
        "age_in_days": "Age in Days",
        "needs_rotation": "Needs Rotation",
        "has_mfa_enabled": "Has MFA Enabled",
        "mfa_enabled": "MFA Enabled",
        "has_hardware_mfa": "Has Hardware MFA",
        "has_console_password": "Has Console Password",
        "has_console_access": "Has Console Access",
        "password_last_used": "Password Last Used",
        "password_last_changed": "Password Last Changed",
        "has_active_access_keys": "Has Active Access Keys",
        "has_active_password": "Has Active Password",
        "has_root_access_key": "Has Root Access Key",
        "has_full_admin_privileges": "Has Full Admin Privileges",
        "is_strong_policy": "Is Strong Policy"
    },

    # RDS Service
    "rds": {
        "db_instance_identifier": "DB Instance Identifier",
        "db_cluster_identifier": "DB Cluster Identifier",
        "master_username": "Master Username",
        "is_default_username": "Is Default Username",
        # "using_default_admin": "Using Default Admin",
        "engine": "Engine",
        "engine_version": "Engine Version",
        "storage_encrypted": "Storage Encrypted",
        # "multi_az": "Multi-AZ",
        # "publicly_accessible": "Publicly Accessible",
        # "auto_minor_version_upgrade": "Auto Minor Version Upgrade",
        # "deletion_protection": "Deletion Protection",
        # "backup_retention_period": "Backup Retention Period",
        "parameter_group_name": "Parameter Group Name",
        "parameter_name": "Parameter Name",
        "parameter_value": "Parameter Value",
        "requires_ssl": "Requires SSL",
        "ssl_required": "SSL Required",
        # "ssl_enforced": "SSL Enforced",
        # "encrypted_in_transit": "Encrypted in Transit",
        # "enhanced_monitoring_enabled": "Enhanced Monitoring Enabled",
        # "using_default_port": "Using Default Port",
        "copy_tags_to_snapshots": "Copy Tags to Snapshots",
        "is_supported": "Is Supported",
        "is_deprecated": "Is Deprecated",
        "db_instance_id": "DB Identifier",
        "is_protected": "Is Protected",
        "min_retention_days": "Min Retention Days",
        # "is_expired": "Is Expired",
        "is_deprecated": "Is Deprecated",
        # "days_until_expiration": "Days Until Expiration",
        "certificate_valid_till": "Certificate Valid Till",
        # "certificate_status": "Certificate Status",
        "ca_certificate_identifier": "CA Certificate Identifier",
        # "has_logging_enabled": "Has Logging Enabled",
        # "enabled_logs": "Enabled Logs",
        "db_cluster_id": "DB Cluster ID",
        # "iam_auth_enabled": "IAM Auth Enabled",
        # "encrypted": "Encrypted",
        "snapshot_id": "Snapshot ID",
        # "parameter_groups": "Parameter Groups",
        "has_critical_subscription": "Has Critical Subscription",
        "port": "Port"
        # "public_snapshot": "Public Snapshot",
        # "in_vpc": "In VPC",
        # "logs_exported": "Logs Exported",
        # "logging_enabled": "Logging Enabled",
        # "audit_logging_enabled": "Audit Logging Enabled",
        # "backups_enabled": "Backups Enabled",
        # "copy_tags_enabled": "Copy Tags Enabled",
        # "monitoring_interval": "Monitoring Interval",
        # "backtrack_window": "Backtrack Window",
        # "backtracking_enabled": "Backtracking Enabled"
    },

    # Lambda Service
    "lambda": {
        "function_name": "Function Name",
        "function_arn": "Function ARN",
        "runtime": "Runtime",
        "is_supported_runtime": "Is Supported Runtime",
        "has_public_access": "Has Public Access",
        "has_resource_policy": "Has Resource Policy",
        "vpc_config": "VPC Config",
        "tracing_enabled": "Tracing Enabled",
        "environment_variables": "Environment Variables",
        "has_secrets_in_env": "Has Secrets in Env",
        "code_signing_enabled": "Code Signing Enabled",
        "dlq_configured": "DLQ Configured"
    },

    # ECS Service
    "ecs": {
        "cluster_name": "Cluster Name",
        "cluster_arn": "Cluster ARN",
        "service_name": "Service Name",
        "service_arn": "Service ARN",
        "task_definition_arn": "Task Definition ARN",
        "task_set_arn": "Task Set ARN",
        "platform_version": "Platform Version",
        "is_latest": "Is Latest",
        "container_insights_enabled": "Container Insights Enabled",
        "assign_public_ip": "Assign Public IP",
        "no_public_ip": "No Public IP",
        "pid_mode": "PID Mode",
        "no_host_process_namespace": "No Host Process Namespace",
        "container_name": "Container Name",
        "is_privileged": "Is Privileged",
        "non_privileged": "Non Privileged",
        "read_only_root_filesystem": "Read Only Root Filesystem",
        "has_secrets_in_env": "Has Secrets in Env",
        "logging_configured": "Logging Configured",
        "secure_network_mode": "Secure Network Mode",
        "has_task_role": "Has Task Role",
        "logging_mode": "Logging Mode",
        "log_driver": "Log Driver",
        "task_definition_family": "Task Definition Family",
        "task_definition_revision": "Task Definition Revision",
        "has_logging_config": "Has Logging Config",
        "network_mode": "Network Mode",
        "task_role_arn": "Task Role ARN",
        "environment_variables": "Environment Variables",
        "log_options": "Log Options",
        "readonly_root_filesystem": "Read Only Root Filesystem",
    },

    # EKS Service
    "eks": {
        "cluster_name": "Cluster Name",
        "kubernetes_version": "Kubernetes Version",
        "is_supported_version": "Is Supported Version",
        "endpoint_private_access": "Endpoint Private Access",
        "endpoint_public_access": "Endpoint Public Access",
        "encryption_config": "Encryption Config",
        "has_secrets_encryption": "Has Secrets Encryption",
        "logging_config": "Logging Config",
        "audit_logging_enabled": "Audit Logging Enabled",
        "private_endpoint": "Private Endpoint",
        "network_policy_enabled": "Network Policy Enabled",
        "has_public_ip": "Has Public IP",
        "remote_access_configured": "Remote Access Configured",
        "private_subnets_only": "Private Subnets Only",
        "ami_type": "AMI Type",
        "instance_types": "Instance Types",
        "node_group_name": "Node Group Name",
        "subnet_count": "Subnet Count",
        "task_role_arn": "Task Role ARN"
    },

    # ELB Service
    "elb": {
        "load_balancer_name": "Load Balancer Name",
        "load_balancer_arn": "Load Balancer ARN",
        "type": "Type",
        "scheme": "Scheme",
        "is_internal": "Is Internal",
        "has_http_listener": "Has HTTP Listener",
        "has_https_listener": "Has HTTPS Listener",
        "has_http_to_https_redirect": "Has HTTP to HTTPS Redirect",
        "redirect_to_https": "Redirect to HTTPS",
        "availability_zones": "Availability Zones",
        "az_count": "AZ Count",
        "spans_multiple_azs": "Spans Multiple AZs",
        "desync_mitigation_mode": "Desync Mitigation Mode",
        "drop_invalid_headers_enabled": "Drop Invalid Headers Enabled",
        "deletion_protection_enabled": "Deletion Protection Enabled",
        "access_logs_enabled": "Access Logs Enabled",
        "idle_timeout": "Idle Timeout",
        "logging_enabled":"Logging Enabled",
        "uses_acm_certificate":"Uses ACM Certificate",
        "connection_draining_enabled":"Connection Draining Enabled",
        "uses_strong_policy":"Uses Strong Policy",
        "cross_zone_load_balancing_enabled": "Cross Zone Load Balancing Enabled"
    },

    # ElastiCache Service
    "elasticache": {
        "replication_group_id": "Replication Group ID",
        "cache_cluster_id": "Cache Cluster ID",
        "engine": "Engine",
        "engine_version": "Engine Version",
        "auto_minor_version_upgrade": "Auto Minor Version Upgrade",
        "snapshot_retention_limit": "Snapshot Retention Limit",
        "transit_encryption_enabled": "Transit Encryption Enabled",
        "at_rest_encryption_enabled": "At Rest Encryption Enabled",
        "auth_token_enabled": "Auth Token Enabled",
        "multi_az_enabled": "Multi-AZ Enabled",
        "cluster_id": "Cluster ID",
        "total_subnets": "Total Subnets",
        "public_subnets_count": "Public Subnets Count",
        "private_subnets_only": "Private Subnets Only",
        "multi_az_status": "Multi-AZ Status",
        "automatic_failover": "Automatic Failover",
        "cluster_enabled": "Cluster Enabled",
        "num_cache_clusters": "Number of Cache Clusters",
        "subnet_group_name": "Subnet Group Name",
        "backups_enabled": "Backups Enabled",
        "automatic_failover_status": "Automatic Failover status",
        "non_default_subnet_group": "Non Default Subnet Group"
    },

    # EFS Service
    "efs": {
        "file_system_id": "File System ID",
        "encrypted": "Encrypted",
        "kms_key_id": "KMS Key ID",
        # "is_in_backup_plan": "Included in Backup Plan",
        "backup_policy": "Backup Policy",
        "automatic_backups_enabled": "Automatic Backups Enabled",
        # "is_backup_enabled": "Is Backup Enabled",
        "access_point_id": "Access Point ID",
        "enforces_root_directory": "Enforces Root Directory",
        "is_root_directory_enforced": "Is Root Directory Enforced",
        "root_directory": "Root Directory",
        "enforces_user_identity": "Enforces User Identity",
        "is_user_identity_enforced": "Is User Identity Enforced",
        "posix_user": "POSIX User",
        "mount_target_id": "Mount Target ID",
        "subnet_id": "Subnet ID",
        # "is_public_subnet": "Is Public Subnet",
        # "is_multi_az": "Is Multi-AZ",
        "throughput_mode": "Throughput Mode",
        "file_system_name": "File System Name",
        "mount_targets_count": "Mount Targets Count",
        "availability_zones_count": "Availability Zones Count",
        # "has_policy": "Has Policy",
        # "has_unrestricted_vpc_access": "Has Unrestricted VPC Access",
        "performance_mode": "Performance Mode"
    },

    # Common keys used across multiple services
    "common": {
        "status": "Status",
        "region": "Region",
        "account_id": "Account ID",
        "resource_id": "Resource ID",
        "resource_name": "Resource Name",
        "resource_type": "Resource Type",
        "created_at": "Created At",
        "updated_at": "Updated At",
        "tags": "Tags",
        "age_in_days": "Age in Days",
        "compliance": "Compliance",
        "create_date": "Create Date",
        "note": "Note",
        "error": "Error",
    }
}

LAMBDA_SUPPORTED_RUNTIMES = [
    # Node.js
    "nodejs22.x", "nodejs20.x", "nodejs18.x",
    # Python
    "python3.13", "python3.12", "python3.11", "python3.10", "python3.9",
    # Java
    "java21", "java17", "java11", "java8.al2", "java8",
    # .NET
    "dotnet8", "dotnet6",
    # Ruby
    "ruby3.3", "ruby3.2",
    # Go
    "go1.x",
    # Custom
    "provided.al2023", "provided.al2", "provided"
]
