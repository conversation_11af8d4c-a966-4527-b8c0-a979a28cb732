import httpx
from fastapi import HTT<PERSON>Exception
from app import app

SLACK_API_URL = "https://slack.com/api/chat.postMessage"


async def send_slack_message(text: str):
    """
    Send a message to Slack channel using Slack Bot Token.

    Args:
        text (str): Message text to send.
    """
    token = app.config.SLACK_BOT_TOKEN
    channel = app.config.SLACK_CHANNEL_ID

    if not token or not channel:
        raise HTTPException(status_code=500, detail="Slack configuration missing")

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json; charset=utf-8",
    }

    payload = {
        "channel": channel,
        "text": text,
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(SLACK_API_URL, headers=headers, json=payload)

    try:
        data = response.json()
    except Exception:
        raise HTTPException(status_code=500, detail="Invalid response from Slack API")

    if not data.get("ok"):
        raise HTTPException(status_code=500, detail=f"Slack API error: {data}")

    return data
