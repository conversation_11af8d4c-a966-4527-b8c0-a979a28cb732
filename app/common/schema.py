import re
from datetime import datetime
from typing import Union
from pydantic import BaseModel, field_validator
from app.common import MIN_PASSWORD_LENGTH, EMAIL_REGEX, CloudProviderNameEnum, AWSRegionNameEnum
from .exception import (
    InvalidEmailException, InvalidPasswordException, InvalidCloudProviderNameException, InvalidAWSRegionException,
    NoAWSRegionException
)


__all__ = ['SignUpLoginSchema', 'RefreshTokenSchema', 'AddAccountSchema', 'LoginResponseSchema',
           'DefaultResponseSchema', 'CloudProviderSchema', 'CloudProvidersResponseSchema', 'GetAccountSchema',
           'GetAccountsResponseSchema', 'GetServicesResponseSchema', 'CreateScanSchema',
           'CreateUserSchema', 'ListPermissionsSchema', 'RolesSchema', 'GetRolesSchema',
           'AssignRevokeRoleSchema', 'UserInfoResponseSchema', 'GetScansResponseSchema', 'ScanDetailResponseSchema',
           'RegionSchema', 'GetRegionsResponseSchema', 'FindingSchema', 'GetFindingsResponseSchema',
           'AccountDetailSchema', 'AccountDetailResponseSchema', 'FindingDetailResponseSchema',
           'TeamMemberSchema', 'TeamMembersResponseSchema', 'RemediateFindingSchema', 'RemediateFindingResponseSchema',
           'UpdateTeamMemberSchema', 'ChangePasswordSchema', 'UpdateUserInfoSchema', 'UpdatePasswordSchema',
           'OTPVerificationSchema', 'ResendOTPSchema', 'OTPResponseSchema']


class SignUpLoginSchema(BaseModel):
    email: str
    password: str
    workspace_name: str = None
    first_name: str = None
    last_name: str = None
    
    @field_validator("email")
    def validate_email(cls, v):
        if v and not re.match(EMAIL_REGEX, v):
            raise InvalidEmailException
        return v

    @field_validator("password")
    def validate_password(cls, v):
        if v and len(v) < MIN_PASSWORD_LENGTH:
            raise InvalidPasswordException
        return v


class RefreshTokenSchema(BaseModel):
    refresh_token: str


class AddAccountSchema(BaseModel):
    cloud_provider_id: int
    account_name: str
    is_organization: bool
    credentials: dict


class LoginResponseSchema(BaseModel):
    ok: bool = True
    status_code: int
    token_type: str
    access_token: str
    refresh_token: str
    message: str = "User logged in successfully"


class UserInfoResponseSchema(BaseModel):
    ok: bool = True
    status_code: int
    data: dict


class DefaultResponseSchema(BaseModel):
    ok: bool = True
    status_code: int
    message: str
    data: dict = None


class CloudProviderSchema(BaseModel):
    id: int
    name: str
    is_enable: bool


class CloudProvidersResponseSchema(BaseModel):
    ok: bool = True
    status_code: int
    data: dict[str, list[CloudProviderSchema]]


class GetAccountSchema(BaseModel):
    id: int
    account_name: str
    account_id: str
    cloud_provider_id: int
    cloud_provider_name: str
    created_at: datetime | None
    last_scan_date: datetime | None
    failed_findings: int
    total_scans: int


class GetAccountsResponseSchema(BaseModel):
    ok: bool = True
    status_code: int
    data: dict[str, list[GetAccountSchema]]


class GetServicesResponseSchema(BaseModel):
    ok: bool = True
    status_code: int

    class ServiceData(BaseModel):
        id: int
        name: str

    data: list[ServiceData]


class CreateScanSchema(BaseModel):
    account_id: int
    cloud_provider_id: int
    cloud_provider_name: str
    services: list[int] | None = None  # Ignored by backend; kept for backward compatibility
    regions: list[str] = []  # Optional - will be discovered automatically if empty

    @field_validator("cloud_provider_name", mode="before")
    def validate_cloud_provider_name(cls, value):
        if value not in CloudProviderNameEnum._value2member_map_:
            raise InvalidCloudProviderNameException
        return value

    @field_validator("regions", mode="before")
    def validate_regions(cls, value):
        """Validate regions are valid (optional - will be discovered if empty)"""
        if not value:
            # Regions will be discovered automatically if empty
            return []
        for region in value:
            if region not in AWSRegionNameEnum._value2member_map_:
                raise InvalidAWSRegionException
        return value


class CreateUserSchema(BaseModel):
    first_name: str = None
    last_name: str = None
    email: str
    password: str
    role_id: int
    is_custom_role: bool = False


class ListPermissionsSchema(BaseModel):
    ok: bool = True
    status_code: int
    data: list[dict[str, Union[int, str]]]


class RolesSchema(BaseModel):
    id: int = None
    name: str
    permissions: list[dict[str, Union[int, str]]]


class GetRolesSchema(BaseModel):
    ok: bool = True
    status_code: int
    data: dict[str, list[RolesSchema]]


class AssignRevokeRoleSchema(BaseModel):
    user_id: int
    role_id: int
    is_custom_role: bool = False


class PaginationSchema(BaseModel):
    total: int
    page: int
    page_size: int
    total_pages: int


class ScanServiceSchema(BaseModel):
    id: int
    service_id: int
    service_name: str
    status: str
    last_scanned_at: datetime | None
    findings_count: int = 0
    failed_findings: int = 0
    passed_findings: int = 0
    remediated_findings: int = 0


class ScanDetailSchema(BaseModel):
    id: int
    account_id: int
    account_name: str
    cloud_provider: str
    scan_start: datetime
    scan_end: datetime | None
    status: str
    total_services: int = 0
    completed_services: int = 0
    findings_count: int = 0
    failed_findings: int = 0
    passed_findings: int = 0
    remediated_findings: int = 0
    services: list[ScanServiceSchema] = []


class ScanDetailResponseSchema(BaseModel):
    ok: bool = True
    status_code: int
    data: ScanDetailSchema = None


class GetScansResponseSchema(BaseModel):
    ok: bool = True
    status_code: int
    data: list
    pagination: PaginationSchema = None


class RegionSchema(BaseModel):
    id: str
    name: str
    description: str = None


class GetRegionsResponseSchema(BaseModel):
    ok: bool = True
    status_code: int
    data: dict = {"regions": []}


class FindingSchema(BaseModel):
    id: int
    scan_id: int
    service_id: int
    service_name: str
    policy_check: str
    severity: str
    description: str
    status: str
    created_at: datetime | None
    account_id: int | None = None
    account_name: str | None = None
    aws_account_id: str | None = None
    cloud_provider_id: int | None = None
    cloud_provider_name: str | None = None


class GetFindingsResponseSchema(BaseModel):
    ok: bool = True
    status_code: int
    pagination: PaginationSchema = {"total": 0, "page": 1, "page_size": 20, "total_pages": 0}
    data: list[FindingSchema] = []


class FindingDetailSchema(BaseModel):
    id: int
    scan_id: int
    service_id: int
    service_name: str
    policy_check: str
    severity: str
    description: str
    status: str
    created_at: datetime | None
    account_id: int | None = None
    account_name: str | None = None
    aws_account_id: str | None = None
    cloud_provider_id: int | None = None
    cloud_provider_name: str | None = None
    details: dict | list | None = {}
    field_labels: dict | None = {}


class FindingDetailResponseSchema(BaseModel):
    ok: bool = True
    status_code: int
    data: FindingDetailSchema


class AccountDetailSchema(BaseModel):
    id: int
    account_name: str
    cloud_provider_id: int
    cloud_provider_name: str
    credential_data: dict
    created_at: datetime
    recent_scans: dict | None= {}


class AccountDetailResponseSchema(BaseModel):
    ok: bool = True
    status_code: int
    data: AccountDetailSchema


class TeamMemberSchema(BaseModel):
    id: int
    email: str
    first_name: str | None
    last_name: str | None
    is_owner: bool
    role_name: str | None
    is_custom_role: bool
    last_login: datetime | None = None
    created_at: datetime | None


class TeamMembersResponseSchema(BaseModel):
    ok: bool = True
    status_code: int
    data: list[TeamMemberSchema]


class RemediateFindingSchema(BaseModel):
    details: dict


class RemediateFindingResponseSchema(BaseModel):
    ok: bool = True
    status_code: int
    message: str


class UpdateTeamMemberSchema(BaseModel):
    user_id: int
    first_name: str = None
    last_name: str = None
    role_id: int = None
    is_custom_role: bool = False


class ChangePasswordSchema(BaseModel):
    user_id: int
    password: str
    confirm_password: str
    
    @field_validator("password")
    def validate_password(cls, v):
        if v and len(v) < MIN_PASSWORD_LENGTH:
            raise InvalidPasswordException
        return v
    
    @field_validator("confirm_password")
    def validate_confirm_password(cls, v, values):
        if "password" in values.data and v != values.data["password"]:
            raise ValueError("Passwords do not match")
        return v


class UpdateUserInfoSchema(BaseModel):
    first_name: str = None
    last_name: str = None
    email: str = None
    
    @field_validator("email")
    def validate_email(cls, v):
        if v and not re.match(EMAIL_REGEX, v):
            raise InvalidEmailException
        return v


class UpdatePasswordSchema(BaseModel):
    current_password: str
    new_password: str
    confirm_password: str

    @field_validator("new_password")
    def validate_new_password(cls, v):
        if v and len(v) < MIN_PASSWORD_LENGTH:
            raise InvalidPasswordException
        return v

    @field_validator("confirm_password")
    def validate_confirm_password(cls, v, values):
        if "new_password" in values.data and v != values.data["new_password"]:
            raise ValueError("Passwords do not match")
        return v


class TransferWorkspaceOwnershipSchema(BaseModel):
    new_owner_user_id: int


# OTP Verification Schemas
class OTPVerificationSchema(BaseModel):
    email: str
    otp_code: str

    @field_validator("email")
    def validate_email(cls, v):
        if v and not re.match(EMAIL_REGEX, v):
            raise InvalidEmailException
        return v

    @field_validator("otp_code")
    def validate_otp_code(cls, v):
        if not v or len(v) != 6 or not v.isdigit():
            raise ValueError("OTP code must be 6 digits")
        return v


class ResendOTPSchema(BaseModel):
    email: str

    @field_validator("email")
    def validate_email(cls, v):
        if v and not re.match(EMAIL_REGEX, v):
            raise InvalidEmailException
        return v


class OTPResponseSchema(BaseModel):
    ok: bool = True
    status_code: int
    message: str
    data: dict = None
