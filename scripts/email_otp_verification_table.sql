-- Email OTP Verification Table Creation Script
-- This script creates the table for storing email OTP verification data

-- Create email_otp_verification table
CREATE TABLE IF NOT EXISTS `email_otp_verification` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `email` VARCHAR(255) NOT NULL,
    `otp_code` VARCHAR(6) NOT NULL,
    `expires_at` TIMESTAMP NOT NULL,
    `is_verified` BOOLEAN DEFAULT FALSE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `signup_data` JSON NULL COMMENT 'Stores the original signup data (first_name, last_name, password_hash, workspace_name)',
    
    -- Indexes for performance
    INDEX `idx_email` (`email`),
    INDEX `idx_otp_code` (`otp_code`),
    INDEX `idx_expires_at` (`expires_at`),
    INDEX `idx_email_otp` (`email`, `otp_code`),
    INDEX `idx_email_verified` (`email`, `is_verified`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add cleanup procedure for expired OTPs
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS CleanupExpiredOTPs()
BEGIN
    DELETE FROM email_otp_verification 
    WHERE expires_at < NOW() 
    OR (is_verified = TRUE AND created_at < DATE_SUB(NOW(), INTERVAL 1 DAY));
END //

DELIMITER ;

-- Verification queries
SELECT 'Email OTP Verification table created successfully' AS status;

-- Show table structure
DESCRIBE email_otp_verification;

-- Show indexes
SHOW INDEX FROM email_otp_verification;
