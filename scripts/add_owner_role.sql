-- =====================================================
-- Add OWNER Role and TRANSFER_WORKSPACE_OWNERSHIP Permission
-- =====================================================
-- This script adds the OWNER role and TRANSFER_WORKSPACE_OWNERSHIP permission
-- to support role-based workspace ownership transfer
-- =====================================================

-- Add TRANSFER_WORKSPACE_OWNERSHIP permission if it doesn't exist
INSERT IGNORE INTO permissions (id, name) 
VALUES (14, 'transfer_workspace_ownership');

-- Add OWNER role if it doesn't exist
INSERT IGNORE INTO roles (id, name) 
VALUES (8, 'Owner');

-- Add all permissions to OWNER role (Owner should have all permissions)
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT 8, p.id FROM permissions p;

-- Assign OWNER role to current workspace owners
-- This query finds users who are workspace owners (created_by = id) and assigns them the OWNER role
INSERT IGNORE INTO user_roles (user_id, role_id)
SELECT u.id, 8
FROM users u 
JOIN workspaces w ON u.workspace_id = w.id 
WHERE w.created_by = u.id;

-- Verify the setup
SELECT 'OWNER role setup verification:' as status;

-- Check if OWNER role exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'OK: OWNER role exists'
        ELSE 'ERROR: OWNER role not found'
    END as owner_role_check
FROM roles 
WHERE id = 8 AND name = 'Owner';

-- Check if TRANSFER_WORKSPACE_OWNERSHIP permission exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'OK: TRANSFER_WORKSPACE_OWNERSHIP permission exists'
        ELSE 'ERROR: TRANSFER_WORKSPACE_OWNERSHIP permission not found'
    END as permission_check
FROM permissions 
WHERE id = 14 AND name = 'transfer_workspace_ownership';

-- Check how many permissions are assigned to OWNER role
SELECT 
    CONCAT('OWNER role has ', COUNT(*), ' permissions assigned') as owner_permissions_count
FROM role_permissions 
WHERE role_id = 8;

-- Check how many users have OWNER role
SELECT 
    CONCAT(COUNT(*), ' users have OWNER role assigned') as owner_users_count
FROM user_roles 
WHERE role_id = 8;

-- Show current workspace owners and their OWNER role status
SELECT 
    w.id as workspace_id,
    w.name as workspace_name,
    u.id as owner_user_id,
    u.email as owner_email,
    CASE 
        WHEN ur.role_id IS NOT NULL THEN 'YES'
        ELSE 'NO'
    END as has_owner_role
FROM workspaces w
JOIN users u ON w.created_by = u.id
LEFT JOIN user_roles ur ON u.id = ur.user_id AND ur.role_id = 8
ORDER BY w.id;
