-- =====================================================
-- Workspace Ownership Transfer Script
-- =====================================================
-- This script transfers workspace ownership from one user to another
-- 
-- USAGE:
-- Replace the following variables before executing:
-- @workspace_id: The ID of the workspace
-- @new_owner_user_id: The ID of the user who will become the new owner
--
-- PREREQUISITES:
-- 1. The new owner must exist and be in the same workspace
-- 2. The new owner must have admin privileges (is_admin = true)
-- 3. The current owner must be different from the new owner
-- =====================================================

-- Set variables (REPLACE THESE VALUES)
SET @workspace_id = 1;  -- Replace with actual workspace ID
SET @new_owner_user_id = 2;  -- Replace with actual new owner user ID

-- =====================================================
-- VALIDATION CHECKS
-- =====================================================

-- Check if workspace exists
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'ERROR: Workspace does not exist'
        ELSE 'OK: Workspace exists'
    END as workspace_check
FROM workspaces 
WHERE id = @workspace_id;

-- Check if new owner exists and is in the correct workspace
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'ERROR: New owner does not exist or is not in the specified workspace'
        ELSE 'OK: New owner exists and is in the correct workspace'
    END as new_owner_check
FROM users 
WHERE id = @new_owner_user_id AND workspace_id = @workspace_id;

-- Check if new owner has admin privileges
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'ERROR: New owner does not have admin privileges'
        ELSE 'OK: New owner has admin privileges'
    END as admin_check
FROM users 
WHERE id = @new_owner_user_id AND is_admin = true;

-- Check if new owner is different from current owner
SELECT 
    CASE 
        WHEN w.created_by = @new_owner_user_id THEN 'ERROR: New owner is already the current owner'
        ELSE 'OK: New owner is different from current owner'
    END as ownership_check
FROM workspaces w
WHERE w.id = @workspace_id;

-- Display current ownership information
SELECT 
    w.id as workspace_id,
    w.name as workspace_name,
    w.created_by as current_owner_id,
    u.email as current_owner_email,
    u.first_name as current_owner_first_name,
    u.last_name as current_owner_last_name
FROM workspaces w
JOIN users u ON w.created_by = u.id
WHERE w.id = @workspace_id;

-- Display new owner information
SELECT 
    u.id as new_owner_id,
    u.email as new_owner_email,
    u.first_name as new_owner_first_name,
    u.last_name as new_owner_last_name,
    u.is_admin as is_admin
FROM users u
WHERE u.id = @new_owner_user_id;

-- =====================================================
-- OWNERSHIP TRANSFER (Uncomment to execute)
-- =====================================================

-- IMPORTANT: Review all validation checks above before uncommenting and executing

-- BEGIN;

-- -- Transfer workspace ownership
-- UPDATE workspaces 
-- SET created_by = @new_owner_user_id 
-- WHERE id = @workspace_id;

-- -- Update new owner's admin status (self-reference)
-- UPDATE users 
-- SET created_by = @new_owner_user_id 
-- WHERE id = @new_owner_user_id;

-- -- Verify the transfer
-- SELECT 
--     'Transfer completed successfully' as status,
--     w.id as workspace_id,
--     w.name as workspace_name,
--     w.created_by as new_owner_id,
--     u.email as new_owner_email,
--     NOW() as transferred_at
-- FROM workspaces w
-- JOIN users u ON w.created_by = u.id
-- WHERE w.id = @workspace_id;

-- COMMIT;

-- =====================================================
-- ROLLBACK INSTRUCTIONS
-- =====================================================
-- If you need to rollback the changes, you can use:
-- 
-- SET @original_owner_user_id = X;  -- Replace X with original owner ID
-- 
-- BEGIN;
-- UPDATE workspaces SET created_by = @original_owner_user_id WHERE id = @workspace_id;
-- UPDATE users SET created_by = @original_owner_user_id WHERE id = @original_owner_user_id;
-- COMMIT;
-- =====================================================
