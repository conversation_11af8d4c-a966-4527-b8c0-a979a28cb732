-- Lambda Checks Migration Script
-- This script adds Lambda checks to the check_list table for the object-based design

-- First, let's find the service_id for Lambda service
-- If Lambda service doesn't exist, you'll need to add it first
-- SELECT id, name FROM services WHERE name = 'lambda';

-- Assuming Lambda service exists, we'll use a variable to store the service_id
-- You may need to replace @lambda_service_id with the actual service_id for Lambda

-- For AWS cloud provider (assuming cloud_provider_id = 1)
SET @aws_cloud_provider_id = 1;

-- Get the Lambda service_id (you may need to update this based on your database)
-- Common service_ids based on the pattern: IAM=4, EC2=2, ECS=10
-- Lambda is likely to be 3, 5, 6, 7, 8, or 9 depending on the order of creation
-- You can find the correct service_id by running: SELECT id, name FROM services WHERE name = 'lambda';

-- For now, let's assume Lambda service_id = 3 (you should verify this)
SET @lambda_service_id = (SELECT id FROM services WHERE name = 'lambda' AND cloud_provider_id = @aws_cloud_provider_id LIMIT 1);

-- Verify that Lambda service exists
SELECT CONCAT('Lambda service_id: ', COALESCE(@lambda_service_id, 'NOT FOUND')) AS verification;

-- Insert Lambda checks into check_list table
INSERT INTO `check_list` 
(check_id, title, script_path, service_id, severity, remediation_guidance, cloud_provider_id, is_active) 
VALUES
-- Lambda Check 1: Prohibit Public Access
('LAMBDA.1', 
 'Lambda function policies should prohibit public access', 
 'lambda_prohibit_public_access', 
 @lambda_service_id, 
 'CRITICAL', 
 'Remove public access from Lambda function resource-based policies. Ensure Principal is not set to "*" or AWS account "*".', 
 @aws_cloud_provider_id,
 TRUE),

-- Lambda Check 2: Supported Runtimes
('LAMBDA.2', 
 'Lambda functions should use supported runtimes', 
 'lambda_supported_runtimes', 
 @lambda_service_id, 
 'MEDIUM', 
 'Update Lambda functions to use supported runtimes. Check AWS documentation for the latest supported runtime versions.', 
 @aws_cloud_provider_id,
 TRUE),

-- Lambda Check 3: Multi-AZ VPC Operation
('LAMBDA.3', 
 'VPC Lambda functions should operate in multiple Availability Zones', 
 'lambda_multi_az_vpc_operation', 
 @lambda_service_id, 
 'MEDIUM', 
 'Configure Lambda functions in VPC to use subnets from multiple Availability Zones for high availability.', 
 @aws_cloud_provider_id,
 TRUE);

-- Verify the insertion
SELECT 
    cl.check_id,
    cl.title,
    cl.script_path,
    cl.severity,
    s.name as service_name,
    cp.name as cloud_provider_name
FROM check_list cl
JOIN services s ON cl.service_id = s.id
JOIN cloud_providers cp ON cl.cloud_provider_id = cp.id
WHERE s.name = 'lambda'
ORDER BY cl.check_id;

-- Display summary
SELECT CONCAT('Successfully added ', COUNT(*), ' Lambda checks') AS summary
FROM check_list cl
JOIN services s ON cl.service_id = s.id
WHERE s.name = 'lambda';
