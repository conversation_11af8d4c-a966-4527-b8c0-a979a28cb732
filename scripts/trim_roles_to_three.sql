-- =====================================================
-- Trim roles to exactly three: Owner (8), Admin (1), Member (9)
-- - Removes deprecated roles and their role_permissions
-- - Creates Member role if missing and assigns minimal permissions
-- - Remaps users who had deprecated roles to Member
-- =====================================================

START TRANSACTION;

-- Ensure required permissions exist
INSERT IGNORE INTO permissions (id, name) VALUES
    (13, 'read_only');

-- Create Member role if it does not exist
INSERT IGNORE INTO roles (id, name) VALUES (9, 'Member');

-- Clean any existing permissions for Member to have a clean slate
DELETE rp FROM role_permissions rp WHERE rp.role_id = 9;

-- Assign minimal permissions to Member
-- Note: Member should NOT be able to invite or manage users
-- Only grant read_only by default. Add more if needed later.
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT 9, p.id FROM permissions p WHERE p.name IN ('read_only');

-- List of roles we KEEP
-- Admin (1), Owner (8), Member (9)

-- Identify deprecated roles (anything not in 1,8,9)
-- Remap users who had deprecated roles to Member (9)
INSERT IGNORE INTO user_roles (user_id, role_id)
SELECT ur.user_id, 9
FROM user_roles ur
JOIN roles r ON r.id = ur.role_id
WHERE ur.role_id NOT IN (1, 8, 9);

-- Remove role_permissions for deprecated roles
DELETE rp FROM role_permissions rp
JOIN roles r ON r.id = rp.role_id
WHERE rp.role_id NOT IN (1, 8, 9);

-- Remove user_roles for deprecated roles (after remapping above)
DELETE ur FROM user_roles ur
JOIN roles r ON r.id = ur.role_id
WHERE ur.role_id NOT IN (1, 8, 9);

-- Finally delete the deprecated roles
DELETE r FROM roles r WHERE r.id NOT IN (1, 8, 9);

COMMIT;

-- Verification queries (read-only)
SELECT 'Roles remaining (should be 1,8,9):' AS info;
SELECT id, name FROM roles ORDER BY id;

SELECT 'Member permissions:' AS info;
SELECT p.name FROM role_permissions rp JOIN permissions p ON p.id = rp.permission_id WHERE rp.role_id = 9 ORDER BY p.name;


