#!/bin/bash

# Email OTP Verification Setup Script
# This script helps set up the email OTP verification system

set -e

echo "🚀 Setting up Email OTP Verification System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
print_status "Checking prerequisites..."

if ! command_exists mysql; then
    print_error "MySQL client not found. Please install MySQL client."
    exit 1
fi

if ! command_exists python3; then
    print_error "Python3 not found. Please install Python3."
    exit 1
fi

print_status "Prerequisites check passed ✓"

# Database setup
print_status "Setting up database..."

read -p "Enter MySQL username: " MYSQL_USER
read -s -p "Enter MySQL password: " MYSQL_PASS
echo
read -p "Enter MySQL database name: " MYSQL_DB
read -p "Enter MySQL host (default: localhost): " MYSQL_HOST
MYSQL_HOST=${MYSQL_HOST:-localhost}

# Test database connection
if mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASS" -e "USE $MYSQL_DB;" 2>/dev/null; then
    print_status "Database connection successful ✓"
else
    print_error "Failed to connect to database. Please check credentials."
    exit 1
fi

# Run database migration
print_status "Running database migration..."
if mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASS" "$MYSQL_DB" < scripts/email_otp_verification_table.sql; then
    print_status "Database migration completed ✓"
else
    print_error "Database migration failed"
    exit 1
fi

# Environment variables setup
print_status "Setting up environment variables..."

ENV_FILE=".env"
if [ ! -f "$ENV_FILE" ]; then
    touch "$ENV_FILE"
    print_status "Created .env file"
fi

# AWS SES Configuration
echo
print_status "AWS SES Configuration"
read -p "Enter AWS SES Region (default: us-east-1): " AWS_SES_REGION
AWS_SES_REGION=${AWS_SES_REGION:-us-east-1}

read -p "Enter AWS SES Access Key ID: " AWS_SES_ACCESS_KEY_ID
read -s -p "Enter AWS SES Secret Access Key: " AWS_SES_SECRET_ACCESS_KEY
echo

read -p "Enter sender email (default: <EMAIL>): " AWS_SES_SENDER_EMAIL
AWS_SES_SENDER_EMAIL=${AWS_SES_SENDER_EMAIL:-<EMAIL>}

read -p "Enter sender name (default: CloudAudit): " AWS_SES_SENDER_NAME
AWS_SES_SENDER_NAME=${AWS_SES_SENDER_NAME:-CloudAudit}

# OTP Configuration
echo
print_status "OTP Configuration (optional - defaults will be used if not specified)"
read -p "Enter OTP expiry minutes (default: 5): " OTP_EXPIRY_MINUTES
OTP_EXPIRY_MINUTES=${OTP_EXPIRY_MINUTES:-5}

read -p "Enter OTP length (default: 6): " OTP_LENGTH
OTP_LENGTH=${OTP_LENGTH:-6}

read -p "Enter OTP resend cooldown minutes (default: 1): " OTP_RESEND_COOLDOWN_MINUTES
OTP_RESEND_COOLDOWN_MINUTES=${OTP_RESEND_COOLDOWN_MINUTES:-1}

# Write to .env file
print_status "Writing environment variables to .env file..."

# Remove existing OTP-related variables
sed -i '/^AWS_SES_/d' "$ENV_FILE" 2>/dev/null || true
sed -i '/^OTP_/d' "$ENV_FILE" 2>/dev/null || true

# Add new variables
cat >> "$ENV_FILE" << EOF

# AWS SES Configuration for Email OTP
AWS_SES_REGION=$AWS_SES_REGION
AWS_SES_ACCESS_KEY_ID=$AWS_SES_ACCESS_KEY_ID
AWS_SES_SECRET_ACCESS_KEY=$AWS_SES_SECRET_ACCESS_KEY
AWS_SES_SENDER_EMAIL=$AWS_SES_SENDER_EMAIL
AWS_SES_SENDER_NAME=$AWS_SES_SENDER_NAME

# OTP Configuration
OTP_EXPIRY_MINUTES=$OTP_EXPIRY_MINUTES
OTP_LENGTH=$OTP_LENGTH
OTP_RESEND_COOLDOWN_MINUTES=$OTP_RESEND_COOLDOWN_MINUTES
EOF

print_status "Environment variables configured ✓"

# Test AWS SES connection
print_status "Testing AWS SES connection..."
python3 -c "
import boto3
import sys
try:
    client = boto3.client('ses', 
                         region_name='$AWS_SES_REGION',
                         aws_access_key_id='$AWS_SES_ACCESS_KEY_ID',
                         aws_secret_access_key='$AWS_SES_SECRET_ACCESS_KEY')
    response = client.get_send_quota()
    print('✓ AWS SES connection successful')
    print(f'  Send quota: {response[\"Max24HourSend\"]} emails/24h')
    print(f'  Sent in last 24h: {response[\"SentLast24Hours\"]}')
except Exception as e:
    print(f'✗ AWS SES connection failed: {e}')
    sys.exit(1)
" || {
    print_error "AWS SES connection test failed. Please check your credentials."
    exit 1
}

# Service restart instructions
print_status "Setup completed successfully! 🎉"
echo
print_warning "Next steps:"
echo "1. Restart your application services:"
echo "   sudo systemctl restart your-app-service"
echo "   sudo systemctl restart celery-worker"
echo "   sudo systemctl restart celery-beat"
echo
echo "2. Test the new endpoints using the curl commands in:"
echo "   docs/email_otp_verification_testing.md"
echo
echo "3. Monitor logs for any issues:"
echo "   tail -f /var/log/your-app/app.log | grep -i otp"
echo
print_status "Email OTP verification system is ready to use!"

# Create a simple test script
cat > test_otp_endpoints.sh << 'EOF'
#!/bin/bash

# Simple test script for OTP endpoints
# Replace YOUR_SERVER_URL with your actual server URL

SERVER_URL="http://localhost:8000"
TEST_EMAIL="<EMAIL>"

echo "Testing OTP endpoints..."

echo "1. Testing signup..."
curl -X POST "$SERVER_URL/auth/signup" \
  -H "Content-Type: application/json" \
  -d "{
    \"email\": \"$TEST_EMAIL\",
    \"password\": \"TestPassword123\",
    \"workspace_name\": \"Test Workspace\",
    \"first_name\": \"Test\",
    \"last_name\": \"User\"
  }"

echo -e "\n\n2. Check your email for OTP code, then run:"
echo "curl -X POST \"$SERVER_URL/auth/verify-otp\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{"
echo "    \"email\": \"$TEST_EMAIL\","
echo "    \"otp_code\": \"YOUR_OTP_CODE\""
echo "  }'"

echo -e "\n\n3. To resend OTP:"
echo "curl -X POST \"$SERVER_URL/auth/resend-otp\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"email\": \"$TEST_EMAIL\"}'"
EOF

chmod +x test_otp_endpoints.sh
print_status "Created test_otp_endpoints.sh for quick testing"
