-- Lambda Service ID Finder Script
-- Run this script first to find the correct service_id for Lambda

-- Display all services to help identify the Lambda service_id
SELECT 
    id as service_id,
    name as service_name,
    cloud_provider_id,
    is_enable,
    created_at
FROM services 
WHERE cloud_provider_id = 1  -- Assuming AWS cloud_provider_id = 1
ORDER BY id;

-- Specifically look for Lambda service
SELECT 
    id as lambda_service_id,
    name as service_name,
    'Use this service_id in the migration script' as instruction
FROM services 
WHERE name = 'lambda' AND cloud_provider_id = 1;

-- Check if Lambda service exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('Lambda service found with ID: ', MAX(id))
        ELSE 'Lambda service NOT FOUND - you may need to create it first'
    END as status
FROM services 
WHERE name = 'lambda' AND cloud_provider_id = 1;

-- If Lambda service doesn't exist, here's the INSERT statement to create it:
-- INSERT INTO services (name, cloud_provider_id, is_enable) VALUES ('lambda', 1, TRUE);
