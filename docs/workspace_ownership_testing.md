# Workspace Ownership Transfer Testing Guide

## Overview
This guide provides comprehensive testing instructions for the workspace ownership transfer functionality, including both direct API calls and role-based ownership transfer.

## Prerequisites
1. FastAPI server running on `http://localhost:8000`
2. Valid authentication token
3. At least 2 users in the same workspace
4. Current user must be workspace owner
5. Target user must have admin privileges

## Setup Database (Run Once)
```bash
# Execute the OWNER role setup script
mysql -u your_username -p your_database < scripts/add_owner_role.sql
```

## Authentication
First, get your authentication token:

```bash
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your_password"
  }'
```

Save the `access_token` from the response for use in subsequent requests.

## Method 1: Direct Ownership Transfer API

### Test Direct Transfer
```bash
curl -X PUT "http://localhost:8000/api/admin/transfer-workspace-ownership" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "new_owner_user_id": 123
  }'
```

**Expected Response:**
```json
{
  "ok": true,
  "status_code": 200,
  "message": "Workspace ownership transferred successfully"
}
```

## Method 2: Role-Based Ownership Transfer

### Step 1: List Available Roles
```bash
curl -X GET "http://localhost:8000/api/roles" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

Look for the "Owner" role (should have `id: 8`).

### Step 2: List Team Members
```bash
curl -X GET "http://localhost:8000/api/users/team-members" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

This shows current team members and their roles. Look for:
- `is_owner: true/false`
- `role_name: "Owner"` for current owner

### Step 3: Transfer Ownership via Role Assignment
```bash
curl -X POST "http://localhost:8000/api/roles/assign" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "user_id": 123,
    "role_id": 8,
    "is_custom_role": false
  }'
```

**Expected Response:**
```json
{
  "ok": true,
  "status_code": 200,
  "message": "Role assigned successfully"
}
```

## Verification Tests

### Verify Ownership Transfer
```bash
curl -X GET "http://localhost:8000/api/users/team-members" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

Check that:
1. New owner has `is_owner: true` and `role_name: "Owner"`
2. Previous owner has `is_owner: false` and different role

### Test New Owner Permissions
Login as the new owner and try to transfer ownership back:

```bash
# Login as new owner
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "their_password"
  }'

# Try to transfer ownership (should work)
curl -X PUT "http://localhost:8000/api/admin/transfer-workspace-ownership" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer NEW_OWNER_TOKEN" \
  -d '{
    "new_owner_user_id": 456
  }'
```

## Error Testing

### Test 1: Non-Owner Trying to Transfer
```bash
# Login as non-owner user
curl -X PUT "http://localhost:8000/api/admin/transfer-workspace-ownership" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer NON_OWNER_TOKEN" \
  -d '{
    "new_owner_user_id": 123
  }'
```

**Expected Error:**
```json
{
  "ok": false,
  "error": "FORBIDDEN",
  "message": "Only workspace owner can transfer ownership",
  "status": 403
}
```

### Test 2: Transfer to Self
```bash
curl -X PUT "http://localhost:8000/api/admin/transfer-workspace-ownership" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "new_owner_user_id": YOUR_USER_ID
  }'
```

**Expected Error:**
```json
{
  "ok": false,
  "error": "BAD_REQUEST",
  "message": "Cannot transfer workspace ownership to yourself",
  "status": 400
}
```

### Test 3: Transfer to Non-Admin User
```bash
curl -X PUT "http://localhost:8000/api/admin/transfer-workspace-ownership" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "new_owner_user_id": NON_ADMIN_USER_ID
  }'
```

**Expected Error:**
```json
{
  "ok": false,
  "error": "BAD_REQUEST",
  "message": "Target user must have admin privileges to become workspace owner",
  "status": 400
}
```

### Test 4: Try to Revoke Owner Role
```bash
curl -X POST "http://localhost:8000/api/roles/revoke" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "user_id": OWNER_USER_ID,
    "role_id": 8,
    "is_custom_role": false
  }'
```

**Expected Error:**
```json
{
  "ok": false,
  "error": "BAD_REQUEST",
  "message": "Bad Request",
  "status": 400
}
```

## Manual SQL Verification

### Check Current Workspace Owner
```sql
SELECT 
    w.id as workspace_id,
    w.name as workspace_name,
    w.created_by as owner_user_id,
    u.email as owner_email
FROM workspaces w
JOIN users u ON w.created_by = u.id
WHERE w.id = YOUR_WORKSPACE_ID;
```

### Check User Roles
```sql
SELECT 
    u.email,
    r.name as role_name,
    ur.role_id
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
WHERE u.workspace_id = YOUR_WORKSPACE_ID
ORDER BY u.email;
```

## Troubleshooting

### Issue: "Target user must have admin privileges"
**Solution:** Ensure target user has admin role assigned:
```bash
curl -X POST "http://localhost:8000/api/roles/assign" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "user_id": TARGET_USER_ID,
    "role_id": 1,
    "is_custom_role": false
  }'
```

### Issue: "Only workspace owner can transfer ownership"
**Solution:** Verify you're logged in as the current workspace owner by checking team members list.

### Issue: OWNER role not found
**Solution:** Run the database setup script: `scripts/add_owner_role.sql`

## Success Indicators
✅ Direct API transfer works  
✅ Role-based transfer works  
✅ Team members list shows correct ownership  
✅ Previous owner loses ownership  
✅ New owner gains all owner permissions  
✅ Error cases return appropriate messages  
✅ Database reflects ownership changes
